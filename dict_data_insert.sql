-- 服务任务状态字典数据插入脚本

-- 1. 首先插入字典类型（如果不存在）
INSERT INTO system_dict_type (id, name, type, status, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, '服务任务状态', 'service_plan_task_status', 0, '服务计划任务的状态字典', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 2. 获取字典类型ID（假设为刚插入的ID，实际使用时需要查询获取）
-- SELECT id FROM system_dict_type WHERE type = 'service_plan_task_status';

-- 3. 插入字典数据项
INSERT INTO system_dict_data (id, sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, 1, '待分配', '1', 'service_plan_task_status', 0, 'info', '', '任务已创建，等待分配执行人', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 2, '进行中', '2', 'service_plan_task_status', 0, 'primary', '', '任务正在执行中', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 3, '已完成', '3', 'service_plan_task_status', 0, 'success', '', '任务已完成', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 4, '已取消', '4', 'service_plan_task_status', 0, 'danger', '', '任务已取消', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 5, '已暂停', '5', 'service_plan_task_status', 0, 'warning', '', '任务已暂停', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
label = VALUES(label),
sort = VALUES(sort),
color_type = VALUES(color_type),
remark = VALUES(remark);

-- 4. 同样为其他字典类型添加数据

-- 服务任务是否周期性
INSERT INTO system_dict_type (id, name, type, status, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, '服务任务是否周期性', 'service_plan_task_is_recurring', 0, '服务计划任务是否为周期性任务', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE name = VALUES(name);

INSERT INTO system_dict_data (id, sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, 1, '否', '0', 'service_plan_task_is_recurring', 0, 'info', '', '非周期性任务', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 2, '是', '1', 'service_plan_task_is_recurring', 0, 'success', '', '周期性任务', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
label = VALUES(label),
sort = VALUES(sort),
color_type = VALUES(color_type);

-- 服务任务优先级
INSERT INTO system_dict_type (id, name, type, status, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, '服务任务优先级', 'service_plan_task_priority', 0, '服务计划任务的优先级', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE name = VALUES(name);

INSERT INTO system_dict_data (id, sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted) 
VALUES 
(NULL, 1, '低', '1', 'service_plan_task_priority', 0, 'info', '', '低优先级', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 2, '中', '2', 'service_plan_task_priority', 0, 'primary', '', '中等优先级', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 3, '高', '3', 'service_plan_task_priority', 0, 'warning', '', '高优先级', 'admin', NOW(), 'admin', NOW(), 0),
(NULL, 4, '紧急', '4', 'service_plan_task_priority', 0, 'danger', '', '紧急优先级', 'admin', NOW(), 'admin', NOW(), 0)
ON DUPLICATE KEY UPDATE 
label = VALUES(label),
sort = VALUES(sort),
color_type = VALUES(color_type);
