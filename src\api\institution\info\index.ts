import request from '@/config/axios'

// 机构信息 VO
export interface InfoVO {
  id: number // 主键ID
  name: string // 机构名称
  shortName: string // 机构简称
  logo: string // 机构logo路径
  type: string // 机构类型（字典值）
  level: string // 机构等级（字典值）
  establishDate: string // 成立时间（YYYY-MM-DD格式字符串）
  businessLicense: string // 营业执照号码
  businessLicenseImg: string // 营业执照图片路径
  legalPerson: string // 法人代表
  legalPersonContact: string // 法人联系方式
  bedCount: number // 床位数量
  areaSize: number // 占地面积(平方米)
  contactPhone: string // 联系电话
  emergencyPhone: string // 紧急联系电话
  email: string // 电子邮箱
  website: string // 官方网站
  address: string // 详细地址
  longitude: number // 经度
  latitude: number // 纬度
  introduction: string // 机构简介
  features: string // 机构特色
  culture: string // 机构文化
  honors: string // 机构荣誉
}

// 机构信息 API
export const InfoApi = {
  // 查询机构信息分页
  getInfoPage: async (params: any) => {
    return await request.get({ url: `/institution/info/page`, params })
  },

  // 查询机构信息详情
  getInfo: async (id: number) => {
    return await request.get({ url: `/institution/info/get?id=` + id })
  },

  // 新增机构信息
  createInfo: async (data: InfoVO) => {
    return await request.post({ url: `/institution/info/create`, data })
  },

  // 修改机构信息
  updateInfo: async (data: InfoVO) => {
    return await request.put({ url: `/institution/info/update`, data })
  },

  // 删除机构信息
  deleteInfo: async (id: number) => {
    return await request.delete({ url: `/institution/info/delete?id=` + id })
  },

  // 导出机构信息 Excel
  exportInfo: async (params) => {
    return await request.download({ url: `/institution/info/export-excel`, params })
  }
}