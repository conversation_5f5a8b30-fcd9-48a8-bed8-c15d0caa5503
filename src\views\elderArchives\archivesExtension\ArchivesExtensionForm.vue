<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      class="bg-[#F5F7FA] p-6 rounded-lg"
    >
      <!-- 基本信息 -->
      <div class="bg-white p-8 rounded-lg mb-6">
        <div class="flex items-center mb-6">
          <Icon icon="ep:user" class="mr-2 text-[#409EFF] text-xl" />
          <span class="text-lg font-bold">基本信息</span>
        </div>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="关联的老人" prop="elderId">
              <el-input v-model="formData.elderId" placeholder="请输入关联的老人ID" class="!w-[400px]">
                <template #prefix>
                  <Icon icon="ep:avatar" class="text-[#909399]" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="记录日期" prop="recordDate">
              <el-date-picker
                v-model="formData.recordDate"
                type="date"
                value-format="x"
                placeholder="选择记录日期"
                class="!w-[400px]"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 身体状况 -->
      <div class="bg-white p-8 rounded-lg mb-6">
        <div class="flex items-center mb-6">
          <Icon icon="ep:first-aid-kit" class="mr-2 text-[#409EFF] text-xl" />
          <span class="text-lg font-bold">身体状况</span>
        </div>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="自理能力" prop="selfCareAbility">
              <el-select v-model="formData.selfCareAbility" placeholder="请选择自理能力" class="!w-[400px]">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SELF_CARE_ABILITY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="精神状态" prop="mentalState">
              <el-select v-model="formData.mentalState" placeholder="请选择精神状态" class="!w-[400px]">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_MENTAL_STATE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="躯体疾病" prop="physicalDisease">
          <el-checkbox-group v-model="formData.physicalDisease" class="w-full">
            <el-checkbox
              v-for="dict in getStrDictOptions(DICT_TYPE.PHYSICAL_DISEASE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              class="mb-3 mr-6"
              border
            />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="精神疾病" prop="mentalDisease">
          <el-checkbox-group v-model="formData.mentalDisease" class="w-full">
            <el-checkbox
              v-for="dict in getStrDictOptions(DICT_TYPE.MENTAL_DISEASE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              class="mb-3 mr-6"
              border
            />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="服药情况" prop="medicationStatus">
          <el-input v-model="formData.medicationStatus" type="textarea" placeholder="请输入服药情况" :rows="3" class="!w-[900px]" />
        </el-form-item>
      </div>

      <!-- 历史记录 -->
      <div class="bg-white p-8 rounded-lg mb-6">
        <div class="flex items-center mb-6">
          <Icon icon="ep:time" class="mr-2 text-[#409EFF] text-xl" />
          <span class="text-lg font-bold">历史记录</span>
        </div>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="跌倒史" prop="fallHistory">
              <el-select v-model="formData.fallHistory" placeholder="请选择跌倒史" class="!w-[250px]">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.FALL_HISTORY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="走失史" prop="wanderingHistory">
              <el-select v-model="formData.wanderingHistory" placeholder="请选择走失史" class="!w-[250px]">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.WANDERING_HISTORY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="住院史" prop="hospitalizationHistory">
              <el-select v-model="formData.hospitalizationHistory" placeholder="请选择住院史" class="!w-[250px]">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.HOSPITALIZATION_HISTORY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="其他意外事件" prop="otherAccidents">
          <el-input v-model="formData.otherAccidents" type="textarea" placeholder="请输入其他意外事件" :rows="3" class="!w-[900px]" />
        </el-form-item>
      </div>

      <!-- 记录信息 -->
      <div class="bg-white p-8 rounded-lg mb-6">
        <div class="flex items-center mb-6">
          <Icon icon="ep:document" class="mr-2 text-[#409EFF] text-xl" />
          <span class="text-lg font-bold">记录信息</span>
        </div>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="记录人" prop="recorder">
              <el-input v-model="formData.recorder" placeholder="请输入记录人" class="!w-[400px]">
                <template #prefix>
                  <Icon icon="ep:user" class="text-[#909399]" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" placeholder="请输入备注" class="!w-[400px]">
                <template #prefix>
                  <Icon icon="ep:edit" class="text-[#909399]" />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { ArchivesExtensionApi, ArchivesExtensionVO } from '@/api/elderArchives/archivesExtension'

/** 老人信息扩展 表单 */
defineOptions({ name: 'ArchivesExtensionForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  selfCareAbility: undefined,
  mentalState: undefined,
  physicalDisease: [],
  mentalDisease: [],
  medicationStatus: undefined,
  fallHistory: undefined,
  wanderingHistory: undefined,
  hospitalizationHistory: undefined,
  otherAccidents: undefined,
  recordDate: undefined,
  recorder: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '关联的老人ID不能为空', trigger: 'blur' }],
  selfCareAbility: [{ required: true, message: '自理能力(1-完全自理 2-轻度依赖 3-中度依赖 4-重度依赖)不能为空', trigger: 'change' }],
  mentalState: [{ required: true, message: '精神状态(1-正常 2-轻度认知障碍 3-中重度认知障碍)不能为空', trigger: 'change' }],
  physicalDisease: [{ required: true, message: '躯体疾病(多选,以逗号分隔:1-高血压 2-心脏病 3-糖尿病 4-慢性支气管炎 5-恶性肿瘤 6-骨折 7-关节炎 8-其他慢性病)不能为空', trigger: 'blur' }],
  mentalDisease: [{ required: true, message: '精神疾病(多选,以逗号分隔:1-焦虑 2-抑郁 3-失眠 4-认知障碍 5-其他)不能为空', trigger: 'blur' }],
  medicationStatus: [{ required: true, message: '服药情况不能为空', trigger: 'blur' }],
  fallHistory: [{ required: true, message: '跌倒史(0-无 1-1次 2-2次 3-3次及以上)不能为空', trigger: 'change' }],
  wanderingHistory: [{ required: true, message: '走失史(0-无 1-1次 2-2次 3-3次及以上)不能为空', trigger: 'change' }],
  hospitalizationHistory: [{ required: true, message: '住院史(0-无 1-1次 2-2次 3-3次及以上)不能为空', trigger: 'change' }],
  otherAccidents: [{ required: true, message: '其他意外事件不能为空', trigger: 'blur' }],
  recordDate: [{ required: true, message: '记录日期不能为空', trigger: 'blur' }],
  recorder: [{ required: true, message: '记录人不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ArchivesExtensionApi.getArchivesExtension(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ArchivesExtensionVO
    if (formType.value === 'create') {
      await ArchivesExtensionApi.createArchivesExtension(data)
      message.success(t('common.createSuccess'))
    } else {
      await ArchivesExtensionApi.updateArchivesExtension(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    selfCareAbility: undefined,
    mentalState: undefined,
    physicalDisease: [],
    mentalDisease: [],
    medicationStatus: undefined,
    fallHistory: undefined,
    wanderingHistory: undefined,
    hospitalizationHistory: undefined,
    otherAccidents: undefined,
    recordDate: undefined,
    recorder: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
