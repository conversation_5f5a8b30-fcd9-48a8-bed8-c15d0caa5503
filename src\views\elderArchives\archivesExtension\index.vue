<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="自理能力" prop="selfCareAbility">
        <el-select
          v-model="queryParams.selfCareAbility"
          placeholder="请选择自理能力"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SELF_CARE_ABILITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="精神状态" prop="mentalState">
        <el-select
          v-model="queryParams.mentalState"
          placeholder="请选择精神状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_MENTAL_STATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="记录人" prop="recorder">
        <el-input
          v-model="queryParams.recorder"
          placeholder="请输入记录人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:archives-extension:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:archives-extension:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="老人姓名" align="center" prop="elderName">
        <template #default="scope">
          <span>{{ scope.row.elderName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="自理能力" align="center" prop="selfCareAbility">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_SELF_CARE_ABILITY" :value="scope.row.selfCareAbility" />
        </template>
      </el-table-column>
      <el-table-column label="精神状态" align="center" prop="mentalState">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_MENTAL_STATE" :value="scope.row.mentalState" />
        </template>
      </el-table-column>
      <el-table-column label="躯体疾病" align="center" prop="physicalDisease">
        <template #default="scope">
          <el-popover
            placement="top"
            trigger="hover"
            :width="300"
          >
            <template #reference>
              <div class="disease-tags">
                <el-tag 
                  v-if="scope.row.physicalDisease" 
                  size="small" 
                  type="info"
                >
                  {{ formatMultiSelect(scope.row.physicalDisease, DICT_TYPE.PHYSICAL_DISEASE) }}
                </el-tag>
                <span v-else>-</span>
              </div>
            </template>
            <div v-if="scope.row.physicalDisease">
              <div v-for="(item, index) in getMultiSelectLabels(scope.row.physicalDisease, DICT_TYPE.PHYSICAL_DISEASE)" :key="index" class="disease-item">
                <el-tag size="small" :type="getTagType(index)">{{ item }}</el-tag>
              </div>
            </div>
            <div v-else>无记录</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="精神疾病" align="center" prop="mentalDisease">
        <template #default="scope">
          <el-popover
            placement="top"
            trigger="hover"
            :width="300"
          >
            <template #reference>
              <div class="disease-tags">
                <el-tag 
                  v-if="scope.row.mentalDisease" 
                  size="small" 
                  type="info"
                >
                  {{ formatMultiSelect(scope.row.mentalDisease, DICT_TYPE.MENTAL_DISEASE) }}
                </el-tag>
                <span v-else>-</span>
              </div>
            </template>
            <div v-if="scope.row.mentalDisease">
              <div v-for="(item, index) in getMultiSelectLabels(scope.row.mentalDisease, DICT_TYPE.MENTAL_DISEASE)" :key="index" class="disease-item">
                <el-tag size="small" :type="getTagType(index)">{{ item }}</el-tag>
              </div>
            </div>
            <div v-else>无记录</div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="记录人" align="center" prop="recorder" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:archives-extension:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:archives-extension:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ArchivesExtensionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ArchivesExtensionApi, ArchivesExtensionVO } from '@/api/elderArchives/archivesExtension'
import ArchivesExtensionForm from './ArchivesExtensionForm.vue'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { useRouter } from 'vue-router'

/** 老人信息扩展 列表 */
defineOptions({ name: 'ArchivesExtension' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<ArchivesExtensionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([]) // 老人基础信息列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  selfCareAbility: undefined,
  mentalState: undefined,
  physicalDisease: undefined,
  mentalDisease: undefined,
  medicationStatus: undefined,
  fallHistory: undefined,
  wanderingHistory: undefined,
  hospitalizationHistory: undefined,
  otherAccidents: undefined,
  recordDate: [],
  recorder: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ArchivesExtensionApi.getArchivesExtensionPage(queryParams)
    // 处理老人姓名显示
    list.value = data.list.map(item => {
      return {
        ...item,
        elderName: getElderName(item.elderId)
      }
    })
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ArchivesExtensionApi.deleteArchivesExtension(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ArchivesExtensionApi.exportArchivesExtension(queryParams)
    // 直接下载Excel
    download.excel(data, '老人信息扩展.xls')
  } catch (error) {
    console.error('导出失败', error)
  } finally {
    exportLoading.value = false
  }
}

/** 获取老人姓名 */
const getElderName = (elderId: number) => {
  if (!elderId) return '-'
  
  try {
    const elder = elderProfiles.value.find(item => item.id === elderId)
    if (elder && elder.name) {
      return elder.name
    } else {
      console.warn(`未找到ID为${elderId}的老人信息`)
      return `未知(ID:${elderId})`
    }
  } catch (error) {
    console.error('获取老人姓名失败', error)
    return `未知(ID:${elderId})`
  }
}

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 获取多选标签值对应的文本列表 */
const getMultiSelectLabels = (values: string, dictType: string) => {
  if (!values) return []
  const valueList = values.split(',')
  const dictOptions = getIntDictOptions(dictType)
  return valueList.map(value => {
    const option = dictOptions.find(opt => opt.value.toString() === value)
    return option ? option.label : value
  })
}

/** 格式化多选显示，显示数量或摘要 */
const formatMultiSelect = (values: string, dictType: string) => {
  if (!values) return '-'
  const labels = getMultiSelectLabels(values, dictType)
  if (labels.length === 0) return '-'
  if (labels.length === 1) return labels[0]
  return `${labels[0]} +${labels.length - 1}`
}

/** 根据索引获取标签类型，循环使用不同颜色 */
const getTagType = (index: number): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const types: ('success' | 'warning' | 'info' | 'primary' | 'danger')[] = ['success', 'warning', 'danger', 'info', 'primary']
  return types[index % types.length]
}

/** 查看详情按钮操作 */
const handleView = (row: ArchivesExtensionVO) => {
  // 跳转到老人信息详情页面
  router.push({
    path: '/elders/archivesInfo/detail',
    query: {
      elder_id: row.elderId
    }
  })
}

/** 初始化 **/
onMounted(async () => {
  try {
    await getElderProfiles() // 先获取老人基础信息
    await getList() // 再获取列表数据
  } catch (error) {
    console.error('初始化数据失败', error)
  }
})
</script>

<style scoped>
.disease-tags {
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.disease-item {
  margin-bottom: 5px;
}

.el-tag + .el-tag {
  margin-left: 4px;
}
</style>
