<template>
  <el-dialog
    v-model="dialogVisible"
    title="老人档案详情"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-tabs v-model="activeTab">
      <!-- 基本信息 Tab -->
      <el-tab-pane label="基本信息" name="basic">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:user" class="mr-5px" />
            基本信息
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ detailData.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="detailData.gender" />
          </el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ detailData.idNumber }}</el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ formatDate(detailData.birthDate) }}</el-descriptions-item>
          <el-descriptions-item label="身高(cm)">{{ detailData.height || '-' }}</el-descriptions-item>
          <el-descriptions-item label="体重(kg)">{{ detailData.weight || '-' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="民族">
            <dict-tag :type="DICT_TYPE.ETHNIC_GROUP" :value="detailData.ethnicGroup" />
          </el-descriptions-item>
          <el-descriptions-item label="宗教信仰">
            <dict-tag :type="DICT_TYPE.RELIGIOUS_BELIEF" :value="detailData.religiousBelief" />
          </el-descriptions-item>
          <el-descriptions-item label="服务等级">
            <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="detailData.serviceLevel" />
          </el-descriptions-item>
          <el-descriptions-item label="健康等级">
            <span v-if="detailData.healthLevel">{{ getHealthLevelText(detailData.healthLevel) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="档案状态">
            <dict-tag :type="DICT_TYPE.ARCHIVE_STATUS" :value="detailData.archiveStatus" />
          </el-descriptions-item>
          <el-descriptions-item label="文化程度">
            <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="detailData.educationLevel" />
          </el-descriptions-item>
          <el-descriptions-item label="婚姻状况">
            <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="detailData.maritalStatus" />
          </el-descriptions-item>
          <el-descriptions-item label="户籍类型">
            <dict-tag :type="DICT_TYPE.HOUSEHOLD_TYPE" :value="detailData.householdType" />
          </el-descriptions-item>
          <el-descriptions-item label="居住类型">
            <template v-if="detailData.residenceType && typeof detailData.residenceType === 'string'">
              <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="parseInt(detailData.residenceType)" />
            </template>
            <template v-else-if="detailData.residenceType && Array.isArray(detailData.residenceType)">
              <dict-tag v-for="item in detailData.residenceType"
                       :key="item"
                       :type="DICT_TYPE.RESIDENCE_TYPE"
                       :value="item"
                       class="mr-1" />
            </template>
            <template v-else-if="detailData.residenceType && typeof detailData.residenceType === 'number'">
              <dict-tag :type="DICT_TYPE.RESIDENCE_TYPE" :value="detailData.residenceType" />
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="医疗保险" :span="2">
            <template v-if="detailData.medicalInsurance">
              <dict-tag v-for="item in detailData.medicalInsurance.split(',')"
                       :key="item"
                       :type="DICT_TYPE.MEDICAL_INSURANCE"
                       :value="parseInt(item)" />
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="职业类型">
            <dict-tag :type="DICT_TYPE.OCCUPATION_TYPE" :value="detailData.occupationType" />
          </el-descriptions-item>
          <el-descriptions-item label="月收入(元)">
            <span v-if="detailData.monthlyIncome">{{ formatMoney(detailData.monthlyIncome) }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="经济来源" :span="2">
            <template v-if="detailData.incomeSource">
              <dict-tag v-for="item in detailData.incomeSource.split(',')"
                       :key="item"
                       :type="DICT_TYPE.INCOME_SOURCE"
                       :value="parseInt(item)"
                       class="mr-1" />
            </template>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>

      <!-- 扩展信息 Tab -->
      <el-tab-pane label="扩展信息" name="extension">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:document" class="mr-5px" />
            扩展信息
          </div>
        </template>
        <div class="extension-content">
          <!-- 老人扩展信息 -->
          <el-card class="mb-4" v-if="extensionData.id">
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:user" class="mr-2" />
                <span class="font-medium">老人扩展信息</span>
              </div>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="自理能力">
                <dict-tag :type="DICT_TYPE.ELDER_SELF_CARE_ABILITY" :value="extensionData.selfCareAbility" />
              </el-descriptions-item>
              <el-descriptions-item label="精神状态">
                <dict-tag :type="DICT_TYPE.ELDER_MENTAL_STATE" :value="extensionData.mentalState" />
              </el-descriptions-item>
              <el-descriptions-item label="躯体疾病">
                <template v-if="extensionData.physicalDisease">
                  <dict-tag v-for="item in extensionData.physicalDisease.split(',')"
                            :key="item"
                            :type="DICT_TYPE.PHYSICAL_DISEASE"
                            :value="Number(item)" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="精神疾病">
                <template v-if="extensionData.mentalDisease">
                  <dict-tag v-for="item in extensionData.mentalDisease.split(',')"
                            :key="item"
                            :type="DICT_TYPE.MENTAL_DISEASE"
                            :value="Number(item)" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="服药情况">{{ extensionData.medicationStatus }}</el-descriptions-item>
              <el-descriptions-item label="跌倒史">
                <dict-tag :type="DICT_TYPE.FALL_HISTORY" :value="extensionData.fallHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="走失史">
                <dict-tag :type="DICT_TYPE.WANDERING_HISTORY" :value="extensionData.wanderingHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="住院史">
                <dict-tag :type="DICT_TYPE.HOSPITALIZATION_HISTORY" :value="extensionData.hospitalizationHistory" />
              </el-descriptions-item>
              <el-descriptions-item label="其他意外事件">{{ extensionData.otherAccidents }}</el-descriptions-item>
              <el-descriptions-item label="记录日期">{{ formatDate(extensionData.recordDate) }}</el-descriptions-item>
              <el-descriptions-item label="记录人">{{ extensionData.recorder }}</el-descriptions-item>
              <el-descriptions-item label="备注">{{ extensionData.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 信息提供者信息 -->
          <el-card class="mb-4">
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:user-filled" class="mr-2" />
                <span class="font-medium">信息提供者信息</span>
              </div>
            </template>
            <div v-if="informationProviders.length > 0">
              <el-table :data="informationProviders" border stripe size="small">
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="gender" label="性别" width="80">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender" />
                  </template>
                </el-table-column>
                <el-table-column prop="relationship" label="与老人的关系" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.ELDER_CONTACT_RELATIONSHIP" :value="scope.row.relationship" />
                  </template>
                </el-table-column>
                <el-table-column prop="isEscort" label="是否带领入住" width="120">
                  <template #default="scope">
                    <el-tag :type="scope.row.isEscort ? 'success' : 'info'" size="small">
                      {{ scope.row.isEscort ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="phone" label="手机号" width="140" />
                <el-table-column prop="idCard" label="身份证号" width="180" />
              </el-table>
            </div>
            <el-empty v-else description="暂无信息提供者信息" />
          </el-card>

          <!-- 紧急联系人信息 -->
          <el-card>
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:phone" class="mr-2" />
                <span class="font-medium">紧急联系人信息</span>
              </div>
            </template>
            <div v-if="emergencyContacts.length > 0">
              <el-table :data="emergencyContacts" border stripe size="small">
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="gender" label="性别" width="80">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender" />
                  </template>
                </el-table-column>
                <el-table-column prop="relationship" label="与老人的关系" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.ELDER_CONTACT_RELATIONSHIP" :value="scope.row.relationship" />
                  </template>
                </el-table-column>
                <el-table-column prop="phone" label="手机号" width="140" />
                <el-table-column prop="idCard" label="身份证号" width="180" />
                <el-table-column prop="emergencyPriority" label="优先级" width="100">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.ELDER_CONTACT_EMERGENCY_PRIORITY" :value="scope.row.emergencyPriority" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无紧急联系人信息" />
          </el-card>

          <!-- 如果没有扩展信息，显示提示 -->
          <el-empty v-if="!extensionData.id && informationProviders.length === 0 && emergencyContacts.length === 0" description="暂无扩展信息" />
        </div>
      </el-tab-pane>

      <!-- 用药信息 Tab -->
      <el-tab-pane label="用药信息" name="medication">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:medicine-box" class="mr-5px" />
            用药信息
          </div>
        </template>
        <div class="medication-content">
          <!-- 用药记录部分 -->
          <el-card class="mb-4">
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:document" class="mr-2" />
                <span class="font-medium">用药记录</span>
              </div>
            </template>
            <div v-if="medicationRecords.length > 0">
              <el-table :data="medicationRecords" border stripe size="small">
                <el-table-column prop="recordDate" label="记录日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.recordDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="recorderName" label="记录人员" width="100" />
                <el-table-column prop="isLongTerm" label="是否长期用药" width="120">
                  <template #default="scope">
                    <el-tag :type="scope.row.isLongTerm ? 'success' : 'info'" size="small">
                      {{ scope.row.isLongTerm ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="startDate" label="开始日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.startDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="endDate" label="结束日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.endDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="medicationStatus" label="用药状态" width="100">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.ELDER_MEDICATION_RECORD_MEDICATION_STATUS" :value="scope.row.medicationStatus" />
                  </template>
                </el-table-column>
                <el-table-column prop="prescriptionDoctor" label="处方医生" width="120" />
                <el-table-column prop="prescriptionHospital" label="处方医院" width="150" />
                <el-table-column prop="remark" label="备注" min-width="150" />
              </el-table>
            </div>
            <el-empty v-else description="暂无用药记录" />
          </el-card>

          <!-- 用药详情部分 -->
          <el-card>
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:medicine-box" class="mr-2" />
                <span class="font-medium">用药详情</span>
              </div>
            </template>
            <div v-if="medicationDetails.length > 0">
              <el-table :data="medicationDetails" border stripe size="small">
                <el-table-column prop="medicineName" label="药物名称" width="150" />
                <el-table-column prop="usageMethod" label="服用方法" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.USAGE_METHOD" :value="scope.row.usageMethod" />
                  </template>
                </el-table-column>
                <el-table-column prop="dosage" label="用药剂量" width="120" />
                <el-table-column prop="frequency" label="用药频率" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.MEDICATION_FREQUENCY" :value="scope.row.frequency" />
                  </template>
                </el-table-column>
                <el-table-column prop="notes" label="用药注意事项" min-width="200" />
                <el-table-column prop="createTime" label="创建时间" width="150">
                  <template #default="scope">
                    {{ formatDate(scope.row.createTime) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无用药详情" />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 疾病信息 Tab -->
      <el-tab-pane label="疾病信息" name="disease">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:document-checked" class="mr-5px" />
            疾病信息
          </div>
        </template>
        <div class="disease-content">
          <!-- 疾病诊断部分 -->
          <el-card class="mb-4">
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:document" class="mr-2" />
                <span class="font-medium">疾病诊断</span>
              </div>
            </template>
            <div v-if="diseaseRecords.length > 0">
              <el-table :data="diseaseRecords" border stripe size="small">
                <el-table-column prop="diagnosisDate" label="诊断日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.diagnosisDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="doctorName" label="诊断医生" width="120" />
                <el-table-column prop="hospitalName" label="诊断机构" width="150" />
                <el-table-column prop="diagnosisType" label="诊断类型" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="scope.row.diagnosisType" />
                  </template>
                </el-table-column>
                <el-table-column prop="diagnosisResult" label="诊断结果概述" min-width="200" />
                <el-table-column prop="treatmentPlan" label="治疗方案" min-width="200" />
                <el-table-column prop="remark" label="备注" min-width="150" />
              </el-table>
            </div>
            <el-empty v-else description="暂无疾病诊断记录" />
          </el-card>

          <!-- 疾病诊断详情部分 -->
          <el-card>
            <template #header>
              <div class="flex items-center">
                <Icon icon="ep:document-checked" class="mr-2" />
                <span class="font-medium">疾病诊断详情</span>
              </div>
            </template>
            <div v-if="diseaseDetails.length > 0">
              <el-table :data="diseaseDetails" border stripe size="small">
                <el-table-column prop="diseaseCode" label="疾病编码(ICD-10)" width="150" />
                <el-table-column prop="diseaseName" label="疾病名称" width="200" />
                <el-table-column prop="isMain" label="是否主要诊断" width="120">
                  <template #default="scope">
                    <el-tag :type="scope.row.isMain ? 'danger' : 'info'" size="small">
                      {{ scope.row.isMain ? '主要诊断' : '次要诊断' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="诊断描述" min-width="300" />
                <el-table-column prop="createTime" label="创建时间" width="150">
                  <template #default="scope">
                    {{ formatDate(scope.row.createTime) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <el-empty v-else description="暂无疾病诊断详情" />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 健康信息 Tab -->
      <el-tab-pane label="健康信息" name="health">
        <template #label>
          <div class="flex items-center">
            <Icon icon="ep:first-aid-kit" class="mr-5px" />
            健康信息
          </div>
        </template>
        <div v-if="healthRecords.length > 0">
          <el-card v-for="(record, index) in healthRecords" :key="record.id" class="mb-4">
            <template #header>
              <div class="flex items-center justify-between">
                <span class="font-medium">健康评估记录 {{ index + 1 }}</span>
                <span class="text-sm text-gray-500">{{ formatArrayDate(record.assessmentDate) }}</span>
              </div>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="压力性损伤评估">
                <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_PRESSURE_INJURY" :value="record.pressureInjury" />
              </el-descriptions-item>
              <el-descriptions-item label="关节活动度评估">
                <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_JOINT_ACTIVITY" :value="record.jointActivity" />
              </el-descriptions-item>
              <el-descriptions-item label="伤口情况">
                <template v-if="record.woundCondition">
                  <dict-tag v-for="item in record.woundCondition.split(',')"
                           :key="item"
                           :type="DICT_TYPE.ELDER_HEALTH_INFO_WOUND_CONDITION"
                           :value="Number(item)"
                           class="mr-1" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="特殊护理情况">
                <template v-if="record.specialCare">
                  <dict-tag v-for="item in record.specialCare.split(',')"
                           :key="item"
                           :type="DICT_TYPE.ELDER_HEALTH_INFO_SPECIAL_CARE"
                           :value="Number(item)"
                           class="mr-1" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="疼痛感">
                <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_PAIN_ASSESSMENT" :value="record.painAssessment" />
              </el-descriptions-item>
              <el-descriptions-item label="牙齿缺失情况">
                <template v-if="record.teethMissing">
                  <dict-tag v-for="item in record.teethMissing.split(',')"
                           :key="item"
                           :type="DICT_TYPE.ELDER_HEALTH_INFO_TEETH_MISSING"
                           :value="Number(item)"
                           class="mr-1" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="义齿佩戴情况">
                <template v-if="record.dentureWearing">
                  <dict-tag v-for="item in record.dentureWearing.split(',')"
                           :key="item"
                           :type="DICT_TYPE.ELDER_HEALTH_INFO_NTURE_WEARING"
                           :value="Number(item)"
                           class="mr-1" />
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="吞咽困难情况">
                <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_SWALLOWING_DIFFICULTY" :value="record.swallowingDifficulty" />
              </el-descriptions-item>
              <el-descriptions-item label="营养状况异常">
                <el-tag :type="record.nutritionStatus ? 'danger' : 'success'" size="small">
                  {{ record.nutritionStatus ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="呼吸道功能异常">
                <el-tag :type="record.respiratoryFunction ? 'danger' : 'success'" size="small">
                  {{ record.respiratoryFunction ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="意识状态异常">
                <el-tag :type="record.consciousnessState ? 'danger' : 'success'" size="small">
                  {{ record.consciousnessState ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="评估人员">{{ record.assessorName }}</el-descriptions-item>
              <el-descriptions-item label="其他特殊情况" :span="2">{{ record.otherConditions || '无' }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ record.remark || '无' }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
        <el-empty v-else description="暂无健康信息" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleViewDetail">查看完整档案</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate, formatArrayDate } from '@/utils/formatTime'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ArchivesExtensionApi } from '@/api/elderArchives/archivesExtension'
import type { ArchivesExtensionVO } from '@/api/elderArchives/archivesExtension'
import { ContactInfoApi } from '@/api/elderArchives/contactinfo'
import type { ContactInfoVO } from '@/api/elderArchives/contactinfo'
import { MedicationRecordApi } from '@/api/elderArchives/medicationrecord'
import type { MedicationRecordVO } from '@/api/elderArchives/medicationrecord'
import { MedicationDetailApi } from '@/api/elderArchives/medicationdetail'
import type { MedicationDetailVO } from '@/api/elderArchives/medicationdetail'
import { DiseaseDiagnosisApi } from '@/api/elderArchives/diseasediagnosis'
import type { DiseaseDiagnosisVO } from '@/api/elderArchives/diseasediagnosis'
import { DiseaseDiagnosisDetailApi } from '@/api/elderArchives/diseasediagnosisdetail'
import type { DiseaseDiagnosisDetailVO } from '@/api/elderArchives/diseasediagnosisdetail'
import { HealthInfoApi } from '@/api/elderArchives/healthinfo'
import type { HealthInfoVO } from '@/api/elderArchives/healthinfo'

const router = useRouter()
const message = useMessage()

const dialogVisible = ref(false)
const activeTab = ref('basic')
const detailLoading = ref(false)
const detailData = ref<any>({})
const extensionData = ref<ArchivesExtensionVO>({} as ArchivesExtensionVO)
const contactList = ref<ContactInfoVO[]>([])
// 信息提供者（带领入住的联系人）
const informationProviders = ref<ContactInfoVO[]>([])
// 紧急联系人
const emergencyContacts = ref<ContactInfoVO[]>([])
// 用药信息
const medicationRecords = ref<MedicationRecordVO[]>([])
const medicationDetails = ref<MedicationDetailVO[]>([])
// 疾病信息
const diseaseRecords = ref<DiseaseDiagnosisVO[]>([])
const diseaseDetails = ref<DiseaseDiagnosisDetailVO[]>([])
// 健康信息
const healthRecords = ref<HealthInfoVO[]>([])

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  activeTab.value = 'basic'
  await loadDetailData(id)
}

/** 加载详情数据 */
const loadDetailData = async (id: number) => {
  detailLoading.value = true
  try {
    // 加载基本信息
    const data = await ArchivesProfileApi.getArchivesProfile(id)
    detailData.value = data

    // 加载扩展信息
    await loadExtensionData(id)

    // 加载联系人信息
    await loadContactData(id)

    // 加载用药信息
    await loadMedicationData(id)

    // 加载疾病信息
    await loadDiseaseData(id)

    // 加载健康信息
    await loadHealthData(id)
  } catch (error: any) {
    message.error('获取详情失败：' + (error.message || '未知错误'))
  } finally {
    detailLoading.value = false
  }
}

/** 加载扩展信息 */
const loadExtensionData = async (elderId: number) => {
  try {
    // 获取最新的一条记录
    const data = await ArchivesExtensionApi.getArchivesExtensionPage({
      elderId,
      pageNo: 1,
      pageSize: 1
    })

    if (data && data.list && data.list.length > 0) {
      const info = data.list[0]
      // 处理数字类型字段
      info.selfCareAbility = Number(info.selfCareAbility) || null
      info.mentalState = Number(info.mentalState) || null
      info.fallHistory = Number(info.fallHistory) || null
      info.wanderingHistory = Number(info.wanderingHistory) || null
      info.hospitalizationHistory = Number(info.hospitalizationHistory) || null

      extensionData.value = info
    } else {
      extensionData.value = {} as ArchivesExtensionVO
    }
  } catch (error: any) {
    console.error('获取扩展信息失败:', error)
  }
}

/** 加载联系人信息 */
const loadContactData = async (elderId: number) => {
  try {
    const data = await ContactInfoApi.getContactInfoPage({
      elderId,
      pageNo: 1,
      pageSize: 100 // 获取所有联系人
    })

    if (data && data.list) {
      contactList.value = data.list

      // 筛选信息提供者（所有非紧急联系人都作为信息提供者显示）
      informationProviders.value = data.list.filter(contact => !contact.isEmergency)

      // 筛选紧急联系人
      emergencyContacts.value = data.list.filter(contact => contact.isEmergency)
        .sort((a, b) => (a.emergencyPriority || 999) - (b.emergencyPriority || 999)) // 按优先级排序
    } else {
      contactList.value = []
      informationProviders.value = []
      emergencyContacts.value = []
    }
  } catch (error: any) {
    console.error('获取联系人信息失败:', error)
  }
}

/** 加载用药信息 */
const loadMedicationData = async (elderId: number) => {
  try {
    // 加载用药记录
    const recordData = await MedicationRecordApi.getMedicationRecordPage({
      elderId,
      pageNo: 1,
      pageSize: 100 // 获取所有用药记录
    })

    if (recordData && recordData.list) {
      medicationRecords.value = recordData.list

      // 加载所有用药记录对应的用药详情
      const allDetails: MedicationDetailVO[] = []
      for (const record of recordData.list) {
        try {
          const detailData = await MedicationDetailApi.getMedicationDetailPage({
            medicationId: record.id,
            pageNo: 1,
            pageSize: 100
          })
          if (detailData && detailData.list) {
            allDetails.push(...detailData.list)
          }
        } catch (error: any) {
          console.error(`获取用药记录${record.id}的详情失败:`, error)
        }
      }
      medicationDetails.value = allDetails
    } else {
      medicationRecords.value = []
      medicationDetails.value = []
    }
  } catch (error: any) {
    console.error('获取用药信息失败:', error)
    medicationRecords.value = []
    medicationDetails.value = []
  }
}

/** 加载疾病信息 */
const loadDiseaseData = async (elderId: number) => {
  try {
    // 加载疾病诊断记录
    const diagnosisData = await DiseaseDiagnosisApi.getDiseaseDiagnosisPage({
      elderId,
      pageNo: 1,
      pageSize: 100 // 获取所有疾病诊断记录
    })

    if (diagnosisData && diagnosisData.list) {
      diseaseRecords.value = diagnosisData.list

      // 加载所有疾病诊断对应的疾病诊断详情
      const allDetails: DiseaseDiagnosisDetailVO[] = []
      for (const diagnosis of diagnosisData.list) {
        try {
          const detailData = await DiseaseDiagnosisDetailApi.getDiseaseDiagnosisDetailPage({
            diagnosisId: diagnosis.id,
            pageNo: 1,
            pageSize: 100
          })
          if (detailData && detailData.list) {
            allDetails.push(...detailData.list)
          }
        } catch (error: any) {
          console.error(`获取疾病诊断${diagnosis.id}的详情失败:`, error)
        }
      }
      diseaseDetails.value = allDetails
    } else {
      diseaseRecords.value = []
      diseaseDetails.value = []
    }
  } catch (error: any) {
    console.error('获取疾病信息失败:', error)
    diseaseRecords.value = []
    diseaseDetails.value = []
  }
}

/** 加载健康信息 */
const loadHealthData = async (elderId: number) => {
  try {
    const data = await HealthInfoApi.getHealthInfoPage({
      elderId,
      pageNo: 1,
      pageSize: 100 // 获取所有健康信息记录
    })

    if (data && data.list) {
      healthRecords.value = data.list
    } else {
      healthRecords.value = []
    }
  } catch (error: any) {
    console.error('获取健康信息失败:', error)
    healthRecords.value = []
  }
}

/**
 * 获取健康等级文字描述
 * 1-能力完好 2-能力轻度受损(轻度失能) 3-能力中度受损(中度失能) 4-能力重度受损(重度失能) 5-能力完全丧失(完全失能)
 */
const getHealthLevelText = (value: number): string => {
  const healthLevelMap = {
    1: '能力完好',
    2: '能力轻度受损(轻度失能)',
    3: '能力中度受损(中度失能)',
    4: '能力重度受损(重度失能)',
    5: '能力完全丧失(完全失能)'
  }
  return healthLevelMap[value] || '未知'
}

/** 查看完整档案 */
const handleViewDetail = () => {
  dialogVisible.value = false
  router.push({
    path: '/elders/archivesInfo/detail',
    query: {
      elder_id: detailData.value.id
    }
  })
}

/**
 * 格式化金额
 */
const formatMoney = (value: number): string => {
  if (!value) return '0.00'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  color: #606266;
}

:deep(.el-descriptions__content) {
  padding: 12px 16px;
}

:deep(.dict-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
}

.extension-content {
  padding: 0;
}

.medication-content {
  padding: 0;
}

.disease-content {
  padding: 0;
}
</style>
