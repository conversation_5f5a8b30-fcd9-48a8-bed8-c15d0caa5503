<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="自理能力" prop="selfCareAbility">
        <el-select v-model="formData.selfCareAbility" placeholder="请选择自理能力" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SELF_CARE_ABILITY)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="精神状态" prop="mentalState">
        <el-select v-model="formData.mentalState" placeholder="请选择精神状态" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_MENTAL_STATE)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="躯体疾病" prop="physicalDisease">
        <el-select v-model="formData.physicalDisease" multiple placeholder="请选择躯体疾病" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.PHYSICAL_DISEASE)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="精神疾病" prop="mentalDisease">
        <el-select v-model="formData.mentalDisease" multiple placeholder="请选择精神疾病" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.MENTAL_DISEASE)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="服药情况" prop="medicationStatus">
        <el-input v-model="formData.medicationStatus" type="textarea" placeholder="请输入服药情况" />
      </el-form-item>
      <el-form-item label="跌倒史" prop="fallHistory">
        <el-select v-model="formData.fallHistory" placeholder="请选择跌倒史" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.FALL_HISTORY)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="走失史" prop="wanderingHistory">
        <el-select v-model="formData.wanderingHistory" placeholder="请选择走失史" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.WANDERING_HISTORY)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="住院史" prop="hospitalizationHistory">
        <el-select v-model="formData.hospitalizationHistory" placeholder="请选择住院史" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.HOSPITALIZATION_HISTORY)" 
                     :key="dict.value" 
                     :label="dict.label" 
                     :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="其他意外事件" prop="otherAccidents">
        <el-input v-model="formData.otherAccidents" type="textarea" placeholder="请输入其他意外事件" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="4"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import type { ArchivesExtensionSaveReqVO } from '@/api/elderArchives/archivesExtension'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['submit'])
const dialogVisible = ref(false)
const loading = ref(false)
const title = ref('')

const formRef = ref<FormInstance>()
const formData = ref<ArchivesExtensionSaveReqVO>({
  elderId: 0,
  selfCareAbility: 0,
  mentalState: 0,
  physicalDisease: [],
  mentalDisease: [],
  medicationStatus: '',
  fallHistory: 0,
  wanderingHistory: 0,
  hospitalizationHistory: 0,
  otherAccidents: '',
  remark: '',
  recorder: '',
  recordDate: undefined
})

const rules = reactive<FormRules>({
  selfCareAbility: [
    { required: true, message: '请选择自理能力', trigger: 'change' }
  ],
  mentalState: [
    { required: true, message: '请选择精神状态', trigger: 'change' }
  ]
})

/** 打开弹窗 */
const open = (data?: ArchivesExtensionSaveReqVO) => {
  dialogVisible.value = true
  title.value = '编辑拓展信息'
  
  // 重置表单
  formRef.value?.resetFields()
  if (data) {
    formData.value = {
      ...data,
      elderId: Number(data.elderId) || 0,
      selfCareAbility: Number(data.selfCareAbility) || 0,
      mentalState: Number(data.mentalState) || 0,
      physicalDisease: typeof data.physicalDisease === 'string' 
        ? data.physicalDisease.split(',').map(String)
        : (data.physicalDisease || []),
      mentalDisease: typeof data.mentalDisease === 'string'
        ? data.mentalDisease.split(',').map(String)
        : (data.mentalDisease || []),
      medicationStatus: data.medicationStatus || '',
      fallHistory: Number(data.fallHistory) || 0,
      wanderingHistory: Number(data.wanderingHistory) || 0,
      hospitalizationHistory: Number(data.hospitalizationHistory) || 0,
      otherAccidents: data.otherAccidents || '',
      remark: data.remark || '',
      recorder: data.recorder || '',
      recordDate: data.recordDate
    }
  }
}

/** 提交表单 */
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate()
  
  loading.value = true
  try {
    // 处理表单数据
    const submitData = {
      ...formData.value,
      elderId: Number(formData.value.elderId),
      selfCareAbility: Number(formData.value.selfCareAbility),
      mentalState: Number(formData.value.mentalState),
      physicalDisease: formData.value.physicalDisease,
      mentalDisease: formData.value.mentalDisease,
      fallHistory: Number(formData.value.fallHistory),
      wanderingHistory: Number(formData.value.wanderingHistory),
      hospitalizationHistory: Number(formData.value.hospitalizationHistory),
      recordDate: new Date()
    }
    
    emit('submit', submitData)
    dialogVisible.value = false
  } catch (error: any) {
    console.error('表单提交失败:', error)
    ElMessage.error('表单提交失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

/** 取消按钮 */
const cancel = () => {
  dialogVisible.value = false
}

defineExpose({
  open
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

:deep(.el-select) {
  width: 100%;
}
</style>
