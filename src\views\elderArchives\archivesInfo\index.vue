<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <!-- 基础搜索条件 -->
      <el-form-item label="老人姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="老人姓名" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber">
        <el-input v-model="queryParams.idNumber" placeholder="请输入身份证号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="服务等级" prop="serviceLevel">
        <el-select v-model="queryParams.serviceLevel" placeholder="请选择服务等级" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SERVICE_LEVEL)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="健康等级" prop="healthLevel">
        <el-select v-model="queryParams.healthLevel" placeholder="请选择健康等级" clearable class="!w-240px">
          <el-option label="1-能力完好" :value="1" />
          <el-option label="2-能力轻度受损(轻度失能)" :value="2" />
          <el-option label="3-能力中度受损(中度失能)" :value="3" />
          <el-option label="4-能力重度受损(重度失能)" :value="4" />
          <el-option label="5-能力完全丧失(完全失能)" :value="5" />
        </el-select>
      </el-form-item>

      <!-- 高级搜索条件 - 可折叠 -->
      <div v-if="showAdvanced">
        <el-form-item label="性别" prop="gender">
          <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="文化程度" prop="educationLevel">
          <el-select v-model="queryParams.educationLevel" placeholder="请选择文化程度" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.EDUCATION_LEVEL)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="婚姻状况" prop="maritalStatus">
          <el-select v-model="queryParams.maritalStatus" placeholder="请选择婚姻状况" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.MARITAL_STATUS)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="户籍类型" prop="householdType">
          <el-select v-model="queryParams.householdType" placeholder="请选择户籍类型" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.HOUSEHOLD_TYPE)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="民族" prop="ethnicGroup">
          <el-select v-model="queryParams.ethnicGroup" placeholder="请选择民族" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ETHNIC_GROUP)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="宗教信仰" prop="religiousBelief">
          <el-select v-model="queryParams.religiousBelief" placeholder="请选择宗教信仰" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RELIGIOUS_BELIEF)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="档案状态" prop="archiveStatus">
          <el-select v-model="queryParams.archiveStatus" placeholder="请选择档案状态" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ARCHIVE_STATUS)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="职业类型" prop="occupationType">
          <el-select v-model="queryParams.occupationType" placeholder="请选择职业类型" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.OCCUPATION_TYPE)" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="经济来源" prop="incomeSource">
          <el-select
            v-model="queryParams.incomeSource"
            multiple
            placeholder="请选择经济来源"
            class="!w-240px"
            :multiple-limit="5"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.INCOME_SOURCE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <!-- 添加标签展示区域 -->
          <div class="selected-tags mt-2">
            <el-tag
              v-for="source in queryParams.incomeSource"
              :key="source"
              class="mr-1 mb-1"
              type="warning"
              effect="plain"
              size="small"
            >
              {{ getDictLabel(DICT_TYPE.INCOME_SOURCE, source) }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="居住类型" prop="residenceType">
          <el-select
            v-model="queryParams.residenceType"
            multiple
            placeholder="请选择居住类型"
            class="!w-240px"
            :multiple-limit="5"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.RESIDENCE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <!-- 添加标签展示区域 -->
          <div class="selected-tags mt-2">
            <el-tag
              v-for="type in queryParams.residenceType"
              :key="type"
              class="mr-1 mb-1"
              type="success"
              effect="plain"
              size="small"
            >
              {{ getDictLabel(DICT_TYPE.RESIDENCE_TYPE, type) }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker v-model="queryParams.birthDate" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
            start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
            start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="toggleAdvanced">
          <Icon :icon="showAdvanced ? 'ep:arrow-up' : 'ep:arrow-down'" class="mr-5px" />
          {{ showAdvanced ? '收起' : '展开' }}
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 操作工具栏 -->
  <ContentWrap>
    <el-row :gutter="10" class="mb-10px">
      <el-col :span="24" class="flex justify-between items-center">
        <div class="text-lg font-bold">老人信息列表</div>
        <el-button type="primary" @click="handleAdd" class="add-button">
          <Icon icon="ep:plus" class="mr-5px" /> 新增老人信息
        </el-button>
      </el-col>
    </el-row>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column   label="老人姓名" align="center" prop="name" min-width="80" />
      <el-table-column label="头像" align="center" width="80">
        <template #default="scope">
          <el-avatar :size="40" :src="scope.row.avatar || '/default-avatar.png'" />
        </template>
      </el-table-column>
      <el-table-column label="服务等级" align="center" prop="serviceLevel" min-width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_SERVICE_LEVEL" :value="scope.row.serviceLevel" />
        </template>
      </el-table-column>
      <el-table-column label="健康等级" align="center" prop="healthLevel" min-width="120">
        <template #default="scope">
          <span v-if="scope.row.healthLevel">{{ getHealthLevelText(scope.row.healthLevel) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="身份证号" align="center" min-width="140">
        <template #default="{ row }">
          <el-tooltip
            :content="row.idNumber"
            placement="top"
            :show-after="0"
            :hide-after="0"
            effect="light"
          >
            <span>{{ formatIdNumber(row.idNumber) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center" prop="gender" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="contactPhone" min-width="120">
        <template #default="{ row }">
          <el-tooltip
            :content="row.contactPhone"
            placement="top"
            :show-after="0"
            :hide-after="0"
            effect="light"
          >
            <span>{{ formatPhoneNumber(row.contactPhone) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="身高(cm)" align="center" prop="height" width="100">
        <template #default="scope">
          <span>{{ scope.row.height || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="体重(kg)" align="center" prop="weight" width="100">
        <template #default="scope">
          <span>{{ scope.row.weight || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="民族" align="center" prop="ethnicGroup" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ETHNIC_GROUP" :value="scope.row.ethnicGroup" />
        </template>
      </el-table-column>
      <el-table-column label="档案状态" align="center" prop="archiveStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ARCHIVE_STATUS" :value="scope.row.archiveStatus" />
        </template>
      </el-table-column>
      <el-table-column label="职业类型" align="center" prop="occupationType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.OCCUPATION_TYPE" :value="scope.row.occupationType" />
        </template>
      </el-table-column>
      <el-table-column label="经济来源" align="center" prop="incomeSource" width="150">
        <template #default="scope">
          <div v-if="scope.row.incomeSource">
            <dict-tag v-for="item in scope.row.incomeSource.split(',')"
                     :key="item"
                     :type="DICT_TYPE.INCOME_SOURCE"
                     :value="parseInt(item)" />
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="月收入(元)" align="center" prop="monthlyIncome" width="120">
        <template #default="scope">
          <span v-if="scope.row.monthlyIncome">{{ formatMoney(scope.row.monthlyIncome) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="380" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleViewQuickDetail(scope.row)"
          >
            查看详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleViewDetail(scope.row)"
          >
            完整档案
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            @click="handleImportContact(scope.row)"
          >
            导入联系人信息
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 编辑弹窗 -->
  <ArchivesProfileForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <DetailDialog ref="detailDialogRef" />

  <!-- 联系人信息导入弹窗 -->
  <ContactInfoImportDialog ref="contactInfoImportDialogRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import { ArchivesInfoApi } from '@/api/elderArchives/archivesInfo'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { useRouter } from 'vue-router'
import ArchivesProfileForm from '../archivesProfile/ArchivesProfileForm.vue'
import DetailDialog from './components/DetailDialog.vue'
import ContactInfoImportDialog from './components/ContactInfoImportDialog.vue'

/** 老人信息 列表 */
defineOptions({ name: 'ArchivesInfo' })

const router = useRouter()
const message = useMessage() // 消息弹窗
// const { t } = useI18n() // 国际化 - 暂时不需要

const loading = ref(true) // 列表的加载中
const list = ref<ArchivesProfileVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvanced = ref(false) // 是否显示高级搜索
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  idNumber: undefined,
  gender: undefined,
  birthDate: [],
  contactPhone: undefined,
  serviceLevel: undefined,
  healthLevel: undefined,
  educationLevel: undefined,
  maritalStatus: undefined,
  householdType: undefined,
  ethnicGroup: undefined,
  religiousBelief: undefined,
  archiveStatus: undefined,
  occupationType: undefined,
  incomeSource: [] as number[],
  residenceType: [] as number[],
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const detailDialogRef = ref() // 详情弹窗
const contactInfoImportDialogRef = ref() // 联系人信息导入弹窗

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ArchivesInfoApi.getArchivesInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看详情按钮操作 - 弹窗 */
const handleViewQuickDetail = (row: ArchivesProfileVO) => {
  detailDialogRef.value.open(row.id)
}

/** 查看完整档案按钮操作 - 跳转页面 */
const handleViewDetail = (row: ArchivesProfileVO) => {
  router.push({
    path: '/elders/archivesInfo/detail',
    query: {
      elder_id: row.id
    }
  })
}

/** 导入联系人信息按钮操作 */
const handleImportContact = (row: ArchivesProfileVO) => {
  contactInfoImportDialogRef.value.open(row.id, row.name)
}

/** 切换高级搜索 */
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()

    // 转换查询参数，只保留后端支持的参数
    const exportParams = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      name: queryParams.name,
      idNumber: queryParams.idNumber, // 确保参数名与后端一致
      contactPhone: queryParams.contactPhone, // 确保参数名与后端一致
      gender: queryParams.gender,
      serviceLevel: queryParams.serviceLevel,
      educationLevel: queryParams.educationLevel,
      maritalStatus: queryParams.maritalStatus,
      householdType: queryParams.householdType,
      ethnicGroup: queryParams.ethnicGroup,
      religiousBelief: queryParams.religiousBelief,
      archiveStatus: queryParams.archiveStatus,
      occupationType: queryParams.occupationType,
      incomeSource: queryParams.incomeSource && queryParams.incomeSource.length > 0
        ? queryParams.incomeSource.join(',')
        : undefined,
      residenceType: queryParams.residenceType && queryParams.residenceType.length > 0
        ? queryParams.residenceType.join(',')
        : undefined,
      birthDateStart: queryParams.birthDate && queryParams.birthDate.length > 0 ? queryParams.birthDate[0] : undefined,
      birthDateEnd: queryParams.birthDate && queryParams.birthDate.length > 0 ? queryParams.birthDate[1] : undefined,
      createTimeStart: queryParams.createTime && queryParams.createTime.length > 0 ? queryParams.createTime[0] : undefined,
      createTimeEnd: queryParams.createTime && queryParams.createTime.length > 0 ? queryParams.createTime[1] : undefined
    }

    // 发起导出
    exportLoading.value = true
    await ArchivesInfoApi.exportArchivesInfo(exportParams)
    message.success('导出成功，请在浏览器下载栏查看')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

/** 新增按钮操作 */
const handleAdd = () => {
  openForm('create')
}

/** 编辑按钮操作 */
const handleEdit = (row: ArchivesProfileVO) => {
  openForm('update', row.id)
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (row: ArchivesProfileVO) => {
  try {
    // 先检查是否可以删除
    const checkResult = await ArchivesProfileApi.checkElderDeletable(row.id)

    if (!checkResult.deletable) {
      const relatedTables = checkResult.relatedTables.join('、')
      message.error(`无法删除该老人档案，以下模块中还存在该老人的数据：${relatedTables}`)
      return
    }

    // 确认删除
    await message.delConfirm(`确定要删除老人"${row.name}"的档案吗？`)

    // 执行删除
    await ArchivesProfileApi.deleteArchivesProfile(row.id)
    message.success('删除成功')

    // 刷新列表
    await getList()
  } catch (error) {
    console.error('删除失败:', error)
    if (error.message) {
      message.error(error.message)
    } else {
      message.error('删除失败')
    }
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

// 不再需要，已从表格中移除

/**
 * 格式化身份证号（脱敏处理）
 * 规则：只隐藏后四位数字
 * @param value 原始身份证号
 * @returns 脱敏后的身份证号
 */
const formatIdNumber = (value: string) => {
  if (!value) return ''
  if (value.length !== 18) return value
  return `${value.substring(0, 14)}****`
}

/**
 * 格式化手机号（脱敏处理）
 * 规则：保留前3位和后4位，中间用*号代替
 * @param value 原始手机号
 * @returns 脱敏后的手机号
 */
const formatPhoneNumber = (value: string) => {
  if (!value) return ''
  if (value.length !== 11) return value
  return `${value.substring(0, 3)}****${value.substring(7)}`
}

// 不再需要，已从表格中移除

/**
 * 获取健康等级文字描述
 * 1-能力完好 2-能力轻度受损(轻度失能) 3-能力中度受损(中度失能) 4-能力重度受损(重度失能) 5-能力完全丧失(完全失能)
 */
const getHealthLevelText = (value: number): string => {
  const healthLevelMap = {
    1: '能力完好',
    2: '能力轻度受损(轻度失能)',
    3: '能力中度受损(中度失能)',
    4: '能力重度受损(重度失能)',
    5: '能力完全丧失(完全失能)'
  }
  return healthLevelMap[value] || '未知'
}

/**
 * 格式化金额
 */
const formatMoney = (value: number): string => {
  if (!value) return '0.00'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

// 在 setup 中导出 getDictLabel
const getDictLabel = (type: string, value: string | number) => {
  const options = getIntDictOptions(type)
  const option = options.find(opt => opt.value === Number(value))
  return option ? option.label : ''
}
</script>

<style scoped>
/* 添加悬停样式 */
.el-table span {
  cursor: pointer;
}

/* 可选：添加一个小图标表示可以查看完整信息 */
.el-table span:hover::after {
  content: '👁️';
  margin-left: 4px;
  font-size: 12px;
}

.remark-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.selected-tags {
  margin-top: 8px;
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 优化选择框样式 */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__tags) {
  flex-wrap: wrap;
}

/* 高级搜索区域样式 */
.advanced-search {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* 表格操作列样式 */
:deep(.el-table .el-button--link) {
  padding: 2px 5px;
}

/* 表格行高度调整 */
:deep(.el-table .el-table__row) {
  height: 60px;
}

/* 新增按钮样式 */
.add-button {
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 4px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 标题样式 */
.text-lg {
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.font-bold {
  font-weight: 600;
}

/* Flex 布局工具类 */
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}
</style>
