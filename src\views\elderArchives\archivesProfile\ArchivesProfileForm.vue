<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading" class="archives-form">
      <!-- 基本信息卡片 -->
      <div class="form-card">
        <div class="card-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="avatar-section">
                <div class="avatar-title">头像照片</div>
                <el-form-item prop="avatar">
                  <el-upload
                    class="avatar-uploader"
                    action=""
                    :http-request="uploadAvatar"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload">
                    <div class="avatar-wrapper">
                      <img v-if="formData.avatar" :src="formData.avatar" class="avatar" />
                      <div v-else class="avatar-placeholder">
                        <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                        <div class="upload-text">点击上传</div>
                      </div>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="16">
              <div class="info-section">
                <el-form-item label="姓名" prop="name" class="highlight-item">
                  <el-input v-model.trim="formData.name" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="身份证号" prop="idNumber">
                  <el-input v-model.trim="formData.idNumber" placeholder="请输入身份证号" maxlength="18" show-word-limit />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="form-card">
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>详细信息</span>
        </div>
        <div class="card-content">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-select v-model="formData.gender" placeholder="请选择性别" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出生日期" prop="birthDate">
                <el-date-picker v-model="formData.birthDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                  placeholder="选择出生日期" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="身高(cm)" prop="height">
                <el-input-number v-model="formData.height" placeholder="请输入身高" :min="0" :max="300" :precision="1" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="体重(kg)" prop="weight">
                <el-input-number v-model="formData.weight" placeholder="请输入体重" :min="0" :max="500" :precision="1" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="民族" prop="ethnicGroup">
                <el-select v-model="formData.ethnicGroup" placeholder="请选择民族" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ETHNIC_GROUP)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宗教信仰" prop="religiousBelief">
                <el-select v-model="formData.religiousBelief" placeholder="请选择宗教信仰" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RELIGIOUS_BELIEF)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="文化程度" prop="educationLevel">
                <el-select v-model="formData.educationLevel" placeholder="请选择文化程度" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.EDUCATION_LEVEL)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="婚姻状况" prop="maritalStatus">
                <el-select v-model="formData.maritalStatus" placeholder="请选择婚姻状况" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.MARITAL_STATUS)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="户籍类型" prop="householdType">
                <el-select v-model="formData.householdType" placeholder="请选择户籍类型" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.HOUSEHOLD_TYPE)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="居住类型" prop="residenceType">
                <el-select
                  v-model="formData.residenceType"
                  multiple
                  placeholder="请选择居住类型"
                  class="w-full"
                  :multiple-limit="5"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.RESIDENCE_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
                <div class="selected-residence-types">
                  <el-tag
                    v-for="type in formData.residenceType"
                    :key="type"
                    class="residence-tag"
                    type="success"
                    effect="light"
                  >
                    {{ getDictLabel(DICT_TYPE.RESIDENCE_TYPE, type) }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="医疗保险" prop="medicalInsurance">
                <el-select
                  v-model="formData.medicalInsurance"
                  multiple
                  placeholder="请选择医疗保险"
                  class="w-full"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.MEDICAL_INSURANCE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
                <div class="selected-insurance-types">
                  <el-tag
                    v-for="type in formData.medicalInsurance"
                    :key="type"
                    class="insurance-tag"
                    type="info"
                    effect="light"
                  >
                    {{ getDictLabel(DICT_TYPE.MEDICAL_INSURANCE, type) }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model.trim="formData.contactPhone" placeholder="请输入联系电话">
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务等级" prop="serviceLevel">
                <el-select v-model="formData.serviceLevel" placeholder="请选择服务等级" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_SERVICE_LEVEL)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="健康等级" prop="healthLevel">
                <el-select v-model="formData.healthLevel" placeholder="请选择健康等级" class="w-full">
                  <el-option label="能力完好" :value="1" />
                  <el-option label="轻度失能" :value="2" />
                  <el-option label="中度失能" :value="3" />
                  <el-option label="重度失能" :value="4" />
                  <el-option label="完全失能" :value="5" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
<!--            <el-col :span="12">-->
<!--              <el-form-item label="档案状态" prop="archiveStatus">-->
<!--                <el-select v-model="formData.archiveStatus" placeholder="请选择档案状态" class="w-full">-->
<!--                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.ARCHIVE_STATUS)" :key="dict.value" :label="dict.label"-->
<!--                    :value="dict.value" />-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="24">
              <el-form-item label="职业类型" prop="occupationType">
                <el-select v-model="formData.occupationType" placeholder="请选择职业类型" class="w-full">
                  <el-option v-for="dict in getIntDictOptions(DICT_TYPE.OCCUPATION_TYPE)" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="月收入(元)" prop="monthlyIncome">
                <el-input-number v-model="formData.monthlyIncome" placeholder="请输入月收入" :min="0" :precision="2" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 预留空位 -->
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="经济来源" prop="incomeSource">
                <el-select
                  v-model="formData.incomeSource"
                  multiple
                  placeholder="请选择经济来源"
                  class="w-full"
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.INCOME_SOURCE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
                <div class="selected-income-sources">
                  <el-tag
                    v-for="source in formData.incomeSource"
                    :key="source"
                    class="income-tag"
                    type="warning"
                    effect="light"
                  >
                    {{ getDictLabel(DICT_TYPE.INCOME_SOURCE, source) }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 备注信息卡片 -->
      <div class="form-card">
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>备注信息</span>
        </div>
        <div class="card-content">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息（选填）"
              maxlength="500"
              show-word-limit
              clearable
            />
          </el-form-item>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import { Plus, User, InfoFilled, Document, Phone } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'

interface FormData {
  id: number
  name: string
  idNumber: string
  gender: number | undefined
  birthDate: string
  height: number | undefined
  weight: number | undefined
  contactPhone: string
  avatar: string
  ethnicGroup: number | undefined
  religiousBelief: number | undefined
  serviceLevel: number | undefined
  healthLevel: number | undefined
  educationLevel: number | undefined
  maritalStatus: number | undefined
  householdType: number | undefined
  residenceType: number[]
  medicalInsurance: number[]
  occupationType: number | undefined
  incomeSource: number[]
  monthlyIncome: number | undefined
  archiveStatus: number | undefined
  remark: string
}

/** 老人基础信息 表单 */
defineOptions({ name: 'ArchivesProfileForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<FormData>({
  id: 0,
  name: '',
  idNumber: '',
  gender: undefined,
  birthDate: '',
  height: undefined,
  weight: undefined,
  contactPhone: '',
  avatar: '',
  ethnicGroup: undefined,
  religiousBelief: undefined,
  serviceLevel: undefined,
  healthLevel: undefined,
  educationLevel: undefined,
  maritalStatus: undefined,
  householdType: undefined,
  residenceType: [],
  medicalInsurance: [],
  occupationType: undefined,
  incomeSource: [],
  monthlyIncome: undefined,
  archiveStatus: undefined,
  remark: ''
})
const formRules = reactive({
  name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  idNumber: [
    { required: true, message: '身份证号不能为空', trigger: 'blur' },
    {
      pattern: /^[\d]{17}[\dX]$/,
      message: '身份证号必须是18位，且只能包含数字和大写X',
      trigger: 'blur'
    },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback()
          return
        }
        // 检查是否包含小写x
        if (value.includes('x')) {
          callback(new Error('身份证号的X必须是大写'))
          return
        }
        // 检查是否只有最后一位是X
        if (value.indexOf('X') !== -1 && value.indexOf('X') !== 17) {
          callback(new Error('X只能出现在最后一位'))
          return
        }
        // 检查是否包含其他字母
        if (/[a-zA-Z]/.test(value.slice(0, 17))) {
          callback(new Error('前17位只能是数字'))
          return
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  gender: [{ required: true, message: '性别不能为空', trigger: 'change' }],
  birthDate: [{ required: true, message: '出生日期不能为空', trigger: 'blur' }],
  contactPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
  serviceLevel: [{ required: true, message: '服务等级不能为空', trigger: 'change' }],
  healthLevel: [{ required: true, message: '健康等级不能为空', trigger: 'change' }],
  educationLevel: [{ required: true, message: '文化程度不能为空', trigger: 'change' }],
  maritalStatus: [{ required: true, message: '婚姻状况不能为空', trigger: 'change' }],
  householdType: [{ required: true, message: '户籍类型不能为空', trigger: 'change' }],
  residenceType: [{ required: true, message: '居住类型不能为空', trigger: 'change' }],
  medicalInsurance: [{ required: true, message: '医疗保险不能为空', trigger: 'change' }],
  occupationType: [{ required: true, message: '职业类型不能为空', trigger: 'change' }],
  remark: [
    { max: 500, message: '备注内容不能超过500个字符', trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ArchivesProfileApi.getArchivesProfile(id)
      formData.value = {
        ...data,
        residenceType: data.residenceType ? data.residenceType.split(',').map(Number) : [],
        medicalInsurance: data.medicalInsurance ? data.medicalInsurance.split(',').map(Number) : [],
        incomeSource: data.incomeSource ? data.incomeSource.split(',').map(Number) : []
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    // 处理提交数据，确保类型正确
    const submitData = {
      ...formData.value,
      // 数字类型字段，如果未选择则默认为0
      gender: formData.value.gender || 0,
      serviceLevel: formData.value.serviceLevel || 0,
      healthLevel: formData.value.healthLevel || 0,
      educationLevel: formData.value.educationLevel || 0,
      maritalStatus: formData.value.maritalStatus || 0,
      householdType: formData.value.householdType || 0,
      occupationType: formData.value.occupationType || 0,
      // 居住类型转换为以逗号分隔的字符串
      residenceType: formData.value.residenceType?.join(',') || '',
      // 医疗保险转换为以逗号分隔的字符串
      medicalInsurance: formData.value.medicalInsurance?.join(',') || '',
      // 经济来源转换为以逗号分隔的字符串
      incomeSource: formData.value.incomeSource?.join(',') || '',
      // 转换日期格式，确保不会为null
      birthDate: formData.value.birthDate ? new Date(formData.value.birthDate) : new Date()
    }
    if (formType.value === 'create') {
      await ArchivesProfileApi.createArchivesProfile(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await ArchivesProfileApi.updateArchivesProfile(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 修改身份证号解析函数，添加更详细的错误提示
const parseIdNumber = (idNumber: string) => {
  if (!idNumber || idNumber.length !== 18) {
    return null
  }

  try {
    // 验证日期部分是否合法
    const year = parseInt(idNumber.substring(6, 10))
    const month = parseInt(idNumber.substring(10, 12))
    const day = parseInt(idNumber.substring(12, 14))

    // 详细的日期验证
    if (year < 1900) {
      message.warning('出生年份不能早于1900年')
      return null
    }
    if (year > new Date().getFullYear()) {
      message.warning('出生年份不能晚于当前年份')
      return null
    }
    if (month < 1 || month > 12) {
      message.warning('出生月份必须在1-12之间')
      return null
    }
    if (day < 1 || day > 31) {
      message.warning('出生日期必须在1-31之间')
      return null
    }

    const birthDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

    // 提取性别并确保转换为数字
    const genderNum = parseInt(idNumber.charAt(16))
    const gender = genderNum % 2 === 0 ? 2 : 1 // 奇数为男(1)，偶数为女(2)

    return {
      birthDate,
      gender
    }
  } catch (error) {
    console.error('解析身份证号失败:', error)
    message.error('身份证号格式不正确')
    return null
  }
}

// 创建防抖的保存函数
const debouncedSave = debounce(async (submitData: ArchivesProfileVO) => {
  try {
    await ArchivesProfileApi.updateArchivesProfile(submitData)
    message.success('自动更新成功')
  } catch (error) {
    console.error('自动更新失败:', error)
    message.error('自动更新失败')
  }
}, 1000) // 1秒内只执行一次

// 修改 watch 中的保存逻辑
watch(() => formData.value.idNumber, async (newValue, oldValue) => {
  // 如果新值和旧值相同，则不处理
  if (newValue === oldValue) return

  // 清空之前的性别和出生日期，以便重新识别
  if (newValue?.length !== 18) {
    formData.value.gender = undefined
    formData.value.birthDate = ''
    return
  }

  const parsedInfo = parseIdNumber(newValue)
  if (parsedInfo) {
    try {
      // 每次身份证号变化时都更新性别和出生日期
      formData.value.gender = parsedInfo.gender
      formData.value.birthDate = parsedInfo.birthDate

      // 如果是编辑模式且有ID，使用防抖函数自动保存
      if (formType.value === 'update' && formData.value.id) {
        const submitData: ArchivesProfileVO = {
          id: formData.value.id,
          name: formData.value.name?.trim() || '',
          idNumber: formData.value.idNumber?.trim() || '',
          gender: formData.value.gender || 0,
          birthDate: formData.value.birthDate ? new Date(formData.value.birthDate) : new Date(),
          contactPhone: formData.value.contactPhone?.trim() || '',
          avatar: formData.value.avatar || '',
          serviceLevel: formData.value.serviceLevel || 0,
          educationLevel: formData.value.educationLevel || 0,
          maritalStatus: formData.value.maritalStatus || 0,
          householdType: formData.value.householdType || 0,
          residenceType: formData.value.residenceType?.join(',') || '',
          medicalInsurance: formData.value.medicalInsurance?.join(',') || '',
          remark: formData.value.remark?.trim() || ''
        }

        // 使用防抖函数
        debouncedSave(submitData)
      }
    } catch (error) {
      console.error('设置解析数据失败:', error)
      message.error('设置解析数据失败')
    }
  } else {
    // 如果解析失败，清空性别和出生日期
    formData.value.gender = undefined
    formData.value.birthDate = ''
  }
}, { immediate: true })

// 修改重置表单函数，确保重置时清空所有字段
const resetForm = () => {
  formData.value = {
    id: 0,
    name: '',
    idNumber: '',
    gender: undefined,
    birthDate: '',
    height: undefined,
    weight: undefined,
    contactPhone: '',
    avatar: '',
    ethnicGroup: undefined,
    religiousBelief: undefined,
    serviceLevel: undefined,
    healthLevel: undefined,
    educationLevel: undefined,
    maritalStatus: undefined,
    householdType: undefined,
    residenceType: [],
    medicalInsurance: [],
    occupationType: undefined,
    incomeSource: [],
    monthlyIncome: undefined,
    archiveStatus: undefined,
    remark: ''
  }
  formRef.value?.resetFields()
}

// 头像上传前的校验
const beforeAvatarUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJpgOrPng) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
  }
  return isJpgOrPng && isLt2M
}

// 自定义上传方法
const uploadAvatar = async (params: any) => {
  try {
    const uploadData = new FormData()
    uploadData.append('file', params.file)
    // 只有在编辑模式下才传递 id
    if (formData.value.id) {
      uploadData.append('id', formData.value.id.toString())
    }
    const response = await ArchivesProfileApi.uploadAvatar(uploadData)
    // 后端返回的是 CommonResult<String> 格式，需要取 data 字段
    formData.value.avatar = response.data || response
    ElMessage.success('头像上传成功')
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('头像上传失败')
  }
}
</script>

<style scoped>
.archives-form {
  padding: 20px;
  background-color: var(--el-bg-color);
}

.form-card {
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.form-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-header .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.card-content {
  padding: 20px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.avatar-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.avatar-wrapper {
  width: 140px;
  height: 140px;
  border: 2px dashed var(--el-border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-lighter);
}

.avatar-wrapper:hover {
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar {
  width: 140px;
  height: 140px;
  display: block;
  object-fit: cover;
}

.upload-text {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: var(--el-text-color-secondary);
}

.info-section {
  padding: 0 20px;
}

.highlight-item :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.w-full {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 添加输入框图标样式 */
:deep(.el-input__prefix) {
  color: var(--el-text-color-secondary);
}

/* 表单项动画 */
.el-form-item {
  transition: all 0.3s ease;
}

.el-form-item:hover :deep(.el-form-item__label) {
  color: var(--el-color-primary);
}

/* 对话框样式 */
:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding: 20px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
}

.selected-residence-types {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}

.residence-tag {
  margin: 2px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__tags) {
  flex-wrap: wrap;
}

.selected-insurance-types {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}

.insurance-tag {
  margin: 2px;
}

.selected-income-sources {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
}

.income-tag {
  margin: 2px;
}
</style>
