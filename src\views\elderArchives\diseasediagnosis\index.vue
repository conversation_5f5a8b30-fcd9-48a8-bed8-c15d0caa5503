<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="老人姓名" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人姓名"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="诊断日期" prop="diagnosisDate">
        <el-date-picker
          v-model="queryParams.diagnosisDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="诊断医生" prop="doctorName">
        <el-input
          v-model="queryParams.doctorName"
          placeholder="请输入诊断医生"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="诊断类型" prop="diagnosisType">
        <el-select
          v-model="queryParams.diagnosisType"
          placeholder="请选择诊断类型"
          clearable
          class="!w-240px"
        >
          <el-option v-for="dict in getDictOptions(DICT_TYPE.DIAGNOSIS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:disease-diagnosis:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:disease-diagnosis:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="老人姓名" align="center" prop="elderName">
        <template #default="scope">
          <span>{{ getElderName(scope.row.elderId) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="诊断日期"
        align="center"
        prop="diagnosisDate"
        :formatter="arrayDateFormatter"
        width="120px"
      />
      <el-table-column label="诊断医生" align="center" prop="doctorName" />
      <el-table-column label="诊断机构" align="center" prop="hospitalName" />
      <el-table-column label="诊断类型" align="center" prop="diagnosisType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIAGNOSIS_TYPE" :value="scope.row.diagnosisType" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['elderArchives:disease-diagnosis:query']"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:disease-diagnosis:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            @click="openDiagnosisDetail(scope.row.id)"
            v-hasPermi="['elderArchives:disease-diagnosis:update']"
          >
            疾病信息录入
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:disease-diagnosis:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DiseaseDiagnosisForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <DiseaseDiagnosisDetail ref="detailRef" />

  <!-- 疾病信息录入弹窗 -->
  <DiseaseDiagnosisDetailDialog ref="diagnosisDetailRef" />
</template>

<script setup lang="ts">
import { dateFormatter, arrayDateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { DiseaseDiagnosisApi, DiseaseDiagnosisVO } from '@/api/elderArchives/diseasediagnosis'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import DiseaseDiagnosisForm from './DiseaseDiagnosisForm.vue'
import DiseaseDiagnosisDetail from './DiseaseDiagnosisDetail.vue'
import DiseaseDiagnosisDetailDialog from './DiseaseDiagnosisDetailDialog.vue'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/** 老人疾病诊断 列表 */
defineOptions({ name: 'DiseaseDiagnosis' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DiseaseDiagnosisVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([]) // 老人列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  diagnosisDate: [],
  doctorName: undefined,
  hospitalName: undefined,
  diagnosisType: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 获取老人姓名 */
const getElderName = (elderId: number) => {
  const elder = elderProfiles.value.find(profile => profile.id === elderId)
  return elder ? elder.name : '未关联'
}

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DiseaseDiagnosisApi.getDiseaseDiagnosisPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看详情操作 */
const detailRef = ref()
const handleViewDetail = (row: DiseaseDiagnosisVO) => {
  detailRef.value.open(row)
}

/** 打开疾病信息录入对话框 */
const diagnosisDetailRef = ref()
const openDiagnosisDetail = (id: number) => {
  diagnosisDetailRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DiseaseDiagnosisApi.deleteDiseaseDiagnosis(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DiseaseDiagnosisApi.exportDiseaseDiagnosis(queryParams)
    download.excel(data, '老人疾病诊断.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  // 先获取老人列表，再获取疾病诊断列表
  await getElderProfiles()
  await getList()
})
</script>
