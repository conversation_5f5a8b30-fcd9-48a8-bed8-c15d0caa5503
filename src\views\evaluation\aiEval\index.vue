<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useTagsView } from '@/hooks/web/useTagsView'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { InfoFilled, ArrowDown } from '@element-plus/icons-vue'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile/index'
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import { AiEvaluationApi } from '@/api/ai/evaluation'
import { ModelApi } from '@/api/ai/model/model'
import formCreate from '@form-create/element-ui'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useMessage } from '@/hooks/web/useMessage'
import { marked } from 'marked'

defineOptions({ name: 'AiEvalIndex' })

// 消息弹窗
const message = useMessage()
// 路由
const route = useRoute()
// 路由
const router = useRouter()
// 标签页管理
const { closeCurrent, refreshPage } = useTagsView()
const tagsViewStore = useTagsViewStore()

// 自动保存相关变量
const autoSaveEnabled = ref(false)
const autoSaveInterval = ref(60000) // 60秒
const autoSaveTimer = ref<ReturnType<typeof setInterval> | null>(null)
const autoSaveProgress = ref(0)
const autoSaveLastTime = ref(Date.now())
const autoSaveStatus = ref<'' | 'success' | 'warning' | 'exception'>('')
const nextSaveTime = ref('')
const savingDraft = ref(false)

// AI分析相关变量
const aiAnalysis = ref('')
const generatingAI = ref(false)
const aiLoading = ref(false)
const aiModelList = ref([])
const selectedModelId = ref<number | null>(null)

// 预览表单
const previewForm = ref({})
// 预览API
const previewApi = ref(null)
// 模板规则
const templateRule = ref([])
// 模板选项
const templateOption = ref({
  submitBtn: false,
  resetBtn: false
})
// 评估师分析
const evaluatorAnalysis = ref<string>('')

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null)

// 评估基础信息
const evalInfo = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationTime: '' as any, // 允许字符串或日期类型
  evaluationReason: '',
  type: 0 as number,
  status: 1 as number // 默认为已提交状态
})

// 添加模板类型的响应式变量
const templateType = ref<number>(0)

// 添加模板类型选项
const templateTypeOptions = ref<Array<any>>([])

// 获取表单类型名称的计算属性
const getTemplateTypeName = computed(() => {
  const option = templateTypeOptions.value.find((item: any) => item.value === templateType.value)
  return option ? option.label : '未知类型'
})

// 添加模板详情变量
const templateDetail = ref({
  version: '',
  validityPeriod: 3,
  validityUnit: 'month',
  validityStartTimeType: 'evaluationTime',
  validityStartTime: '',
  templateRule: [] as any[]
})

// 加载模板数据
const loadTemplate = async () => {
  const templateId = route.query.templateId as string
  if (!templateId) return

  try {
    // 获取模板详情
    const data = await TemplateApi.getTemplate(parseInt(templateId))
    templateDetail.value = data

    // 解析formSchema提取有效期设置和评分规则
    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        // console.log(schema)
        if (schema.option && schema.option.form) {
          templateDetail.value.validityPeriod = schema.option.form.validityPeriod || 3
          templateDetail.value.validityUnit = schema.option.form.validityUnit || 'month'
          templateDetail.value.validityStartTimeType =
            schema.option.form.validityStartTimeType || 'evaluationTime'
          templateDetail.value.validityStartTime = schema.option.form.validityStartTime || ''

          // 提取评分规则
          if (schema.option.form.templateRule) {
            try {
              // 如果templateRule是字符串，解析为数组
              const templateRule = typeof schema.option.form.templateRule === 'string'
                ? JSON.parse(schema.option.form.templateRule)
                : schema.option.form.templateRule
              templateDetail.value.templateRule = templateRule
              console.log('从模板中提取的评分规则:', templateRule)
            } catch (ruleError) {
              console.error('解析评分规则失败:', ruleError)
              templateDetail.value.templateRule = []
            }
          } else {
            templateDetail.value.templateRule = []
          }
        }
      } catch (e) {
        // console.error('解析模板数据失败:', e)
        ElMessage.error('解析模板数据失败')
      }
    }

    // 其他模板加载逻辑
    if (data.formSchema) {
      const schema = JSON.parse(data.formSchema)
      templateRule.value = schema.rule || []

      // 保存完整的原始模板选项
      templateOption.value = {
        ...templateOption.value,
        ...schema.option,
        formName: schema.option?.formName || evalInfo.templateName, // 确保formName可用
        form: schema.option?.form || {},
        submitBtn: false,
        resetBtn: false
      }

      // 获取模板类型
      templateType.value = schema.option?.form?.templateType || 0
      console.log('模板类型:', templateType.value)
    }
  } catch (error) {
    // console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

// 添加一个变量来跟踪是否是继续评估模式以及评估结果ID
const isContinueEvaluation = ref(false)
const evaluationResultId = ref<number | null>(null)

// 加载AI模型列表
const loadAiModels = async () => {
  try {
    const models = await ModelApi.getModelSimpleList(1) // 1表示聊天模型类型
    // 过滤掉包含"dify"的模型（不区分大小写）
    aiModelList.value = (models || []).filter(model => {
      const modelName = (model.name || '').toLowerCase()
      const modelPlatform = (model.platform || '').toLowerCase()
      const modelModel = (model.model || '').toLowerCase()
      return !modelName.includes('dify') &&
             !modelPlatform.includes('dify') &&
             !modelModel.includes('dify')
    })

    // 如果有模型，默认选择第一个
    if (aiModelList.value.length > 0) {
      selectedModelId.value = aiModelList.value[0].id
    }
  } catch (error) {
    console.error('加载AI模型列表失败:', error)
    message.warning('加载AI模型列表失败')
  }
}

// 加载评估基础信息
const loadEvalInfo = async () => {
  try {
    const query = route.query

    // 检查是否是继续评估模式
    isContinueEvaluation.value = query.isContinue === 'true'

    // 如果是继续评估模式，需要先获取评估结果数据
    if (isContinueEvaluation.value && query.id) {
      const resultId = parseInt(query.id as string)
      evaluationResultId.value = resultId // 保存评估结果ID，用于后续更新操作

      try {
        const resultData = await ResultApi.getResult(resultId)

        // 填充评估信息
        evalInfo.elderId = resultData.elderId
        evalInfo.elderName = resultData.elderName
        evalInfo.templateId = resultData.templateId
        evalInfo.templateName = resultData.templateName
        evalInfo.evaluatorId = resultData.evaluatorId
        evalInfo.evaluatorName = resultData.evaluatorName
        evalInfo.evaluationReason = resultData.evaluationReason
        evalInfo.type = resultData.type
        evalInfo.status = 2 // 设置为暂存状态

        // 如果有评估结果数据，解析并填充表单
        if (resultData.result) {
          try {
            const parsedResult = JSON.parse(resultData.result)

            // 填充表单数据
            if (parsedResult.formData) {
              previewForm.value = parsedResult.formData
            } else if (parsedResult.fullResult && parsedResult.fullResult.rawFormData) {
              previewForm.value = parsedResult.fullResult.rawFormData
            }

            // 填充评估师分析和AI分析
            evaluatorAnalysis.value = resultData.evaluatorAnalysis || ''
            aiAnalysis.value = resultData.aiAnalysis || ''
          } catch (e) {
            console.error('解析评估结果数据失败:', e)
          }
        }
      } catch (error) {
        console.error('获取评估结果数据失败:', error)
        message.error('获取评估结果数据失败')
      }
    } else {
      // 正常模式，从路由参数获取信息
      evalInfo.elderId = parseInt(query.elderId as string)
      evalInfo.elderName = query.elderName as string
      evalInfo.templateId = parseInt(query.templateId as string)
      evalInfo.templateName = query.templateName as string
      evalInfo.evaluatorId = parseInt(query.evaluatorId as string)
      evalInfo.evaluatorName = query.evaluatorName as string
      evalInfo.evaluationTime = query.evaluationTime
      evalInfo.evaluationReason = query.evaluationReason as string
      evalInfo.type = query.type as unknown as number
      evalInfo.status = Number(query.status) || 1 // 默认为已提交状态
    }

    // 根据 elderId 获取老人信息
    if (evalInfo.elderId) {
      const elderData = await ArchivesProfileApi.getArchivesProfile(evalInfo.elderId)
      elderInfo.value = elderData // 存储老人信息
    }
    console.log(elderInfo.value)
  } catch (error) {
    // console.error('加载评估信息失败:', error)
    message.error('加载评估信息失败')
  }
}

// 处理表单提交
const handleSubmit = async (formData: any) => {
  // console.log('提交的表单数据:', formData)
  // 复制规则以避免修改原始数据
  const copyRules = formCreate.copyRules(templateRule.value)
  const options = templateOption.value

  try {
    let aiInput = `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 构建字段和标题的映射关系
    const fieldTitleMap = {}
    const fieldOptionsMap = {}
    const fieldParentMap = {}
    const fieldMidParentMap = {}
    const fieldTopParentMap = {}

    // 递归函数，用于从规则中提取字段信息
    const extractFieldInfo = (
      rules,
      parentTitle = '',
      midParentTitle = '',
      topParentTitle = ''
    ) => {
      if (!rules || !Array.isArray(rules)) return

      rules.forEach((rule) => {
        // 跳过elAlert类型
        if (rule._fc_drag_tag === 'elAlert') return

        // 获取当前项的标题
        let currentTitle = ''
        if (rule.props?.header) {
          currentTitle = rule.props.header
        } else if (rule.title && rule.title.trim() !== '') {
          currentTitle = rule.title
        } else if (rule.name?.startsWith('ref_') && rule.info && rule.info.trim() !== '') {
          currentTitle = rule.info
        } else if (rule.name && !rule.name.startsWith('ref_')) {
          currentTitle = rule.name
        }

        // 记录规则的基本信息
        // if (rule.field) {
        //   console.log(
        //     `提取字段信息: 字段=${rule.field}, 标题=${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // } else if (currentTitle) {
        //   console.log(
        //     `处理标题: ${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // }

        // 如果有字段，记录它的信息
        if (rule.field) {
          fieldTitleMap[rule.field] = currentTitle || ''
          fieldParentMap[rule.field] = parentTitle || ''
          fieldMidParentMap[rule.field] = midParentTitle || ''
          fieldTopParentMap[rule.field] = topParentTitle || ''

          if (rule.options && rule.options.length > 0) {
            fieldOptionsMap[rule.field] = rule.options
            // console.log(`字段${rule.field}的选项:`, rule.options)
          }
        }

        // 如果有子项，递归处理
        if (rule.children && rule.children.length > 0) {
          // 确定标题的层级关系
          let newTopParent = topParentTitle
          let newMidParent = midParentTitle

          // 如果是顶层卡片
          if (!parentTitle && rule._fc_drag_tag === 'elCard') {
            newTopParent = currentTitle
          }
          // 如果已有顶层父级，但没有中间父级
          else if (topParentTitle && !midParentTitle && rule._fc_drag_tag === 'elCard') {
            newMidParent = currentTitle
          }

          extractFieldInfo(rule.children, currentTitle || parentTitle, newMidParent, newTopParent)
        }
      })
    }

    // 提取字段信息
    // console.log('开始提取字段信息...')
    extractFieldInfo(copyRules)
    // console.log('字段标题映射:', fieldTitleMap)
    // console.log('字段父标题映射:', fieldParentMap)
    // console.log('字段中间父标题映射:', fieldMidParentMap)
    // console.log('字段顶层标题映射:', fieldTopParentMap)
    // console.log('字段选项映射:', fieldOptionsMap)

    // 处理formData，构建aiInput
    // console.log('开始构建aiInput...')

    // 将评估结果组织成层级结构，使用数组保持顺序
    const hierarchicalResults = {}
    // 记录处理顺序
    const processOrder = {
      topParents: [] as any[],
      midParents: {},
      parentTitles: {},
      fields: {}
    }

    // 对字段进行分组和层级化处理
    for (const field in formData) {
      if (fieldTopParentMap[field]) {
        const topParent = fieldTopParentMap[field]
        const midParent = fieldMidParentMap[field]
        const parentTitle = fieldParentMap[field]
        const fieldTitle = fieldTitleMap[field]

        // 记录处理顺序
        // 记录顶层标题顺序
        if (!processOrder.topParents.includes(topParent)) {
          processOrder.topParents.push(topParent)
        }

        // 记录中间标题顺序
        if (midParent && midParent !== topParent) {
          if (!processOrder.midParents[topParent]) {
            processOrder.midParents[topParent] = []
          }
          if (!processOrder.midParents[topParent].includes(midParent)) {
            processOrder.midParents[topParent].push(midParent)
          }

          // 记录直接父标题顺序（在中间标题下）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.parentTitles[midKey]) {
              processOrder.parentTitles[midKey] = []
            }
            if (!processOrder.parentTitles[midKey].includes(parentTitle)) {
              processOrder.parentTitles[midKey].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${midParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在中间标题下）
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.fields[midKey]) {
              processOrder.fields[midKey] = []
            }
            processOrder.fields[midKey].push(field)
          }
        } else {
          // 记录直接父标题顺序（在顶层标题下）
          if (parentTitle && parentTitle !== topParent) {
            if (!processOrder.parentTitles[topParent]) {
              processOrder.parentTitles[topParent] = []
            }
            if (!processOrder.parentTitles[topParent].includes(parentTitle)) {
              processOrder.parentTitles[topParent].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在顶层标题下）
            if (!processOrder.fields[topParent]) {
              processOrder.fields[topParent] = []
            }
            processOrder.fields[topParent].push(field)
          }
        }

        // 处理值
        let resultValue = formData[field]
        let resultLabel = ''
        if (fieldOptionsMap[field]) {
          if (Array.isArray(resultValue)) {
            const labels = resultValue.map((val) => {
              const option = fieldOptionsMap[field].find((opt) => String(opt.value) === String(val))
              return option ? option.label : '未找到标签'
            })
            resultLabel = labels.join('、')
          } else {
            const option = fieldOptionsMap[field].find(
              (opt) => String(opt.value) === String(resultValue)
            )
            resultLabel = option ? option.label : '未找到标签'
          }
        } else {
          resultLabel = String(resultValue)
        }

        // 创建层级结构
        if (!hierarchicalResults[topParent]) {
          hierarchicalResults[topParent] = {
            title: topParent,
            children: [],
            childrenMap: {} // 用于快速查找
          }
        }

        // 添加中间父级（如果存在）
        if (midParent && midParent !== topParent) {
          // 检查中间父级是否已存在
          if (!hierarchicalResults[topParent].childrenMap[midParent]) {
            const midParentNode = {
              title: midParent,
              parent: topParent,
              children: [],
              childrenMap: {} // 用于快速查找
            }
            hierarchicalResults[topParent].children.push(midParentNode)
            hierarchicalResults[topParent].childrenMap[midParent] = midParentNode
          }

          // 添加直接父级（如果存在且与中间父级不同）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            // 检查直接父级是否已存在
            if (!midParentNode.childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: midParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              midParentNode.children.push(parentNode)
              midParentNode.childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = midParentNode.childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与中间父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: midParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            midParentNode.children.push(fieldNode)
            midParentNode.childrenMap[fieldKey] = fieldNode
          }
        }
        // 如果没有中间父级
        else {
          // 添加直接父级（如果存在且与顶层父级不同）
          if (parentTitle && parentTitle !== topParent) {
            // 检查直接父级是否已存在
            if (!hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: topParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              hierarchicalResults[topParent].children.push(parentNode)
              hierarchicalResults[topParent].childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与顶层父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: topParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            hierarchicalResults[topParent].children.push(fieldNode)
            hierarchicalResults[topParent].childrenMap[fieldKey] = fieldNode
          }
        }

        // 构建文本输出
        aiInput += `${topParent}\n`
        if (midParent && midParent !== topParent) {
          aiInput += `${midParent}\n`
        }
        if (parentTitle && parentTitle !== topParent && parentTitle !== midParent) {
          aiInput += `${parentTitle}\n`
        }
        if (
          fieldTitle &&
          fieldTitle !== parentTitle &&
          fieldTitle !== midParent &&
          fieldTitle !== topParent
        ) {
          aiInput += `${fieldTitle}\n`
        }
        aiInput += `${resultLabel}\n\n`
      }
    }

    // 最终处理，根据处理顺序重新组织结果，并删除临时的childrenMap
    const orderedResults = {}
    processOrder.topParents.forEach((topParent) => {
      if (hierarchicalResults[topParent]) {
        // 创建有序的顶级结果
        const topResult = {
          title: hierarchicalResults[topParent].title,
          children: [] as any[]
        }

        // 处理中间父级（如果有）
        if (processOrder.midParents[topParent]) {
          processOrder.midParents[topParent].forEach((midParent) => {
            if (hierarchicalResults[topParent].childrenMap[midParent]) {
              const midNode = hierarchicalResults[topParent].childrenMap[midParent]
              const orderedMidNode = {
                title: midNode.title,
                parent: midNode.parent,
                children: [] as any[]
              }

              // 处理直接父级（如果有）
              const midKey = `${topParent}:${midParent}`
              if (processOrder.parentTitles[midKey]) {
                processOrder.parentTitles[midKey].forEach((parentTitle) => {
                  if (midNode.childrenMap[parentTitle]) {
                    const parentNode = midNode.childrenMap[parentTitle]
                    const orderedParentNode = {
                      title: parentNode.title,
                      parent: parentNode.parent,
                      children: [] as any[]
                    }

                    // 处理字段
                    const parentKey = `${topParent}:${midParent}:${parentTitle}`
                    if (processOrder.fields[parentKey]) {
                      processOrder.fields[parentKey].forEach((field) => {
                        const fieldKey = fieldTitleMap[field] || field
                        if (parentNode.childrenMap[fieldKey]) {
                          orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                        }
                      })
                    }

                    orderedMidNode.children.push(orderedParentNode)
                  }
                })
              }

              // 处理直接在中间父级下的字段
              if (processOrder.fields[midKey]) {
                processOrder.fields[midKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (midNode.childrenMap[fieldKey] && !midNode.childrenMap[fieldKey].children) {
                    orderedMidNode.children.push(midNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedMidNode)
            }
          })
        }

        // 处理直接父级（如果没有中间父级）
        if (processOrder.parentTitles[topParent]) {
          processOrder.parentTitles[topParent].forEach((parentTitle) => {
            if (hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
              const orderedParentNode = {
                title: parentNode.title,
                parent: parentNode.parent,
                children: [] as any[]
              }

              // 处理字段
              const parentKey = `${topParent}:${parentTitle}`
              if (processOrder.fields[parentKey]) {
                processOrder.fields[parentKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (parentNode.childrenMap[fieldKey]) {
                    orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedParentNode)
            }
          })
        }

        // 处理直接在顶级下的字段
        if (processOrder.fields[topParent]) {
          processOrder.fields[topParent].forEach((field) => {
            const fieldKey = fieldTitleMap[field] || field
            if (
              hierarchicalResults[topParent].childrenMap[fieldKey] &&
              !hierarchicalResults[topParent].childrenMap[fieldKey].children
            ) {
              topResult.children.push(hierarchicalResults[topParent].childrenMap[fieldKey])
            }
          })
        }

        orderedResults[topParent] = topResult
      }
    })

    // console.log('原始层级化结果:', hierarchicalResults)
    // console.log('处理顺序:', processOrder)
    // console.log('有序层级化结果:', orderedResults)
    // console.log('最终AI输入:\n', aiInput)

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evalInfo.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value ? new Date().getFullYear() - elderInfo.value.birthDate[0] : ''
        },
        templateInfo: {
          id: evalInfo.templateId || 0,
          name: evalInfo.templateName,
          type: templateType.value,
          validityPeriod: templateDetail.value?.validityPeriod || 3,
          validityUnit: templateDetail.value?.validityUnit || 'month',
          validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
          validityStartTime: templateDetail.value?.validityStartTime || ''
        },
        evaluationInfo: {
          evaluatorId: evalInfo.evaluatorId || 0,
          evaluatorName: evalInfo.evaluatorName,
          evaluationReason: evalInfo.evaluationReason,
          evaluationTime: evalInfo.evaluationTime
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInput,
      hierarchicalResults: orderedResults,
      rawFormData: formData
    }

    // 将完整结果转换为JSON字符串
    // const fullResultJSONString = JSON.stringify(fullResultJSON, null, 2)
    // console.log('完整JSON结果:', fullResultJSONString)

    // if (templateType.value !== 0) {
    //   if (!evaluatorAnalysis.value) {
    //     message.error('评估师分析不能为空')
    //     return
    //   }
    // }

    // 确保options包含正确的模板信息和评分规则
    // 即使没有评分规则，也要确保传递空数组，这样后端可以计算总分
    const templateRule = templateDetail.value?.templateRule || []
    const enhancedOptions = {
      ...options,
      form: {
        ...(options.form || {}),
        templateType: templateType.value,
        templateRule: JSON.stringify(templateRule),
        validityPeriod: templateDetail.value?.validityPeriod || 3,
        validityUnit: templateDetail.value?.validityUnit || 'month',
        validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
        validityStartTime: templateDetail.value?.validityStartTime || ''
      }
    }

    console.log('=== 前端提交调试信息 ===')
    console.log('提交时的模板详情:', templateDetail.value)
    console.log('提交时的评分规则:', templateRule)
    console.log('评分规则是否为空:', templateRule.length === 0)
    console.log('模板类型:', templateType.value)
    console.log('增强后的options:', enhancedOptions)
    console.log('增强后的options.form:', enhancedOptions.form)
    console.log('表单数据:', formData)
    console.log('=========================')

    // 准备评估结果数据
    const resultData = {
      elderId: evalInfo.elderId || 0,
      elderName: elderInfo.value?.name || '',
      templateId: evalInfo.templateId || 0,
      templateName: evalInfo.templateName,
      evaluatorId: evalInfo.evaluatorId || 0,
      evaluatorName: evalInfo.evaluatorName,
      evaluationReason: evalInfo.evaluationReason,
      evaluationTime: new Date().getTime(),
      aiInputs: aiInput,
      aiAnalysis: aiAnalysis.value || '',
      type: evalInfo.type,
      status: 1, // 已提交状态
      evaluatorAnalysis: evaluatorAnalysis.value,
      result: JSON.stringify({
        options: enhancedOptions,
        rules: copyRules,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      })
    }

    // 如果是继续评估模式，则更新现有记录，否则创建新记录
    if (isContinueEvaluation.value && evaluationResultId.value) {
      // 更新评估结果
      await ResultApi.updateResult({
        ...resultData,
        id: evaluationResultId.value
      })
    } else {
      // 创建评估结果
      await ResultApi.createResult(resultData)
    }

    message.success('保存成功')
    // 使用新的标签页跳转函数
    handleEvaluationComplete()
  } catch (error) {
    // console.error('保存评估结果失败:', error)
    message.error('保存失败')
  }
}

// 获取有效期单位文本
const getValidityUnitText = (unit) => {
  switch (unit) {
    case 'day':
      return '天'
    case 'week':
      return '周'
    case 'month':
    default:
      return '个月'
  }
}

// 添加计算剩余有效期的函数
const getRemainingValidity = () => {
  if (
    !templateDetail.value ||
    templateDetail.value.validityStartTimeType !== 'fixedDate' ||
    !templateDetail.value.validityStartTime
  ) {
    return null
  }

  try {
    // 获取固定起始日期
    const startDate = new Date(templateDetail.value.validityStartTime)
    // 获取有效期和单位
    const validityPeriod = templateDetail.value.validityPeriod || 3
    const validityUnit = templateDetail.value.validityUnit || 'month'

    // 计算到期日期
    let expiryDate = new Date(startDate)
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    return daysLeft > 0 ? daysLeft : 0
  } catch (error) {
    // console.error('计算剩余有效期失败:', error)
    ElMessage.error('计算剩余有效期失败')
    return null
  }
}

// 返回上一页
const goBack = () => {
  router.push('/evaluation/result')
}

// 处理完成评估后的页面跳转
const handleEvaluationComplete = () => {
  console.log('=== 单一模板评估：开始处理评估完成后的页面跳转 ===')

  // 保存当前路由信息，因为跳转后route会改变
  const currentRoute = { ...route }
  console.log('当前路由信息:', { path: currentRoute.path, fullPath: currentRoute.fullPath })

  // 先设置刷新标记，确保在跳转前就设置好
  localStorage.setItem('evaluation_result_refresh', 'true')
  localStorage.setItem('evaluation_result_timestamp', new Date().getTime().toString())
  console.log('已设置localStorage刷新标记')

  // 检查评估记录页面的标签页是否已经存在
  const visitedViews = tagsViewStore.getVisitedViews
  console.log('当前已打开的标签页:', visitedViews.map(v => ({ path: v.path, fullPath: v.fullPath })))

  const resultPagePath = '/evaluation/result'
  const existingResultTab = visitedViews.find(view => view.path === resultPagePath)

  console.log('评估记录页面是否已存在:', !!existingResultTab)

  if (existingResultTab) {
    console.log('评估记录页面已存在，先关闭当前标签页，然后跳转到已存在页面')
    // 如果评估记录页面已经打开，先关闭当前标签页，然后跳转到该页面
    closeCurrent(currentRoute, () => {
      console.log('当前评估标签页已关闭，正在跳转到已存在的评估记录页面')
      // 不添加query参数，避免创建新标签页
      router.push(resultPagePath).then(() => {
        console.log('跳转到已存在的评估记录页面成功')
        // 使用nextTick确保页面完全加载后再触发事件
        nextTick(() => {
          // 触发一个自定义事件来通知评估记录页面刷新
          console.log('触发evaluationCompleted事件')
          window.dispatchEvent(new CustomEvent('evaluationCompleted'))

          // 额外触发一个延迟事件，确保页面有足够时间响应
          setTimeout(() => {
            console.log('触发延迟的evaluationCompleted事件')
            window.dispatchEvent(new CustomEvent('evaluationCompleted'))
          }, 100)
        })
      }).catch(error => {
        console.error('跳转到评估记录页面失败:', error)
      })
    })
  } else {
    console.log('评估记录页面不存在，关闭当前标签页后创建新标签页')
    // 如果评估记录页面不存在，先关闭当前标签页，然后跳转创建新标签页
    closeCurrent(currentRoute, () => {
      console.log('当前标签页已关闭，正在跳转到评估记录页面')
      router.push(resultPagePath).then(() => {
        console.log('跳转到新的评估记录页面成功')
        // 新页面会在mounted时自动检查localStorage标记并刷新
      }).catch(error => {
        console.error('跳转到评估记录页面失败:', error)
      })
    })
  }
}

// 切换自动保存
const toggleAutoSave = () => {
  // 禁用自动保存功能
  message.info('自动保存功能已禁用')
}

// 开始自动保存
const startAutoSave = () => {
  // 禁用自动保存功能
  message.info('自动保存功能已禁用')
}

// 停止自动保存
const stopAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
    autoSaveTimer.value = null
  }
}

// 更新自动保存进度
const updateAutoSaveProgress = () => {
  // 禁用自动保存功能
}

// 暂存评估数据
const saveDraft = async (isAutoSave = false) => {
  if (savingDraft.value) return

  savingDraft.value = true
  autoSaveStatus.value = 'success'

  try {
    // 获取表单数据
    const formData = previewApi.value ? previewApi.value.formData() : {}

    // 复制规则以避免修改原始数据
    const copyRules = formCreate.copyRules(templateRule.value)
    const options = templateOption.value

    // 构建AI输入
    let aiInput = `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 构建字段和标题的映射关系
    const fieldTitleMap = {}
    const fieldOptionsMap = {}
    const fieldParentMap = {}
    const fieldMidParentMap = {}
    const fieldTopParentMap = {}

    // 递归函数，用于从规则中提取字段信息
    const extractFieldInfo = (
      rules,
      parentTitle = '',
      midParentTitle = '',
      topParentTitle = ''
    ) => {
      if (!rules || !Array.isArray(rules)) return

      rules.forEach((rule) => {
        // 跳过elAlert类型
        if (rule._fc_drag_tag === 'elAlert') return

        // 获取当前项的标题
        let currentTitle = ''
        if (rule.props?.header) {
          currentTitle = rule.props.header
        } else if (rule.title && rule.title.trim() !== '') {
          currentTitle = rule.title
        } else if (rule.name?.startsWith('ref_') && rule.info && rule.info.trim() !== '') {
          currentTitle = rule.info
        } else if (rule.name && !rule.name.startsWith('ref_')) {
          currentTitle = rule.name
        }

        // 如果有字段，记录它的信息
        if (rule.field) {
          fieldTitleMap[rule.field] = currentTitle || ''
          fieldParentMap[rule.field] = parentTitle || ''
          fieldMidParentMap[rule.field] = midParentTitle || ''
          fieldTopParentMap[rule.field] = topParentTitle || ''

          if (rule.options && rule.options.length > 0) {
            fieldOptionsMap[rule.field] = rule.options
          }
        }

        // 如果有子项，递归处理
        if (rule.children && rule.children.length > 0) {
          // 确定标题的层级关系
          let newTopParent = topParentTitle
          let newMidParent = midParentTitle

          // 如果是顶层卡片
          if (!parentTitle && rule._fc_drag_tag === 'elCard') {
            newTopParent = currentTitle
          }
          // 如果已有顶层父级，但没有中间父级
          else if (topParentTitle && !midParentTitle && rule._fc_drag_tag === 'elCard') {
            newMidParent = currentTitle
          }

          extractFieldInfo(rule.children, currentTitle || parentTitle, newMidParent, newTopParent)
        }
      })
    }

    // 提取字段信息
    extractFieldInfo(copyRules)

    // 将评估结果组织成层级结构，使用数组保持顺序
    const hierarchicalResults = {}

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evalInfo.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value ? new Date().getFullYear() - elderInfo.value.birthDate[0] : ''
        },
        templateInfo: {
          id: evalInfo.templateId || 0,
          name: evalInfo.templateName,
          type: templateType.value,
          validityPeriod: templateDetail.value?.validityPeriod || 3,
          validityUnit: templateDetail.value?.validityUnit || 'month',
          validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
          validityStartTime: templateDetail.value?.validityStartTime || ''
        },
        evaluationInfo: {
          evaluatorId: evalInfo.evaluatorId || 0,
          evaluatorName: evalInfo.evaluatorName,
          evaluationReason: evalInfo.evaluationReason,
          evaluationTime: evalInfo.evaluationTime
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInput,
      hierarchicalResults: hierarchicalResults,
      rawFormData: formData
    }

    // 确保options包含正确的模板信息和评分规则（暂存时也需要）
    const currentTemplateRule = templateDetail.value?.templateRule || []
    const enhancedOptions = {
      ...options,
      form: {
        ...(options.form || {}),
        templateType: templateType.value,
        templateRule: JSON.stringify(currentTemplateRule),
        validityPeriod: templateDetail.value?.validityPeriod || 3,
        validityUnit: templateDetail.value?.validityUnit || 'month',
        validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
        validityStartTime: templateDetail.value?.validityStartTime || ''
      }
    }

    // 准备评估结果数据
    const resultData = {
      elderId: evalInfo.elderId || 0,
      elderName: elderInfo.value?.name || '',
      templateId: evalInfo.templateId || 0,
      templateName: evalInfo.templateName,
      evaluatorId: evalInfo.evaluatorId || 0,
      evaluatorName: evalInfo.evaluatorName,
      evaluationReason: evalInfo.evaluationReason,
      evaluationTime: new Date().getTime(),
      aiInputs: aiInput,
      aiAnalysis: aiAnalysis.value || '',
      type: evalInfo.type,
      status: 2, // 暂存状态
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      result: JSON.stringify({
        options: enhancedOptions, // 使用增强后的options
        rules: copyRules,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      })
    }

    // 如果是继续评估模式，则更新现有记录，否则创建新记录
    if (isContinueEvaluation.value && evaluationResultId.value) {
      // 更新评估结果
      await ResultApi.updateResult({
        ...resultData,
        id: evaluationResultId.value
      })
    } else {
      // 创建评估结果
      const newResultId = await ResultApi.createResult(resultData)
      // 保存新创建的评估结果ID，以便后续更新操作
      evaluationResultId.value = newResultId
      isContinueEvaluation.value = true
    }

    if (!isAutoSave) {
      message.success('暂存成功')

      // 在localStorage中设置标记，表示需要刷新评估记录页面
      localStorage.setItem('evaluation_result_refresh', 'true')
      localStorage.setItem('evaluation_result_timestamp', new Date().getTime().toString())

      // 使用新的标签页跳转函数
      handleEvaluationComplete()
    } else {
      console.log('自动保存成功:', new Date().toLocaleTimeString())
    }
  } catch (error) {
    console.error('暂存失败:', error)
    if (!isAutoSave) {
      message.error('暂存失败: ' + (error.response?.data?.msg || error.message))
    }
  } finally {
    savingDraft.value = false
    if (isAutoSave) {
      autoSaveStatus.value = ''
    }
  }
}

// AI分析对话框相关变量
const dialogVisible = ref(false)
const finishDialogVisible = ref(false)
const isAnalysisFinish = ref(false)
const isReasoningCollapsed = ref([]) // 默认展开分析过程
const aiReasoning = ref('')
const tempReasoning = ref('')
const tempAnalysis = ref('')
const aiResult = ref('')

// 声明函数引用
let useSimulatedAIAnalysis: () => Promise<void>

// 生成AI分析
const generateAIAnalysis = async () => {
  if (generatingAI.value) return

  generatingAI.value = true

  try {
    // 检查是否选择了AI模型
    if (!selectedModelId.value) {
      message.error('请先选择AI模型')
      generatingAI.value = false
      return
    }

    message.info('正在生成AI分析，请稍候...')

    // 准备AI输入数据基本信息
    let aiInputData = `老人信息：\n姓名：${elderInfo.value?.name}，身份证号：${elderInfo.value?.idNumber}，性别：${elderInfo.value?.gender === 1 ? '男' : '女'}，出生日期：${elderInfo.value?.birthDate ? (Array.isArray(elderInfo.value.birthDate) ? elderInfo.value.birthDate.join('-') : elderInfo.value.birthDate) : ''}\n`
    aiInputData += `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 获取表单数据
    let formData = {}
    try {
      if (previewApi.value && typeof previewApi.value.formData === 'function') {
        formData = previewApi.value.formData()
      }
    } catch (error) {
      console.error('获取表单数据失败:', error)
    }

    // 调用后端接口转换表单数据为可读文本
    try {
      // 将表单规则转换为JSON字符串
      const formRuleJson = JSON.stringify(templateRule.value || [])

      // 调用后端接口
      const convertResponse = await ResultApi.convertFormData({
        formData: JSON.stringify(formData),
        formRule: formRuleJson
      })

      // 如果转换成功，使用转换后的文本
      if (convertResponse) {
        aiInputData += convertResponse
      } else {
        // 如果转换失败，使用JSON格式
        aiInputData += `评估数据：\n${JSON.stringify(formData, null, 2)}`
      }
    } catch (error) {
      console.error('转换表单数据失败:', error)
      // 如果转换失败，使用JSON格式
      aiInputData += `评估数据：\n${JSON.stringify(formData, null, 2)}`
    }

    // 显示对话框，让用户知道正在处理
    finishDialogVisible.value = true
    tempReasoning.value = "正在分析数据，请稍候..."

    // 调用后端AI分析接口
    const controller = new AbortController()
    let currentContent = ''

    try {
      await AiEvaluationApi.generateAnalysisStream({
        data: {
          modelId: selectedModelId.value,
          content: aiInputData,
          evaluationResultId: evaluationResultId.value,
          stream: true
        },
        onMessage: (event) => {
          try {
            const data = JSON.parse(event.data)
            if (data.code === 0 && data.data) {
              const content = data.data.content || ''
              currentContent += content

              // 检查是否包含 [think] 和 [/think] 标签
              if (currentContent.includes('[/think]')) {
                const parts = currentContent.split('[/think]')
                if (parts.length === 2) {
                  // 提取 think 标签中的内容
                  tempReasoning.value = parts[0].replace('[think]', '').trim()
                  // 提取 think 标签后的内容
                  tempAnalysis.value = parts[1].trim()
                } else {
                  // 如果分割失败，将所有内容作为分析结果
                  tempAnalysis.value = currentContent
                  tempReasoning.value = ''
                }
              } else {
                // 如果没有标签，将所有内容作为分析结果
                tempAnalysis.value = currentContent
                tempReasoning.value = ''
              }

              // 检查是否完成
              if (data.data.finished) {
                isAnalysisFinish.value = true
                aiReasoning.value = tempReasoning.value
                aiAnalysis.value = tempAnalysis.value
                isReasoningCollapsed.value = ['1']

                // 保存AI分析到后端
                if (evaluationResultId.value && aiAnalysis.value) {
                  ResultApi.updateAiAnalysis({
                    id: evaluationResultId.value,
                    aiAnalysis: `[think]${aiReasoning.value}[/think]\n` + aiAnalysis.value
                  }).then(() => {
                    message.success('AI分析生成成功')
                  }).catch((error) => {
                    console.error('保存AI分析失败:', error)
                    message.warning('AI分析生成成功，但保存失败')
                  })
                }

                generatingAI.value = false
              }
            }
          } catch (error) {
            console.error('解析AI响应失败:', error)
          }
        },
        onError: (error) => {
          console.error('AI分析流式请求失败:', error)
          message.error('AI分析生成失败，请稍后重试')
          generatingAI.value = false
          finishDialogVisible.value = false
        },
        onClose: () => {
          console.log('AI分析流式连接关闭')
          if (!isAnalysisFinish.value) {
            generatingAI.value = false
          }
        },
        ctrl: controller
      })
    } catch (error) {
      console.error('调用AI分析接口失败:', error)
      message.error('AI分析生成失败，请稍后重试')
      generatingAI.value = false
      finishDialogVisible.value = false
    }
  } catch (error) {
    console.error('生成AI分析失败:', error)
    message.error('生成AI分析失败')
    generatingAI.value = false
  }
}

// 使用模拟数据生成AI分析
useSimulatedAIAnalysis = async () => {
  // 生成示例AI分析内容
  const analysis = `# 评估分析报告\n\n## 基本情况\n老人 **${elderInfo.value?.name}** 进行了${evalInfo.templateName}评估。\n\n## 评估结果\n根据评估数据分析，老人的主要情况如下：\n\n1. 生活自理能力良好\n2. 认知功能正常\n3. 情绪状态稳定\n\n## 建议\n建议针对老人的情况，提供以下服务：\n\n- 定期健康检查\n- 社交活动参与\n- 适当的体育锻炼`

  // 模拟思考过程
  aiReasoning.value = "分析老人的评估数据...\n根据评估表单中的数据，老人的生活自理能力、认知功能和情绪状态都处于正常范围。"

  // 更新AI分析内容
  aiAnalysis.value = analysis
  tempAnalysis.value = analysis
  isAnalysisFinish.value = true
  finishDialogVisible.value = true
  isReasoningCollapsed.value = ['1']

  // 如果有评估结果ID，则保存AI分析到后端
  if (evaluationResultId.value) {
    await ResultApi.updateAiAnalysis({
      id: evaluationResultId.value,
      aiAnalysis: `[think]${aiReasoning.value}[/think]\n` + analysis
    })
    message.success('AI分析生成成功')
  } else {
    // 如果没有评估结果ID，只在本地保存，不调用后端API
    message.success('AI分析已生成，可以在提交评估时一并保存')
  }

  generatingAI.value = false
}

// 查看AI分析
const viewAIAnalysis = () => {
  if (aiAnalysis.value) {
    finishDialogVisible.value = true
    isReasoningCollapsed.value = ['1']
  } else {
    message.warning('暂无AI分析结果，请先生成AI分析')
  }
}

// 保存AI分析
const saveAIAnalysis = async () => {
  if (!aiAnalysis.value) {
    message.warning('暂无AI分析结果，请先生成AI分析')
    return
  }

  try {
    // 如果有评估结果ID，则调用API保存AI分析
    if (evaluationResultId.value) {
      const response = await ResultApi.updateAiAnalysis({
        id: evaluationResultId.value,
        aiAnalysis: `[think]${aiReasoning.value}[/think]\n` + aiAnalysis.value
      })

      if (response) {
        message.success('AI分析保存成功')
      } else {
        message.error('保存失败，请重试')
      }
    } else {
      // 如果没有评估结果ID，只在本地保存，不调用后端API
      message.success('AI分析已生成，可以在提交评估时一并保存')
    }
  } catch (error) {
    console.error('保存AI分析失败:', error)
    message.error('保存失败，请重试')
  }
}

// 格式化AI分析内容为HTML
const formattedAIAnalysis = computed(() => {
  if (!aiAnalysis.value) return ''
  return marked(aiAnalysis.value)
})

// 渲染AI结果
const renderedAiResult = computed(() => {
  return marked(aiAnalysis.value)
})

// 渲染临时结果
const renderedTempResult = computed(() => {
  return marked(tempAnalysis.value)
})

// 声明引用
const resultContainer = ref(null)
const reasoningContainer = ref(null)

// 监听tempAnalysis的变化，确保内容更新时自动滚动到底部
watch(
  () => tempAnalysis.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (resultContainer.value) {
          // 自动滚动到底部，确保用户能看到最新内容
          resultContainer.value.scrollTop = resultContainer.value.scrollHeight
        }
      })
    }
  }
)

// 添加对tempReasoning的监听
watch(
  () => tempReasoning.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (reasoningContainer.value) {
          // 自动滚动到底部，确保用户能看到最新思考内容
          reasoningContainer.value.scrollTop = reasoningContainer.value.scrollHeight
        }
      })
    }
  }
)

// 初始化
onMounted(() => {
  // 加载模板类型选项
  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  console.log('模板类型选项:', templateTypeOptions.value)

  // 加载模板和评估信息
  loadTemplate()
  loadEvalInfo()

  // 加载AI模型列表
  loadAiModels()
})

// 组件销毁前清理
onBeforeUnmount(() => {
  stopAutoSave()
})

// 工具函数：脱敏处理
const maskString = (str: string, start: number, end: number) => {
  if (!str) return ''
  const maskLength = end - start
  const maskStr = '*'.repeat(maskLength)
  return str.substring(0, start) + maskStr + str.substring(end)
}

// 计算属性：脱敏后的身份证号
const maskedIdNumber = computed(() => {
  if (!elderInfo.value?.idNumber) return ''
  return maskString(elderInfo.value.idNumber, 6, 14)
})

// 计算属性：脱敏后的联系电话
const maskedPhone = computed(() => {
  if (!elderInfo.value?.contactPhone) return ''
  return maskString(elderInfo.value.contactPhone, 3, 7)
})
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="flex flex-1">
      <div class="flex-1 mr-4">
        <el-card class="min-h-83vh">
          <div class="flex justify-between items-center mb-4">
            <h2>{{ evalInfo.templateName }}</h2>
            <div class="action-buttons flex gap-2">
              <el-button
                type="warning"
                @click="() => saveDraft(false)"
                :loading="savingDraft"
              >
                暂存
              </el-button>
              <el-button
                type="primary"
                @click="() => {
                  if (previewApi && typeof previewApi.formData === 'function') {
                    try {
                      handleSubmit(previewApi.formData())
                    } catch (error) {
                      console.error('获取表单数据失败:', error)
                      message.error('提交失败，无法获取表单数据')
                    }
                  } else {
                    message.error('提交失败，表单API未初始化')
                  }
                }"
              >
                提交
              </el-button>
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2 mb-2">
            <span class="mr-4">表单类型: {{ getTemplateTypeName }}</span>
            <span class="mr-4">版本号: {{ templateDetail.version || '1.0.0' }}</span>
            <span>
              <template
                v-if="
                  templateDetail.validityStartTimeType === 'fixedDate' &&
                  templateDetail.validityStartTime
                "
              >
                剩余有效期: {{ getRemainingValidity() }} 天 (总有效期
                {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }}，从
                {{ templateDetail.validityStartTime }}
                起)
              </template>
              <template v-else>
                有效期: {{ templateDetail.validityPeriod || 3 }}
                {{ getValidityUnitText(templateDetail.validityUnit || 'month') }} (从评估时间起)
              </template>
            </span>
          </div>
          <el-divider />
          <div class="evaluation-form" style="max-height: 69vh; overflow-y: auto">
            <form-create
              :modelValue="previewForm"
              v-model:api="previewApi"
              :rule="templateRule"
              :option="templateOption"
              @submit="handleSubmit"
            />
          </div>
        </el-card>
      </div>

      <!-- 老人信息 -->
      <el-card class="bg-white p-3 rounded-lg w-110 min-h-83vh">
        <!-- 评估信息 -->
        <div class="eval-info">
          <div class="info-item">
            <span class="label">评估师：</span>
            <span class="value">{{ evalInfo.evaluatorName }}</span>
          </div>
          <div class="info-item">
            <span class="label">评估原因：</span>
            <span class="value">{{ evalInfo.evaluationReason }}</span>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <!-- <div class="w-24 h-24 rounded-full overflow-hidden mb-4">
            <img
              src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
              class="w-full h-full object-cover"
              alt="老人头像"
            />
          </div>
          <h3 class="text-lg font-medium">{{ elderName }}</h3> -->
        </div>
        <div class="mt-1 space-y-4">
          <div class="flex justify-between">
            <span>老人姓名:</span>
            <span>{{ elderInfo?.name }}</span>
          </div>
          <div class="flex justify-between">
            <span>年龄:</span>
            <span>{{ elderInfo ? new Date().getFullYear() - elderInfo.birthDate[0] : '' }}岁</span>
          </div>
<!--          <div class="flex justify-between">-->
<!--            <span>身份证号:</span>-->
<!--            <span>{{ elderInfo?.idNumber ? elderInfo.idNumber.substring(0, 6) + '********' + elderInfo.idNumber.substring(14) : '' }}</span>-->
<!--          </div>-->
          <div class="flex justify-between">
            <span>性别:</span>
            <span>{{ elderInfo?.gender === 1 ? '男' : '女' }}</span>
          </div>
          <div class="flex justify-between">
            <span>出生日期:</span>
            <span>{{
              elderInfo?.birthDate
                ? Array.isArray(elderInfo.birthDate)
                  ? elderInfo.birthDate.join('-')
                  : formatDate(elderInfo.birthDate)
                : ''
            }}</span>
          </div>
<!--          <div class="flex justify-between">-->
<!--            <span>联系电话:</span>-->
<!--            <span>{{ elderInfo?.contactPhone ? elderInfo.contactPhone.substring(0, 3) + '****' + elderInfo.contactPhone.substring(7) : '' }}</span>-->
<!--          </div>-->
        </div>
        <el-divider />
<!--        <div>-->
<!--          &lt;!&ndash; 评估师分析 - 仅在 templateType 为 1(评估表)或 2(量表)时显示 &ndash;&gt;-->
<!--          <div v-if="templateType === 1 || templateType === 2" class="mt-4">-->
<!--            <div class="mb-2">评估师分析</div>-->
<!--            <div class="rounded-lg text-sm">-->
<!--              <div class="">-->
<!--                <el-input-->
<!--                  type="textarea"-->
<!--                  v-model="evaluatorAnalysis"-->
<!--                  :rows="3"-->
<!--                  placeholder="请输入评估师分析"-->
<!--                />-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->

<!--          &lt;!&ndash; AI分析区域 &ndash;&gt;-->
<!--          <div class="mt-4">-->
<!--            <div class="mb-3">-->
<!--              <div class="mb-2">-->
<!--                <div class="text-lg font-bold">-->
<!--                  AI 分析-->
<!--                </div>-->
<!--              </div>-->
<!--              &lt;!&ndash; AI模型选择 &ndash;&gt;-->
<!--              <div class="mb-3">-->
<!--                <el-select-->
<!--                  v-model="selectedModelId"-->
<!--                  placeholder="选择AI模型"-->
<!--                  style="width: 200px"-->
<!--                  size="small"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="model in aiModelList"-->
<!--                    :key="model.id"-->
<!--                    :label="model.name"-->
<!--                    :value="model.id"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </div>-->
<!--              <div class="flex space-x-2">-->
<!--                <el-button plain type="primary" @click="finishDialogVisible = true" v-if="aiAnalysis !== ''">-->
<!--                  查看-->
<!--                </el-button>-->
<!--                <el-button plain type="primary" @click="generateAIAnalysis" :loading="generatingAI">-->
<!--                  生成AI意见-->
<!--                </el-button>-->
<!--                <el-button plain type="success" @click="saveAIAnalysis"> 保存 </el-button>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="bg-gray-50 pl-6 pt-3 pb-3 rounded-lg text-sm leading-6">-->
<!--              <div-->
<!--                class="text-gray-700"-->
<!--                v-html="marked(aiAnalysis)"-->
<!--                v-if="aiAnalysis !== ''"-->
<!--                style="max-height: 28vh; overflow-y: auto"-->
<!--              >-->
<!--              </div>-->
<!--              <div class="text-gray-700" v-else>暂无AI分析结果</div>-->
<!--            </div>-->
<!--          </div>-->


<!--        </div>-->
      </el-card>
    </div>
  </div>

  <!-- AI分析结果对话框 -->
  <el-dialog v-model="finishDialogVisible" title="AI 分析结果" width="70%">
    <div>
      <el-card class="custom-collapse-card reasoning-card" shadow="hover">
        <template #header>
          <div
            class="custom-collapse-header"
            @click="isReasoningCollapsed = isReasoningCollapsed.includes('1') ? [] : ['1']"
          >
            <div class="flex justify-between w-full pr-4">
              <div class="flex items-center">
                <h3 class="m-0">分析过程</h3>
                <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                  <el-icon class="ml-1">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">
                  {{ isReasoningCollapsed.includes('1') ? '点击展开' : '点击折叠' }}
                </span>
                <el-icon
                  class="transition-all"
                  :class="isReasoningCollapsed.includes('1') ? 'transform rotate-180' : ''"
                >
                  <ArrowDown />
                </el-icon>
              </div>
            </div>
          </div>
        </template>
        <div
          class="custom-collapse-content"
          :class="{ hidden: isReasoningCollapsed.includes('1') }"
        >
          <div class="reasoning-container">
            <div
              class="text-wrap text-base whitespace-pre-wrap reasoning-content"
              ref="reasoningContainer"
            >
              {{ isAnalysisFinish ? aiReasoning : tempReasoning }}
            </div>
          </div>
        </div>
      </el-card>

      <el-divider />

      <el-card class="result-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="flex items-center">
              <h3>分析结果</h3>
              <el-tooltip content="AI分析的结论" placement="top" class="ml-2">
                <el-icon class="ml-1">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </div>
        </template>
        <div class="result-container">
          <div
            v-html="marked(isAnalysisFinish ? aiAnalysis : tempAnalysis)"
            class="text-wrap leading-7 text-base result-content"
            ref="resultContainer"
          ></div>
        </div>
      </el-card>
    </div>
    <template #footer>
      <el-button @click="finishDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;

  .action-buttons {
    display: flex;
    gap: 10px;
  }

  .auto-save-progress {
    width: 80px;
    margin-left: 10px;
  }
}

.eval-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 5px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
      font-size: 14px;
    }

    .value {
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.ai-analysis {
  min-height: 150px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;

  .ai-content {
    max-height: 300px;
    overflow-y: auto;

    :deep(h1) {
      font-size: 1.5rem;
      margin-top: 0.5rem;
      margin-bottom: 1rem;
    }

    :deep(h2) {
      font-size: 1.25rem;
      margin-top: 0.5rem;
      margin-bottom: 0.75rem;
    }

    :deep(ul) {
      padding-left: 1.5rem;
    }
  }

  .no-ai {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
  }
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.reasoning-card,
.result-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reasoning-container,
  .result-container {
    height: 350px;
    overflow-y: auto;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
  }

  .reasoning-content,
  .result-content {
    animation: fadeIn 0.5s ease-in-out;
    line-height: 1.6;
  }
}

.result-card {
  .result-container {
    background-color: #f0f9eb;

    &:hover {
      background-color: #e6f7df;
    }
  }

  .result-content {
    padding: 0 12px;

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
      color: #303133;
    }

    :deep(p) {
      margin-bottom: 12px;
    }

    :deep(ul),
    :deep(ol) {
      padding-left: 24px;
      margin-bottom: 12px;
    }

    :deep(blockquote) {
      padding: 0 12px;
      color: #606266;
      border-left: 4px solid #dcdfe6;
      margin: 16px 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
  }

  to {
    opacity: 1;
  }
}

.custom-collapse-card {
  .custom-collapse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
    padding: 8px 0px;
    border-radius: 4px;
  }

  .custom-collapse-content {
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    &.hidden {
      max-height: 0;
      padding: 0;
      opacity: 0;
    }
  }
}
</style>
