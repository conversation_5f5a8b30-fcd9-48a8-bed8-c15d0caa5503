<template>
  <div class="list-selector">
    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <el-form :model="queryParams" inline>
        <el-form-item label="清单名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入清单名称"
            clearable
            style="width: 180px;"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="清单类型">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择清单类型"
            clearable
            style="width: 180px;"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATE_LIST_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option
              v-for="dict in listStatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 清单列表区域 -->
    <div class="list-list" v-loading="loading">
      <el-row :gutter="20">
        <el-col
          v-for="item in listData"
          :key="item.id"
          :span="6"
          class="mb-20"
        >
          <el-card
            class="list-card"
            shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <el-tooltip :content="item.name" placement="top" :show-after="500">
                  <span class="list-name">{{ item.name }}</span>
                </el-tooltip>
                <div class="list-badges">
                  <el-tag size="small" type="info" style="white-space: nowrap;">
                    {{ getListTypeName(item.type) }}
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="item.status === 0 ? 'success' : 'danger'"
                    class="ml-5"
                    style="white-space: nowrap;"
                  >
                    {{ getListStatusName(item.status) }}
                  </el-tag>
                </div>
              </div>
            </template>
            <div class="list-content">
              <div class="list-info">
                <p class="list-description" v-if="item.description">
                  {{ item.description }}
                </p>
                <p class="list-description" v-else>
                  <span class="text-muted">无描述</span>
                </p>
                <p class="list-templates">
                  包含模板数量: {{ getTemplateCount(item) }}
                </p>
                <p class="list-time">创建时间: {{ formatDate(item.createTime) }}</p>
              </div>
              <div class="list-actions">
                <el-button
                  type="primary"
                  link
                  @click.stop="previewList(item)"
                >
                  预览
                </el-button>
                <el-button
                  type="primary"
                  @click="startEvaluation(item)"
                >
                  评估
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          :page-size="8"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 清单预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="清单预览"
      width="60%"
      destroy-on-close
      :close-on-click-modal="false"
      @closed="handlePreviewClose"
    >
      <div class="preview-form" v-if="previewListData" v-loading="previewLoading">
        <div class="list-info">
          <h3>{{ previewListData.name }}</h3>
          <div class="list-meta">
            <el-tag size="small" type="info" class="mr-10">类型: {{ getListTypeName(previewListData.type) }}</el-tag>
            <el-tag size="small" :type="previewListData.status === 0 ? 'success' : 'danger'" class="mr-10">
              状态: {{ getListStatusName(previewListData.status) }}
            </el-tag>
          </div>

          <p v-if="previewListData.description" class="list-description">
            描述: {{ previewListData.description }}
          </p>

          <el-divider content-position="left">包含的评估模板</el-divider>

          <div class="templates-preview">
            <div v-if="previewTemplates.length > 0">
              <el-table :data="previewTemplates" style="width: 100%" border stripe>
                <el-table-column prop="name" label="模板名称" min-width="200" />
                <el-table-column prop="version" label="版本" width="100" />
                <el-table-column prop="type" label="类型" width="120">
                  <template #default="scope">
                    {{ getTemplateTypeName(scope.row.type) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100" align="center">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'" size="small">
                      {{ getTemplateStatusName(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template #default="scope">
                    <el-button type="primary" link size="small" @click="previewTemplate(scope.row)">
                      预览
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="no-templates">
              <el-empty description="此清单未包含任何模板" />
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="清单数据加载失败" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="templatePreviewVisible"
      title="模板预览"
      width="60%"
      destroy-on-close
      :close-on-click-modal="false"
      @closed="handleTemplatePreviewClose"
    >
      <div class="preview-form" v-if="previewTemplateData" v-loading="templatePreviewLoading">
        <div class="template-info">
          <h3>{{ previewTemplateData.name }}</h3>
          <div class="template-meta">
            <el-tag size="small" type="info" class="mr-10">版本: {{ previewTemplateData.version || '无版本号' }}</el-tag>
            <el-tag size="small" type="success" class="mr-10">类型: {{ getTemplateTypeName(previewTemplateData.type) }}</el-tag>
            <el-tag size="small" :type="previewTemplateData.status === 0 ? 'success' : 'danger'">
              状态: {{ getTemplateStatusName(previewTemplateData.status) }}
            </el-tag>
          </div>

          <el-divider content-position="left">表单预览</el-divider>

          <form-create
            v-if="previewTemplateData.formSchema && templateRule.length > 0"
            v-model:value="previewForm"
            v-model:api="previewApi"
            :rule="templateRule"
            :option="templateOption"
          />
          <div v-else class="no-schema">
            <el-empty description="暂无表单结构或表单结构解析失败" />
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="模板数据加载失败" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templatePreviewVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 老人选择对话框 -->
    <el-dialog
      v-model="elderDialogVisible"
      title="选择评估老人"
      width="600px"
      destroy-on-close
    >
      <el-form :model="elderForm" label-width="100px">
        <el-form-item label="老人" prop="elderId" required>
          <el-select
            v-model="elderForm.elderId"
            filterable
            placeholder="请选择老人"
            style="width: 100%"
            @change="handleElderChange"
          >
            <el-option
              v-for="item in elderList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评估原因" prop="evaluationReason" required>
          <el-select v-model="elderForm.evaluationReason" placeholder="请选择评估原因" style="width: 100%">
            <el-option label="首次评估" value="首次评估" />
            <el-option label="定期评估" value="定期评估" />
            <el-option label="状态变化评估" value="状态变化评估" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="elderDialogVisible = false">取消</el-button>
          <el-button type="primary" :disabled="!elderForm.elderId || !elderForm.evaluationReason" @click="confirmElder">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineProps, defineExpose, watch } from 'vue'
import { ListApi } from '@/api/evaluation/list'
import { TemplateApi } from '@/api/evaluation/template'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useMessage } from '@/hooks/web/useMessage'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ListExecutionApi } from '@/api/evaluation/listexecution'
import { ElMessage } from 'element-plus'
import formCreate from '@form-create/element-ui'

defineOptions({ name: 'ListSelector' })

const props = defineProps({
  selectedList: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:selectedList', 'list-selected'])

// 消息提示
const message = useMessage()
const router = useRouter()
const userStore = useUserStore()

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 8, // 固定显示8个卡片（2行x4列）
  name: '',
  type: undefined, // 改为undefined，匹配数字类型
  status: 0 // 默认只显示启用的清单
})

// 老人选择对话框
const elderDialogVisible = ref(false)
const elderList = ref<any[]>([])
const elderForm = ref({
  elderId: undefined as number | undefined,
  elderName: '',
  evaluationReason: ''
})
const currentList = ref<any>(null)

// 清单状态选项
const listStatusOptions = ref(getIntDictOptions(DICT_TYPE.EVALUATION_LIST_STATUS))

// 清单列表数据
const listData = ref([])
const total = ref(0)
const loading = ref(false)

// 模板数据缓存
const templateMap = ref({})

// 清单预览相关
const previewDialogVisible = ref(false)
const previewListData = ref(null)
const previewTemplates = ref([])
const previewLoading = ref(false)

// 模板预览相关
const templatePreviewVisible = ref(false)
const previewTemplateData = ref(null)
const templatePreviewLoading = ref(false)
const previewForm = ref({})
const previewApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
  form: {
    labelPosition: 'top',
    disabled: true // 预览模式下禁用表单
  },
  submitBtn: false // 不显示提交按钮
})

// 判断清单是否被选中
const isSelected = (list) => {
  return props.selectedList && props.selectedList.id === list.id
}

// 选择清单
const selectList = (list) => {
  emit('update:selectedList', list)
  emit('list-selected', list)
}

// 获取清单包含的模板数量
const getTemplateCount = (list) => {
  if (!list.templateIds) return 0
  return list.templateIds.split(',').length
}

// 获取模板类型名称
const getTemplateTypeName = (type: number) => {
  // 使用字典获取模板类型选项
  const templateTypeOptions = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  const item = templateTypeOptions.find((d: any) => d.value === type)
  return item ? item.label : '未知类型'
}

// 获取清单类型名称
const getListTypeName = (type: number) => {
  const listTypeOptions = getIntDictOptions(DICT_TYPE.EVALUATE_LIST_TYPE)
  const item = listTypeOptions.find((d: any) => d.value === type)
  return item ? item.label : '未知类型'
}

// 获取清单状态名称
const getListStatusName = (status: number) => {
  const item = listStatusOptions.value.find((d: any) => d.value === status)
  return item ? item.label : '未知状态'
}

// 获取模板状态名称
const getTemplateStatusName = (status: number) => {
  const templateStatusOptions = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_STATUS)
  const item = templateStatusOptions.find((d: any) => d.value === status)
  return item ? item.label : '未知状态'
}

// 查询清单列表
const getListData = async () => {
  loading.value = true
  try {
    const res = await ListApi.getListPage(queryParams)
    listData.value = res.list
    total.value = res.total
  } catch (error) {
    console.error('获取清单列表失败:', error)
    message.error('获取清单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取模板列表
const getTemplateList = async () => {
  try {
    // 使用getTemplatePage替代getSimpleTemplateList，获取完整的模板信息
    const res = await TemplateApi.getTemplatePage({
      pageNo: 1,
      pageSize: 100,
      status: 0 // 只获取启用状态的模板
    })
    // 将模板列表转换为id映射
    templateMap.value = res.list.reduce((acc, template) => {
      acc[template.id] = template
      return acc
    }, {})
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

// 预览清单
const previewList = async (list) => {
  previewDialogVisible.value = true
  previewLoading.value = true
  previewListData.value = list
  previewTemplates.value = []

  try {
    // 如果模板映射为空，先获取模板列表
    if (Object.keys(templateMap.value).length === 0) {
      await getTemplateList()
    }

    // 获取清单包含的模板
    if (list.templateIds) {
      const templateIds = list.templateIds.split(',')

      // 对于每个模板ID，获取完整的模板信息
      const templatePromises = templateIds.map(async (id) => {
        // 如果模板映射中已有完整信息，直接使用
        if (templateMap.value[id] && templateMap.value[id].version && templateMap.value[id].type !== undefined) {
          return templateMap.value[id]
        }

        // 否则，获取完整的模板信息
        try {
          const template = await TemplateApi.getTemplate(Number(id))
          // 更新模板映射
          templateMap.value[id] = template
          return template
        } catch (err) {
          console.error(`获取模板ID=${id}的详情失败:`, err)
          return null
        }
      })

      // 等待所有模板信息获取完成
      const templates = await Promise.all(templatePromises)
      previewTemplates.value = templates.filter(template => template) // 过滤掉不存在的模板
    }
  } catch (error) {
    console.error('获取清单详情失败:', error)
    message.error('获取清单详情失败')
  } finally {
    previewLoading.value = false
  }
}

// 预览模板
const previewTemplate = async (template) => {
  templatePreviewVisible.value = true
  templatePreviewLoading.value = true
  previewTemplateData.value = null
  templateRule.value = []

  try {
    // 获取完整的模板信息
    const res = await TemplateApi.getTemplate(template.id)
    previewTemplateData.value = res

    // 解析模板数据
    if (res.formSchema) {
      try {
        const schema = JSON.parse(res.formSchema)
        templateRule.value = schema.rule || []
        // 可以选择是否使用保存的 option
        // templateOption.value = { ...templateOption.value, ...schema.option }
      } catch (error) {
        console.error('解析模板数据失败:', error)
        ElMessage.error('模板数据格式错误')
        templateRule.value = []
      }
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    ElMessage.error('获取模板详情失败')
  } finally {
    templatePreviewLoading.value = false
  }
}

// 处理清单预览对话框关闭
const handlePreviewClose = () => {
  // 清空预览相关的数据
  previewListData.value = null
  previewTemplates.value = []
}

// 处理模板预览对话框关闭
const handleTemplatePreviewClose = () => {
  // 清空预览相关的数据
  previewTemplateData.value = null
  previewForm.value = {}
  previewApi.value = null
  templateRule.value = []
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getListData()
}

// 重置
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = undefined
  queryParams.status = 0
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  getListData()
}

// 获取老人列表
const getElderList = async () => {
  try {
    const res = await ArchivesProfileApi.getArchivesProfileSimpleList()
    elderList.value = res
  } catch (error) {
    console.error('获取老人列表失败:', error)
    message.error('获取老人列表失败')
  }
}

// 开始评估
const startEvaluation = (list: any) => {
  currentList.value = list
  elderDialogVisible.value = true
}

// 处理老人选择变化
const handleElderChange = (id: any) => {
  const elder = elderList.value.find(item => item.id === id)
  if (elder) {
    elderForm.value.elderName = elder.name
  }
}

// 确认老人选择
const confirmElder = async () => {
  if (!elderForm.value.elderId || !elderForm.value.evaluationReason) {
    message.warning('请选择老人和评估原因')
    return
  }

  elderDialogVisible.value = false

  // 创建评估任务清单执行记录
  await createListExecution()

  // 重置表单
  elderForm.value = {
    elderId: undefined,
    elderName: '',
    evaluationReason: ''
  }
}

// 创建评估任务清单执行记录
const createListExecution = async () => {
  try {
    // 确保elderId是数字类型
    if (!elderForm.value.elderId) {
      message.error('请选择老人')
      return
    }

    // 尝试获取第一个模板的API密钥
    let apiKey = ''
    if (currentList.value.templateIds) {
      const templateIds = currentList.value.templateIds.split(',')
      if (templateIds.length > 0) {
        try {
          const firstTemplateId = Number(templateIds[0])
          if (!isNaN(firstTemplateId)) {
            const apiKeyRes = await TemplateApi.getTemplateApiKey(firstTemplateId)
            if (apiKeyRes) {
              apiKey = apiKeyRes
            }
          }
        } catch (error) {
          console.error('获取模板API密钥失败:', error)
        }
      }
    }

    // 创建评估任务清单执行记录
    const executionData = {
      listId: currentList.value.id,
      listName: currentList.value.name,
      elderId: Number(elderForm.value.elderId), // 确保是数字类型
      elderName: elderForm.value.elderName,
      evaluatorId: userStore.user.id,
      evaluatorName: userStore.user.nickname,
      evaluationReason: elderForm.value.evaluationReason,
      status: 0,
      startTime: new Date().getTime(), // 使用时间戳
      requiredTemplateIds: currentList.value.templateIds,
      apiKey: apiKey // 设置API密钥
    }

    const result = await ListExecutionApi.createListExecution(executionData)

    // 跳转到步骤式评估页面
    router.push({
      path: '/evaluation/listEval',
      query: {
        id: result,
        fromApplication: 'true'
      }
    })
  } catch (error) {
    console.error('创建评估任务清单执行记录失败:', error)
    message.error('创建评估任务清单执行记录失败')
  }
}

// 暴露方法
defineExpose({
  getListData,
  resetQuery
})

// 初始化
getListData()
getTemplateList()
getElderList()
</script>

<style lang="scss" scoped>
.list-selector {
  .filter-container {
    margin-bottom: 20px;
  }

  .list-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .list-card {
      min-height: 220px; /* 增加最小高度，确保有足够空间显示内容 */
      transition: all 0.3s;
      border: 2px solid #e4e7ed; /* 添加明显的边框 */
      display: flex;
      flex-direction: column;
      width: 100%; /* 确保卡片宽度为100% */
      box-sizing: border-box; /* 确保边框不会增加宽度 */

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary); /* 悬停时边框变为主色调 */
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .list-name {
          font-weight: bold;
          font-size: 16px;
          width: 100%;
          margin-bottom: 8px;
          /* 移除文本溢出省略 */
          /* overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap; */
          word-break: break-word; /* 允许在任何字符间换行 */
          line-height: 1.3;
        }

        .list-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap; /* 防止标签换行 */
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .list-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .list-info {
          margin-bottom: 10px;
          flex: 1;

          p {
            margin: 5px 0;
            color: #606266;
            font-size: 14px;
          }

          .list-description {
            /* 移除文本溢出省略 */
            /* overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical; */
            min-height: 40px;
            word-break: break-word; /* 允许在任何字符间换行 */
            line-height: 1.3;
          }

          .text-muted {
            color: #909399;
            font-style: italic;
          }
        }

        .list-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
          gap: 10px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .preview-form {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;

    .list-info {
      margin-bottom: 20px;

      h3 {
        margin-top: 0;
        margin-bottom: 15px;
      }

      .list-meta {
        margin-bottom: 15px;
      }

      .mr-10 {
        margin-right: 10px;
      }

      .list-description {
        margin: 15px 0;
        color: #606266;
      }
    }

    .templates-preview {
      max-height: 400px;
      overflow-y: auto;

      .no-templates {
        padding: 20px;
        text-align: center;
      }
    }
  }


}
</style>
