<template>
  <div class="app-container">
    <ContentWrap title="评估应用">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="intro-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>欢迎使用评估应用</span>
              </div>
            </template>
            <div class="intro-content">
              <p>评估应用提供了便捷的评估工具，您可以选择单一模板评估或评估清单进行评估。</p>
              <p>请在下方选择您需要的评估方式，然后点击卡片上的"评估"按钮开始评估。</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="evaluation-tabs-container">
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane label="模板评估" name="template">
                <TemplateSelector ref="templateSelectorRef" />
              </el-tab-pane>
              <el-tab-pane label="清单评估" name="list">
                <ListSelector ref="listSelectorRef" />
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TemplateSelector from './components/TemplateSelector.vue'
import ListSelector from './components/ListSelector.vue'

defineOptions({ name: 'EvaluationApplication' })

// 当前激活的标签页
const activeTab = ref('template')

// 组件引用
const templateSelectorRef = ref()
const listSelectorRef = ref()
</script>

<style lang="scss" scoped>
.intro-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }

  .intro-content {
    line-height: 1.6;
  }
}

.evaluation-tabs-container {
  width: 100%;
  overflow-x: hidden; /* 防止水平滚动 */
  min-width: 1200px; /* 设置最小宽度，确保卡片布局不会因窗口缩小而变形 */

  :deep(.el-tabs__content) {
    overflow: visible; /* 确保标签内容可见 */
  }
}

.mt-20 {
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .app-container {
    overflow-x: auto; /* 当窗口小于1200px时，允许水平滚动 */
  }

  .evaluation-tabs-container {
    padding: 0 10px;
  }
}
</style>
