<script lang="ts" setup>
import { Config } from '@form-create/designer'
import { ref, useTemplateRef, type Ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TemplateApi } from '@/api/evaluation/template'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useRoute } from 'vue-router'

defineOptions({ name: 'EvaluationBuild' })

// 获取路由实例
const route = useRoute()
// 获取路由实例
const router = useRouter()
// 获取设计器实例
const designer = useTemplateRef('designer') as Ref<
  InstanceType<typeof import('@form-create/designer').FcDesigner>
>
// 是否开启评分
const isOpenScore = ref(true)
// 是否开启表单规则
const isOpenTemplateRule = ref(true)
// 是否隐藏 API 密钥
const isHiddenApiKey = ref(true)
// 是否隐藏起始日期
const isHiddenStartDate = ref(true)
// 表单类型选项
const templateTypeOptions = ref<Array<any>>([])
// 导入/导出对话框
const modelVisible = ref(false)
// 导入/导出类型
const importType = ref<'option' | 'rule'>('option')
// 导入/导出 JSON
const json = ref('')
// 配置
const config: Config = {
  showCustomProps: false,
  autoActive: true,
  appendConfigData: [
    'version',
    'templateType',
    'templateRule',
    'isOpenScore',
    'allScore',
    'validityPeriod',
    'validityUnit',
    'validityStartTimeType',
    'validityStartTime'
  ],
  hiddenItem: [
    'password',
    'inputNumber',
    'switch',
    'rate',
    'timePicker',
    'timeRange',
    'slider',
    'datePicker',
    'dateRange',
    'colorPicker',
    'cascader',
    'upload',
    'elTransfer',
    'tree',
    'elTreeSelect',
    'fcEditor',
    'dataTable',
    'group',
    'subForm',
    'stepForm',
    'tableForm',
    'elButton',
    'html',
    'elDivider',
    'elTag',
    'text',
    'elImage',
    'fcRow',
    'fcTable',
    'elTabs',
    'space',
    'elCollapse'
  ],
  // 隐藏所有子表单组件
  hiddenMenu: ['subform'],
  // 修改 formRule
  formRule: {
    prepend: true,
    rule() {
      return [
        {
          type: 'input',
          field: 'version',
          title: '版本号'
        },
        {
          type: 'select',
          field: 'templateType',
          title: '表单类型',
          // value: 0,
          options: templateTypeOptions,
          hook: {
            value(evt) {
              if (evt.value === 0) {
                isOpenTemplateRule.value = true
                isHiddenApiKey.value = true
              } else if (evt.value === undefined) {
                isOpenTemplateRule.value = true
                isHiddenApiKey.value = true
              } else if (evt.value === 2) {
                isOpenTemplateRule.value = false
                isHiddenApiKey.value = false
              } else {
                isOpenTemplateRule.value = false
                isHiddenApiKey.value = true
              }
              // console.log('value 发生变化:', evt.value)
            }
          }
        },
        {
          type: 'input',
          field: 'apiKey',
          title: 'API密钥',
          info: 'API密钥用于调用API接口，修改时需要重新提交',
          value: '',
          hidden: isHiddenApiKey,
          props: {
            placeholder: '请输入 API 密钥',
            type: 'password',
            showPassword: true
          }
        },

        {
          type: 'radio',
          field: 'validityStartTimeType',
          title: '评估有效期',
          value: 'evaluationTime',
          options: [
            { label: '从评估时间开始', value: 'evaluationTime' },
            { label: '固定日期开始', value: 'fixedDate' }
          ],
          col: {
            span: 24
          },
          hook: {
            value(evt) {
              // console.log('起始时间类型变化为:', evt.value)
              // 根据选择控制日期选择器的显示/隐藏
              isHiddenStartDate.value = evt.value !== 'fixedDate'
            }
          }
        },
        {
          type: 'datePicker',
          field: 'validityStartTime',
          title: '起始日期',
          value: '',
          props: {
            type: 'date',
            placeholder: '选择起始日期',
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD'
          },
          hidden: isHiddenStartDate,
          col: {
            span: 24
          }
        },
        {
          type: 'inputNumber',
          field: 'validityPeriod',
          title: '时长',
          value: 3, // 默认值为3
          props: {
            min: 1,
            max: 365,
            step: 1,
            controlsPosition: 'right'
          },
          col: {
            span: 12
          }
        },
        {
          type: 'select',
          field: 'validityUnit',
          title: '单位',
          value: 'month', // 默认为月
          options: [
            { label: '天', value: 'day' },
            { label: '周', value: 'week' },
            { label: '月', value: 'month' }
          ],
          col: {
            span: 12
          }
        },
        {
          type: 'button',
          field: 'isOpenTemplateRule',
          title: '表单规则',
          props: {
            type: 'primary',
            size: 'small',
            plain: true
          },
          hidden: isOpenTemplateRule,
          children: ['设置规则'],
          style: {
            width: '100%'
          },
          on: {
            click: () => {
              scoreRuleDialogVisible.value = true
            }
          }
        },
        {
          type: 'input',
          field: 'templateRule',
          title: '表单规则',
          value: [],
          hidden: true
        }
      ]
    }
  },

  // 拓展表单配置项：Rule.props
  componentRule: {
    default: {
      append: true,
      rule() {
        return [
          {
            type: 'input',
            field: 'score',
            title: '得分',
            hidden: isOpenScore
          }
        ]
      }
    }
  }
}

// 挂载
onMounted(() => {
  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  const id = route.query.id
  if (id) {
    loadTemplate(id as string)
  }
})

// 添加模态框
const scoreRuleDialogVisible = ref(false)
const ruleType = ref('score') // 规则类型：score-分数区间，percentage-百分比
const scoreRules = ref([{ min: 0, max: 0, result: '' }])
const saveRuleLoading = ref(false) // 保存规则的加载状态

// 添加得分规则
const addScoreRule = () => {
  scoreRules.value.push({ min: 0, max: 0, result: '' })
}

// 删除得分规则
const removeScoreRule = (index: number) => {
  scoreRules.value.splice(index, 1)
}

// 规则类型变化处理
const onRuleTypeChange = (type: string) => {
  if (type === 'percentage') {
    // 切换到百分比规则，重置规则为百分比示例
    scoreRules.value = [
      { min: 0, max: 30, result: '轻度依赖' },
      { min: 31, max: 70, result: '中度依赖' },
      { min: 71, max: 100, result: '重度依赖' }
    ]
  } else {
    // 切换到分数区间规则，重置为默认示例
    scoreRules.value = [
      { min: 0, max: 60, result: '轻度依赖' },
      { min: 61, max: 80, result: '中度依赖' },
      { min: 81, max: 100, result: '重度依赖' }
    ]
  }
}



// 处理导入 Option
const handleImportOption = () => {
  modelVisible.value = true
  importType.value = 'option'
  json.value = ''
}

// 处理导入 Rule
const handleImportRule = () => {
  modelVisible.value = true
  importType.value = 'rule'
  json.value = ''
}

// 处理导出 Option
const handleExportOption = () => {
  modelVisible.value = true
  json.value = designer.value?.getOptionsJson() || ''
}

// 处理导出 Rule
const handleExportRule = () => {
  modelVisible.value = true
  json.value = designer.value?.getJson() || ''
}

// 确认导入
const handleImport = () => {
  try {
    if (!json.value) {
      return
    }
    const jsonData = JSON.parse(json.value)
    if (importType.value === 'option') {
      designer.value?.setOptions(jsonData)
    } else {
      designer.value?.setRule(jsonData)
    }
    modelVisible.value = false
    ElMessage.success('导入成功')
  } catch (error) {
    ElMessage.error('JSON 格式错误')
    // console.error(error)
  }
}

// 加载模板数据
const loadTemplate = async (id: string) => {
  try {
    const data = await TemplateApi.getTemplate(parseInt(id))
    if (data.formSchema) {
      // data.formSchema 已经是字符串了，需要解析成对象
      const schema =
        typeof data.formSchema === 'string' ? JSON.parse(data.formSchema) : data.formSchema

      // 设置规则和选项
      designer.value?.setRule(schema.rule)
      designer.value?.setOptions(schema.option)

      // 设置表单名称和版本号
      const formConfig = designer.value?.getOption()
      // console.log('formConfig', formConfig)
      // 如果存在评分规则，解析并设置到 scoreRules
      if (formConfig.form?.templateRule) {
        try {
          const parsedRules = JSON.parse(formConfig.form?.templateRule)
          // 更新 scoreRules
          scoreRules.value = parsedRules
        } catch (error) {
          // console.error('解析评分规则失败:', error)
        }
      }
    }
  } catch (error) {
    // console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

// 保存模板
const handleSaveTemplate = async () => {
  try {
    // 获取表单规则和配置
    const rule = designer.value?.getRule()
    if (rule.length === 0) {
      ElMessage.warning('请先设计表单')
      return
    }

    const formConfig = designer.value?.getOption()
    const templateName = formConfig?.formName
    const templateVersion = formConfig?.form?.version
    const templateType = formConfig?.form?.templateType
    const apiKey = formConfig?.form?.apiKey
    // console.log('apiKey', apiKey)

    // 保存当前的templateRule，避免被覆盖
    const currentTemplateRule = formConfig?.form?.templateRule

    if (formConfig?.form) {
      formConfig.form.apiKey = ''
      // 确保templateRule不被覆盖
      if (currentTemplateRule) {
        formConfig.form.templateRule = currentTemplateRule
      }

      // 更新设计器的选项
      designer.value?.setOptions(formConfig)
    }

    if (
      !templateName ||
      !templateVersion ||
      templateType === undefined ||
      templateType === undefined
    ) {
      ElMessage.warning('请填写表单名称、版本号和表单类型')
      return
    }

    if (templateType === 2 && !apiKey) {
      ElMessage.warning('请填写API密钥')
      return
    }

    // 构建完整的表单配置 JSON
    const formSchema = {
      rule: rule,
      option: formConfig,
      config: {
        name: templateName,
        version: templateVersion,
        templateType: templateType,
        createTime: new Date().getTime(),
        updateTime: new Date().getTime()
      }
    }

    // 调试：输出当前的评分规则
    console.log('保存模板时的评分规则:', formConfig?.form?.templateRule)
    console.log('完整的formSchema:', formSchema)

    // 构建模板数据
    const templateData = {
      id: route.query.id ? parseInt(route.query.id as string) : undefined,
      name: templateName,
      version: templateVersion,
      status: 0,
      type: templateType,
      apiKey: apiKey,
      formSchema: JSON.stringify(formSchema)
    }

    // 保存或更新模板
    if (templateData.id) {
      await TemplateApi.updateTemplate(templateData)
    } else {
      await TemplateApi.createTemplate(templateData)
    }

    ElMessage.success('保存成功')
    router.push('/evaluation/template')
  } catch (error) {
    // console.error('保存模板失败:', error)
    ElMessage.error('保存失败')
  }
}

// 保存得分规则
const saveScoreRules = async () => {
  // 验证规则
  if (!validateRules()) {
    return
  }

  saveRuleLoading.value = true
  try {
    // 格式化规则数据 - 移除多余的空格和换行，但保持数组格式
    const formattedRules = JSON.stringify(scoreRules.value)

    // 调试：输出要保存的规则
    console.log('要保存的评分规则:', scoreRules.value)
    console.log('格式化后的规则:', formattedRules)

    // 获取当前表单配置
    const options = designer.value?.getOption()
    console.log('保存前的options:', options)

    if (options) {
      // 更新表单配置
      options.form = options.form || {}
      options.form.templateRule = formattedRules

      // 使用 setOption 更新配置
      designer.value?.setOption(options)

      // 验证是否保存成功
      const updatedOptions = designer.value?.getOption()
      console.log('保存后的options:', updatedOptions)
      console.log('保存后的templateRule:', updatedOptions?.form?.templateRule)
    }

    // 如果是编辑现有模板，直接保存到后端
    const templateId = route.query.id
    if (templateId) {
      await saveRulesToBackend(parseInt(templateId as string), formattedRules)
      ElMessage.success('评分规则保存成功')
    } else {
      // 新建模板时，只保存到前端，提示用户需要保存模板
      ElMessage.success('评分规则设置完成，保存模板后生效')
    }

    scoreRuleDialogVisible.value = false
  } catch (error) {
    console.error('保存规则失败:', error)
    ElMessage.error('保存规则失败')
  } finally {
    saveRuleLoading.value = false
  }
}

// 保存规则到后端
const saveRulesToBackend = async (templateId: number, rules: string) => {
  try {
    console.log('开始保存规则到后端，模板ID:', templateId)

    // 获取现有模板数据
    const existingTemplate = await TemplateApi.getTemplate(templateId)
    console.log('获取到现有模板数据:', existingTemplate)

    // 解析现有的formSchema
    let formSchema: any = {}
    if (existingTemplate.formSchema) {
      formSchema = typeof existingTemplate.formSchema === 'string'
        ? JSON.parse(existingTemplate.formSchema)
        : existingTemplate.formSchema
    } else {
      // 如果没有现有的formSchema，创建一个基本结构
      const currentRule = designer.value?.getRule() || []
      const currentOptions = designer.value?.getOption() || {}

      formSchema = {
        rule: currentRule,
        option: currentOptions,
        config: {
          name: existingTemplate.name,
          version: existingTemplate.version,
          templateType: existingTemplate.type,
          createTime: new Date().getTime(),
          updateTime: new Date().getTime()
        }
      }
    }

    // 更新formSchema中的templateRule
    if (!formSchema.option) {
      formSchema.option = {}
    }
    if (!formSchema.option.form) {
      formSchema.option.form = {}
    }
    formSchema.option.form.templateRule = rules

    // 更新模板
    const updateData = {
      id: templateId,
      name: existingTemplate.name,
      version: existingTemplate.version,
      status: existingTemplate.status,
      type: existingTemplate.type,
      apiKey: existingTemplate.apiKey || '', // 保持原有的apiKey，如果没有则为空
      formSchema: JSON.stringify(formSchema)
    }

    console.log('向后端保存规则，更新数据:', updateData)

    await TemplateApi.updateTemplate(updateData)
    console.log('规则已成功保存到后端')

  } catch (error) {
    console.error('保存规则到后端失败:', error)
    throw error
  }
}

// 验证规则
const validateRules = () => {
  // 如果没有规则，说明用户想清空规则，直接返回 true
  if (scoreRules.value.length === 0) {
    return true
  }

  // 验证每条规则
  for (const rule of scoreRules.value) {
    if (rule.min === undefined || rule.max === undefined || !rule.result) {
      ElMessage.warning('请完整填写每条规则')
      return false
    }
    if (rule.min > rule.max) {
      ElMessage.warning('最小值不能大于最大值')
      return false
    }
  }

  // 验证区间是否重叠
  // 先按最小值排序
  const sortedRules = [...scoreRules.value].sort((a, b) => a.min - b.min)

  for (let i = 0; i < sortedRules.length - 1; i++) {
    // 检查当前区间的最大值是否大于下一个区间的最小值（真正的重叠）
    if (sortedRules[i].max > sortedRules[i + 1].min) {
      ElMessage.warning('得分区间不能重叠')
      return false
    }
  }

  return true
}
</script>

<template>
  <div class="fd-container">
    <FcDesigner ref="designer" :config="config">
      <template #handle>
        <!-- 导入按钮 -->
        <el-button size="small" type="primary" plain @click="handleImportOption">
          导入 Option
        </el-button>
        <el-button size="small" type="primary" plain @click="handleImportRule">
          导入 Rule
        </el-button>
        <!-- 导出按钮 -->
        <el-button size="small" type="primary" plain @click="handleExportOption">
          导出 Option
        </el-button>
        <el-button size="small" type="danger" plain @click="handleExportRule">
          导出 Rule
        </el-button>
        <!-- 保存模板按钮 -->
        <el-button size="small" type="primary" @click="handleSaveTemplate">
          <Icon icon="ep:folder-add" class="mr-5px" />
          保存模板
        </el-button>
      </template>
    </FcDesigner>

    <!-- 导入/导出对话框 -->
    <el-dialog
      v-model="modelVisible"
      :title="importType === 'option' ? '导入 Option' : '导入 Rule'"
      width="50%"
      @close="() => (json = '')"
    >
      <el-input
        v-model="json"
        type="textarea"
        :rows="10"
        :placeholder="importType === 'option' ? '请输入 Option JSON' : '请输入 Rule JSON'"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modelVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImport" :disabled="!json"> 确认导入 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加得分规则配置对话框 -->
    <el-dialog v-model="scoreRuleDialogVisible" title="设置得分规则" width="800px" destroy-on-close>
      <div class="score-rules">
        <!-- 规则说明 -->
        <el-alert type="info" :closable="false" class="mb-4">
          <p class="text-sm">请设置得分区间和对应的评估结果：</p>
          <p class="text-xs text-gray-500 mt-1">* 得分区间不能重叠，对应的评估结果不能为空</p>
          <p class="text-xs text-gray-500 mt-1">* 建议按照从低分到高分的顺序设置规则</p>
          <p class="text-xs text-blue-600 mt-1">* 注意：表单中每个选项的键值（value）就是该选项对应的分数</p>
        </el-alert>

        <!-- 规则类型选择 -->
        <div class="mb-4">
          <el-radio-group v-model="ruleType" @change="onRuleTypeChange">
            <el-radio label="score">分数区间规则</el-radio>
            <el-radio label="percentage">百分比规则</el-radio>
          </el-radio-group>
        </div>

        <!-- 规则列表 -->
        <div class="rule-list">
          <div v-for="(rule, index) in scoreRules" :key="index" class="rule-item">
            <el-row :gutter="16" align="middle">
              <el-col :span="16">
                <div class="flex items-center">
                  <span class="mr-2 text-gray-600">当得分在</span>
                  <el-input-number
                    v-model="rule.min"
                    :min="0"
                    :max="rule.max"
                    size="small"
                    class="w-20"
                  />
                  <span class="mx-2 text-gray-600">至</span>
                  <el-input-number v-model="rule.max" :min="rule.min" size="small" class="w-20" />
                  <span class="mx-2 text-gray-600">{{ ruleType === 'percentage' ? '%时' : '分时' }}，评估结果为</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="flex items-center">
                  <el-input
                    v-model="rule.result"
                    size="small"
                    placeholder="请输入结果"
                    class="flex-1"
                  />
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    class="ml-2"
                    @click="removeScoreRule(index)"
                  >
                    <Icon icon="ep:delete" />
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 添加规则按钮 -->
        <div class="flex justify-center mt-4">
          <el-button type="primary" plain size="small" @click="addScoreRule">
            <Icon icon="ep:plus" class="mr-1" />
            添加规则
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="scoreRuleDialogVisible = false" :disabled="saveRuleLoading">取消</el-button>
          <el-button type="primary" @click="saveScoreRules" :loading="saveRuleLoading">
            {{ saveRuleLoading ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss">
.fd-container {
  height: 100vh;
  margin: 0 !important;
  padding: 0px;
  display: flex;
  flex-direction: column;
}

.preview-form {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;

  :deep(.el-form) {
    max-width: 800px;
    margin: 0 auto;
  }
}

.score-rules {
  padding: 16px;

  .rule-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
  }

  .rule-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s;

    &:hover {
      background-color: #f0f2f5;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .el-input-number {
      width: 100px;
    }
  }
}

:deep(.el-dialog__body) {
  padding-top: 16px;
  padding-bottom: 16px;
}

:deep(.el-alert) {
  border-radius: 8px;
}
</style>
