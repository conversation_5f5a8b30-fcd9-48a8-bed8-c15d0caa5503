<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, onBeforeUnmount, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowDown, InfoFilled, Warning } from '@element-plus/icons-vue'
import { marked } from 'marked'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import { useTagsView } from '@/hooks/web/useTagsView'
import { useTagsViewStore } from '@/store/modules/tagsView'

import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile/index'
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi, EvaluatorAnalysisUpdateReqVO, AIAnalysisUpdateReqVO } from '@/api/evaluation/result'
import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import { ListA<PERSON> } from '@/api/evaluation/list'
import { AiEvaluationApi } from '@/api/ai/evaluation'
import { ModelApi } from '@/api/ai/model/model'
import formCreate from '@form-create/element-ui'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
// 导入综合分析组件
import SummaryAnalysis from './components/SummaryAnalysis.vue'

defineOptions({ name: 'ListEvalIndex' })

const route = useRoute()
const message = useMessage()
const router = useRouter()
const { closeCurrent } = useTagsView()
const tagsViewStore = useTagsViewStore()

const id = route.query.id
// 标记是否来自评估应用（通过URL参数fromApplication判断）
const isFromApplication = route.query.fromApplication === 'true'

// 步骤条相关
const activeStep = ref(0)
const templateIds = ref<string[]>([])
const currentTemplateId = ref<number>()
const steps = ref<{ title: string; description: string; status?: string; icon?: string; originalDescription?: string; originalStatus?: string }[]>([])

// 表单相关
const previewForm = ref({})
const previewApi = ref<any>(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: { show: false },
  resetBtn: { show: false }
})

// 上一步相关
const isViewingPreviousStep = ref(false)
const previousStepFormData = ref<any>(null)
const previousStepIndex = ref<number>(-1)

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null) // 存储老人信息

// 评估任务清单执行数据
const listExecutionInfo = ref<ListExecutionVO | null>(null)

// 清单基本信息（包含评分规则）
const listInfo = ref<any>(null)

// 评估基础信息
const evalInfo = reactive({
  elderId: undefined as number | undefined,
  elderName: '',
  templateId: undefined as number | undefined,
  templateName: '',
  evaluatorId: undefined as number | undefined,
  evaluatorName: '',
  evaluationTime: '' as any, // 允许字符串或日期类型
  evaluationReason: ''
})

// 添加模板类型的响应式变量
const templateType = ref<number>(0)

// 添加模板类型选项
const templateTypeOptions = ref<Array<any>>([])

// 存储已完成的模板ID和结果ID数组
const templateIdsArray = ref<string[]>([])
const resultIdsArray = ref<string[]>([])
// 保存每个表单的数据
const formDataMap = ref<Record<string, any>>({})

// 获取表单类型名称的计算属性
const getTemplateTypeName = computed(() => {
  const option = templateTypeOptions.value.find((item: any) => item.value === templateType.value)
  return option ? option.label : '未知类型'
})

// 添加模板详情变量
const templateDetail = ref({
  version: '',
  validityPeriod: 3,
  validityUnit: 'month',
  validityStartTimeType: 'evaluationTime',
  validityStartTime: ''
})

// 添加评估师分析和AI分析变量
const evaluatorAnalysis = ref<string>('')
const aiAnalysis = ref<string>('')
const finishDialogVisible = ref(false)
const isAnalysisFinish = ref(false)
const isReasoningCollapsed = ref([]) // 默认展开分析过程
const aiReasoning = ref('')
const tempReasoning = ref('')
const tempAnalysis = ref('')
const aiResult = ref('')

// 添加暂存和AI分析相关状态
const savingDraft = ref(false)
const autoSaveStatus = ref('')
const autoSaveInterval = ref<number | null>(null)
const lastSaveTime = ref<string>('')
const generatingAI = ref(false)

// AI模型相关变量
const aiModelList = ref([])
const selectedModelId = ref<number | null>(null)

// 格式化AI分析内容为HTML
const formattedAIAnalysis = computed(() => {
  if (!aiAnalysis.value) return ''
  return marked(aiAnalysis.value)
})

// 渲染AI结果
const renderedAiResult = computed(() => {
  return marked(aiAnalysis.value)
})

// 渲染临时结果
const renderedTempResult = computed(() => {
  return marked(tempAnalysis.value)
})

// 查看AI分析
const viewAIAnalysis = () => {
  if (aiAnalysis.value) {
    finishDialogVisible.value = true
    isReasoningCollapsed.value = ['1']
  } else {
    ElMessage.warning('暂无AI分析结果，请先生成AI分析')
  }
}

// 保存AI分析
const saveAIAnalysis = async () => {
  if (!aiAnalysis.value) {
    ElMessage.warning('暂无AI分析结果，请先生成AI分析')
    return
  }

  try {
    // 保存AI分析到本地变量，不调用后端接口
    if (listExecutionInfo.value?.id) {
      // 只在本地显示成功消息，不更新listExecutionInfo对象
      // 这样可以避免在提交表单时将AI分析结果保存到清单库表中
      // listExecutionInfo.value.aiAnalysis = `[think]${aiReasoning.value}[/think]\n` + aiAnalysis.value
      ElMessage.success('AI分析保存成功')
    } else {
      ElMessage.error('无法保存AI分析，请先保存评估结果')
    }
  } catch (error) {
    console.error('保存AI分析失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 添加加载状态
const loading = ref(true)

// 处理完成评估后的页面跳转
const handleEvaluationComplete = () => {
  console.log('=== 清单评估：开始处理评估完成后的页面跳转 ===')

  // 保存当前路由信息，因为跳转后route会改变
  const currentRoute = { ...route }
  console.log('当前路由信息:', { path: currentRoute.path, fullPath: currentRoute.fullPath })

  // 先设置刷新标记，确保在跳转前就设置好
  localStorage.setItem('evaluation_result_refresh', 'true')
  localStorage.setItem('evaluation_result_timestamp', new Date().getTime().toString())
  console.log('已设置localStorage刷新标记')

  // 检查评估记录页面的标签页是否已经存在
  const visitedViews = tagsViewStore.getVisitedViews
  console.log('当前已打开的标签页:', visitedViews.map(v => ({ path: v.path, fullPath: v.fullPath })))

  const resultPagePath = '/evaluation/result'
  const existingResultTab = visitedViews.find(view => view.path === resultPagePath)

  console.log('评估记录页面是否已存在:', !!existingResultTab)

  if (existingResultTab) {
    console.log('评估记录页面已存在，先关闭当前标签页，然后跳转到已存在页面')
    // 如果评估记录页面已经打开，先关闭当前标签页，然后跳转到该页面
    closeCurrent(currentRoute, () => {
      console.log('当前评估标签页已关闭，正在跳转到已存在的评估记录页面')
      // 不添加query参数，避免创建新标签页
      router.push(resultPagePath).then(() => {
        console.log('跳转到已存在的评估记录页面成功')
        // 使用nextTick确保页面完全加载后再触发事件
        nextTick(() => {
          // 触发一个自定义事件来通知评估记录页面刷新
          console.log('触发evaluationCompleted事件')
          window.dispatchEvent(new CustomEvent('evaluationCompleted'))

          // 额外触发一个延迟事件，确保页面有足够时间响应
          setTimeout(() => {
            console.log('触发延迟的evaluationCompleted事件')
            window.dispatchEvent(new CustomEvent('evaluationCompleted'))
          }, 100)
        })
      }).catch(error => {
        console.error('跳转到评估记录页面失败:', error)
      })
    })
  } else {
    console.log('评估记录页面不存在，关闭当前标签页后创建新标签页')
    // 如果评估记录页面不存在，先关闭当前标签页，然后跳转创建新标签页
    closeCurrent(currentRoute, () => {
      console.log('当前标签页已关闭，正在跳转到评估记录页面')
      router.push(resultPagePath).then(() => {
        console.log('跳转到新的评估记录页面成功')
        // 新页面会在mounted时自动检查localStorage标记并刷新
      }).catch(error => {
        console.error('跳转到评估记录页面失败:', error)
      })
    })
  }
}

// 加载AI模型列表
const loadAiModels = async () => {
  try {
    const models = await ModelApi.getModelSimpleList(1) // 1表示聊天模型类型
    // 过滤掉包含"dify"的模型（不区分大小写）
    aiModelList.value = (models || []).filter(model => {
      const modelName = (model.name || '').toLowerCase()
      const modelPlatform = (model.platform || '').toLowerCase()
      const modelModel = (model.model || '').toLowerCase()
      return !modelName.includes('dify') &&
             !modelPlatform.includes('dify') &&
             !modelModel.includes('dify')
    })

    // 优先选择DeepSeek-R1相关模型
    if (aiModelList.value.length > 0) {
      // 查找DeepSeek-R1相关模型
      const deepSeekR1Model = aiModelList.value.find(model =>
        model.name && (
          model.name.toLowerCase().includes('deepseek-r1') ||
          model.name.toLowerCase().includes('deepseek r1') ||
          model.model && model.model.toLowerCase().includes('deepseek-r1')
        )
      )

      if (deepSeekR1Model) {
        selectedModelId.value = deepSeekR1Model.id
      } else {
        // 如果没有找到DeepSeek-R1，则选择第一个
        selectedModelId.value = aiModelList.value[0].id
      }
    }
  } catch (error) {
    console.error('加载AI模型列表失败:', error)
    message.warning('加载AI模型列表失败')
  }
}

// 滚动表单到顶部
const scrollToTop = () => {
  nextTick(() => {
    // 处理普通滚动容器
    const formElement = document.querySelector('.evaluation-form')
    if (formElement) {
      formElement.scrollTop = 0
    }

    // 尝试自动点击页面上的el-backtop组件
    const backTopBtn = document.querySelector('.el-backtop')
    if (backTopBtn) {
      ;(backTopBtn as HTMLElement).click()
    } else {
      // 如果没有找到el-backtop组件，则手动滚动页面到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  })
}

// 加载评估任务清单执行数据
const loadListExecutionInfo = async () => {
  if (!id) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 获取评估任务清单执行数据
    const data = await ListExecutionApi.getListExecution(Number(id))
    listExecutionInfo.value = data

    // 获取清单基本信息（包含评分规则）
    if (data && data.listId) {
      try {
        const listData = await ListApi.getList(data.listId)
        listInfo.value = listData
        console.log('获取到的清单信息:', listData)
        console.log('清单评分规则:', listData?.listRule)
      } catch (error) {
        console.error('获取清单信息失败:', error)
        ElMessage.warning('获取清单信息失败，可能影响分数计算')
      }
    }

    // 填充评估基础信息
    if (data) {
      evalInfo.elderId = data.elderId
      evalInfo.elderName = data.elderName
      evalInfo.evaluatorId = data.evaluatorId
      evalInfo.evaluatorName = data.evaluatorName
      evalInfo.evaluationTime = data.startTime
      evalInfo.evaluationReason = data.evaluationReason

      // 如果有已填充的模板ID，获取模板信息
      if (data.requiredTemplateIds) {
        templateIds.value = data.requiredTemplateIds.split(',')

        // 初始化步骤，先用默认名称
        steps.value = templateIds.value.map((_, index) => ({
          title: `评估表单 ${index + 1}`,
          description: '待完成'
        }))

        // 添加清单综合分析步骤
        steps.value.push({
          title: '清单综合分析',
          description: '待完成'
        })

        // 加载真实的表单名称
        const templatesPromises = templateIds.value.map(async (templateId, index) => {
          try {
            const templateData = await TemplateApi.getTemplate(Number(templateId))
            if (templateData && templateData.name) {
              steps.value[index].title = templateData.name
            }
          } catch (error) {
            ElMessage.error(`加载表单${index + 1}名称失败:`, error)
          }
        })

        // 等待所有表单名称加载完成
        await Promise.all(templatesPromises)

        // 处理已完成和暂存的模板状态
        let resultStatusMap = {}
        let draftTemplateIds = []

        // 获取暂存的模板ID列表
        if (data.draftTemplateIds) {
          draftTemplateIds = data.draftTemplateIds.split(',')
        }

        // 如果有已完成的模板，需要根据评估结果状态来更新步骤状态
        if (data.completedTemplateIds && data.resultIds) {
          const completedIds = data.completedTemplateIds.split(',')
          const resultIds = data.resultIds.split(',')

          // 获取所有评估结果的状态信息
          for (let i = 0; i < resultIds.length; i++) {
            try {
              const resultData = await ResultApi.getResult(Number(resultIds[i]))
              if (resultData) {
                resultStatusMap[completedIds[i]] = resultData.status
              }
            } catch (error) {
              console.error(`获取评估结果${resultIds[i]}状态失败:`, error)
            }
          }

          // 更新步骤状态
          completedIds.forEach((completedId) => {
            const index = templateIds.value.findIndex((id) => id === completedId)
            if (index !== -1) {
              const status = resultStatusMap[completedId]
              if (status === 1) {
                // 状态为1表示已提交，标记为已完成
                steps.value[index].description = '已完成'
                steps.value[index].status = 'finish'
              } else if (status === 2) {
                // 状态为2表示暂存，标记为暂存状态
                steps.value[index].description = '暂存'
                steps.value[index].status = 'warning'
              } else if (status === 3) {
                // 状态为3表示进行中，标记为进行中状态
                steps.value[index].description = '进行中'
                steps.value[index].status = 'process'
              }
            }
          })
        }

        // 标记暂存的模板（优先级高于已完成状态）
        draftTemplateIds.forEach((draftId) => {
          const index = templateIds.value.findIndex((id) => id === draftId)
          if (index !== -1) {
            // 检查该模板是否也在已完成列表中
            const isAlsoCompleted = templateIdsArray.value.includes(draftId)
            if (isAlsoCompleted) {
              // 如果既在暂存列表又在已完成列表中，检查状态
              const status = resultStatusMap[draftId]
              if (status === 2) {
                // 状态为2表示暂存，显示为暂存
                steps.value[index].description = '暂存'
                steps.value[index].status = 'warning'
              } else if (status === 3) {
                // 状态为3表示进行中，显示为进行中
                steps.value[index].description = '进行中'
                steps.value[index].status = 'process'
              } else {
                // 状态为1表示已提交，显示为已完成
                steps.value[index].description = '已完成'
                steps.value[index].status = 'finish'
              }
            } else {
              // 只在暂存列表中，显示为暂存
              steps.value[index].description = '暂存'
              steps.value[index].status = 'warning'
            }
          }
        })

        // 找到第一个未完成的步骤（优先级：待完成 > 进行中 > 暂存）
        let firstIncompleteStep = -1

        // 首先查找第一个待完成的步骤
        for (let i = 0; i < templateIds.value.length; i++) {
          if (steps.value[i].description === '待完成') {
            firstIncompleteStep = i
            break
          }
        }

        // 如果没有待完成的步骤，查找第一个进行中的步骤
        if (firstIncompleteStep === -1) {
          for (let i = 0; i < templateIds.value.length; i++) {
            if (steps.value[i].description === '进行中') {
              firstIncompleteStep = i
              break
            }
          }
        }

        // 如果没有进行中的步骤，查找第一个暂存的步骤
        if (firstIncompleteStep === -1) {
          for (let i = 0; i < templateIds.value.length; i++) {
            if (steps.value[i].description === '暂存') {
              firstIncompleteStep = i
              break
            }
          }
        }

        // 设置当前步骤
        if (firstIncompleteStep !== -1) {
          activeStep.value = firstIncompleteStep
        } else {
          // 如果所有步骤都已完成，设置为最后一个步骤
          activeStep.value = templateIds.value.length - 1
        }

        // 检查清单是否真正完成（所有模板都已正式提交，没有暂存）
        // 注意：这里只检查模板步骤，不包括综合分析步骤
        const allTemplatesCompleted = steps.value.slice(0, -1).every(step => step.description === '已完成')
        const hasDrafts = draftTemplateIds.length > 0

        // 检查综合分析是否完成
        const hasEvaluatorAnalysis = data.evaluatorAnalysis && data.evaluatorAnalysis.trim() !== '';
        const hasAIAnalysis = data.aiAnalysis && data.aiAnalysis.trim() !== '';
        const summaryAnalysisCompleted = hasEvaluatorAnalysis && hasAIAnalysis;

        if (allTemplatesCompleted && !hasDrafts && summaryAnalysisCompleted) {
          // 所有模板和综合分析都已完成
          if (listExecutionInfo.value) {
            listExecutionInfo.value.status = 1
          }
          // 设置综合分析步骤为已完成
          steps.value[steps.value.length - 1].description = '已完成';
          steps.value[steps.value.length - 1].status = 'finish';
        } else if (hasDrafts || templateIdsArray.value.length > 0 || !allTemplatesCompleted) {
          // 有暂存或部分完成，状态为进行中
          if (listExecutionInfo.value) {
            listExecutionInfo.value.status = 0
          }
          // 设置综合分析步骤状态
          if (allTemplatesCompleted && !hasDrafts) {
            // 所有模板都已完成，但综合分析未完成
            if (hasEvaluatorAnalysis || hasAIAnalysis) {
              steps.value[steps.value.length - 1].description = '进行中';
              steps.value[steps.value.length - 1].status = 'process';
            } else {
              steps.value[steps.value.length - 1].description = '待完成';
              steps.value[steps.value.length - 1].status = 'wait';
            }
          }
        } else {
          // 未开始，状态为进行中
          if (listExecutionInfo.value) {
            listExecutionInfo.value.status = 0
          }
        }

        // 加载已完成和暂存模板的结果数据
        const allTemplateIds = []
        const allResultIds = []

        // 收集已完成的模板
        if (data.completedTemplateIds && data.resultIds) {
          const completedIds = data.completedTemplateIds.split(',')
          const resultIds = data.resultIds.split(',')
          allTemplateIds.push(...completedIds)
          allResultIds.push(...resultIds)
        }

        // 收集暂存的模板（需要查询对应的结果ID）
        if (draftTemplateIds.length > 0) {
          for (const draftTemplateId of draftTemplateIds) {
            // 检查是否已经在已完成列表中
            if (!allTemplateIds.includes(draftTemplateId)) {
              try {
                // 查询暂存模板的评估结果
                const draftResults = await ResultApi.getResultPage({
                  templateId: Number(draftTemplateId),
                  listExecutionId: data.id,
                  pageNo: 1,
                  pageSize: 10
                })

                if (draftResults.list && draftResults.list.length > 0) {
                  const draftResult = draftResults.list[0]
                  allTemplateIds.push(draftTemplateId)
                  allResultIds.push(String(draftResult.id))
                }
              } catch (error) {
                console.error(`查询暂存模板${draftTemplateId}的结果失败:`, error)
              }
            }
          }
        }

        // 逐个加载所有模板的结果数据（包括已完成和暂存）
        for (let i = 0; i < allTemplateIds.length; i++) {
          try {
            const resultId = allResultIds[i]
            const templateId = allTemplateIds[i]

            // 获取结果数据
            const resultData = await ResultApi.getResult(Number(resultId))

            if (resultData && resultData.result) {
              try {
                const parsedResult = JSON.parse(resultData.result)
                // console.log('表单结果数据:', templateId, parsedResult)

                // 保存规则数据到对应表单
                if (parsedResult.rules) {
                  // 保存规则数据，但不立即应用（会在loadTemplate时应用）
                  formDataMap.value[`${templateId}_rules`] = parsedResult.rules
                }

                // 处理表单数据 - 兼容多种数据结构
                const formValues: any = {}

                // 首先检查是否有新格式数据结构中的 formData
                if (parsedResult.formData) {
                  // 新数据结构直接使用 formData
                  Object.assign(formValues, parsedResult.formData)
                }
                // 再检查是否有 hierarchicalResults 或 fullResult，这是新的层次化结构
                else if (parsedResult.fullResult && parsedResult.fullResult.rawFormData) {
                  // 从完整的 JSON 结构中提取 rawFormData
                  Object.assign(formValues, parsedResult.fullResult.rawFormData)
                }
                // 最后尝试旧的数据结构
                else if (
                  parsedResult.assessmentResults &&
                  Array.isArray(parsedResult.assessmentResults)
                ) {
                  try {
                    parsedResult.assessmentResults.forEach((result: any) => {
                      if (result.items && Array.isArray(result.items)) {
                        result.items.forEach((item: any) => {
                          const rule =
                            item.props && item.props.field ? item.props.field : item.name
                          formValues[rule] = item.value
                        })
                      }
                    })
                  } catch (error) {
                    // console.error('处理旧格式评估结果时出错:', error)
                    ElMessage.error('处理旧格式评估结果时出错')
                  }
                }

                // 保存表单数据
                formDataMap.value[templateId] = formValues
              } catch (parseError) {
                // console.error(`解析表单${i + 1}结果数据失败:`, parseError)
                ElMessage.error(`解析表单${i + 1}结果数据失败`)
              }
            }
          } catch (error) {
            // console.error(`加载表单${i + 1}结果数据失败:`, error)
            ElMessage.error(`加载表单${i + 1}结果数据失败`)
          }
        }

        // 优先跳转到进行中的模板，然后是待完成的模板，最后是暂存的模板
        // 只有在URL明确指定了暂存模板ID时才直接跳转到暂存模板
        let targetStepIndex = -1

        // 检查URL中是否指定了要打开的暂存模板ID
        const draftTemplateId = route.query.draftTemplateId

        if (draftTemplateId && templateIds.value.includes(String(draftTemplateId))) {
          // 如果指定了暂存模板ID，并且该ID在模板列表中，则直接打开该模板
          const draftIndex = templateIds.value.indexOf(String(draftTemplateId))
          targetStepIndex = draftIndex
        } else {
          // 如果没有指定暂存模板ID，优先查找第一个进行中的模板
          // 只查找模板步骤，排除最后的综合分析步骤
          for (let i = 0; i < templateIds.value.length; i++) {
            if (steps.value[i].description === '进行中') {
              targetStepIndex = i
              break
            }
          }

          // 如果没有进行中的模板，查找待完成的模板
          if (targetStepIndex === -1) {
            for (let i = 0; i < templateIds.value.length; i++) {
              if (steps.value[i].description === '待完成') {
                targetStepIndex = i
                break
              }
            }
          }

          // 如果没有待完成的模板，查找暂存的模板（按顺序查找第一个）
          if (targetStepIndex === -1) {
            // 只查找模板步骤，排除最后的综合分析步骤
            for (let i = 0; i < templateIds.value.length; i++) {
              if (steps.value[i].description === '暂存') {
                targetStepIndex = i
                break
              }
            }
          }
        }

        // 如果找到了目标模板，跳转到该模板
        if (targetStepIndex !== -1) {
          activeStep.value = targetStepIndex

          // 检查是否是最后一个步骤（清单综合分析）
          if (targetStepIndex === steps.value.length - 1) {
            // 检查当前页面上是否有暂存状态的步骤（排除综合分析步骤）
            const hasDraftSteps = steps.value.some((step, stepIndex) => {
              return stepIndex !== steps.value.length - 1 && // 排除综合分析步骤
                     step.description === '暂存'
            })

            // 同时检查数据库中的暂存模板ID（作为备用检查）
            const draftIds = data.draftTemplateIds?.split(',').filter(id => id) || []

            // 如果页面上有暂存步骤或数据库中有暂存模板，都不能进入综合评估
            if (hasDraftSteps || draftIds.length > 0) {
              // 有暂存的模板，不能进入综合评估，跳转到第一个暂存的模板
              ElMessage.warning('存在暂存的模板，请先完成所有模板评估后再进行综合评估')

              // 优先从页面状态中找到第一个暂存模板
              let firstDraftIndex = -1
              for (let i = 0; i < steps.value.length - 1; i++) { // 排除综合分析步骤
                if (steps.value[i].description === '暂存') {
                  firstDraftIndex = i
                  break
                }
              }

              // 如果页面状态中没有找到，从数据库中找
              if (firstDraftIndex === -1 && draftIds.length > 0) {
                const firstDraftTemplateId = draftIds[0]
                firstDraftIndex = templateIds.value.indexOf(firstDraftTemplateId)
              }

              if (firstDraftIndex !== -1) {
                activeStep.value = firstDraftIndex
                const templateId = templateIds.value[firstDraftIndex]
                if (templateId) {
                  currentTemplateId.value = Number(templateId)
                }

                // 更新模板名称
                evalInfo.templateName = steps.value[firstDraftIndex]?.title || `评估表单 ${firstDraftIndex + 1}`

                await loadTemplate(currentTemplateId.value)

                // 获取并更新模板名称
                try {
                  const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
                  if (templateData && templateData.name) {
                    evalInfo.templateName = templateData.name
                    steps.value[firstDraftIndex].title = templateData.name
                  }
                } catch (templateError) {
                  console.error('获取模板信息失败:', templateError)
                  ElMessage.error('获取模板信息失败')
                }

                // 设置当前步骤为"进行中"状态（在更新标题之后）
                steps.value[firstDraftIndex].description = '进行中'
              } else {
                // 如果找不到暂存模板的索引，跳转到第一个模板
                activeStep.value = 0
                currentTemplateId.value = Number(templateIds.value[0])
                evalInfo.templateName = steps.value[0]?.title || `评估表单 1`
                await loadTemplate(currentTemplateId.value)
                steps.value[0].description = '进行中'
              }
            } else {
              // 没有暂存的模板，可以进入综合评估
              currentTemplateId.value = null
              // 设置当前步骤为"进行中"状态
              steps.value[targetStepIndex].description = '进行中'
            }
          } else {
            // 确保模板ID是有效的数字
            const templateId = templateIds.value[targetStepIndex]
            if (templateId && !isNaN(Number(templateId))) {
              currentTemplateId.value = Number(templateId)

              // 先更新模板名称，确保标题和内容同步
              evalInfo.templateName = steps.value[targetStepIndex]?.title || `评估表单 ${targetStepIndex + 1}`

              await loadTemplate(currentTemplateId.value)

              // 再次确认模板名称（从API获取最新的名称）
              try {
                const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
                if (templateData && templateData.name) {
                  evalInfo.templateName = templateData.name
                  // 同时更新步骤标题，确保一致性
                  steps.value[targetStepIndex].title = templateData.name
                }
              } catch (templateError) {
                // console.error('获取模板信息失败:', templateError)
                ElMessage.error('获取模板信息失败')
              }

              // 设置当前步骤为"进行中"状态（在更新标题之后）
              steps.value[targetStepIndex].description = '进行中'
            } else {
              console.error('无效的模板ID:', templateId)
              ElMessage.error('无效的模板ID')
            }
          }
        } else {
          // 所有模板都已完成，检查是否有暂存的模板
          const draftIds = data.draftTemplateIds?.split(',').filter(id => id) || []
          if (draftIds.length > 0) {
            // 有暂存的模板，但优先查找进行中的模板，如果没有才跳转到暂存模板
            let targetIndex = -1

            // 优先查找进行中的模板
            for (let i = 0; i < templateIds.value.length; i++) {
              if (steps.value[i].description === '进行中') {
                targetIndex = i
                break
              }
            }

            // 如果没有进行中的模板，查找待完成的模板
            if (targetIndex === -1) {
              for (let i = 0; i < templateIds.value.length; i++) {
                if (steps.value[i].description === '待完成') {
                  targetIndex = i
                  break
                }
              }
            }

            // 如果没有待完成的模板，才跳转到第一个暂存的模板
            if (targetIndex === -1) {
              const firstDraftTemplateId = draftIds[0]
              targetIndex = templateIds.value.indexOf(firstDraftTemplateId)
            }

            if (targetIndex !== -1) {
              activeStep.value = targetIndex
              const templateId = templateIds.value[targetIndex]
              if (templateId) {
                currentTemplateId.value = Number(templateId)
              }

              // 更新模板名称
              evalInfo.templateName = steps.value[targetIndex]?.title || `评估表单 ${targetIndex + 1}`

              await loadTemplate(currentTemplateId.value)

              // 获取并更新模板名称
              try {
                const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
                if (templateData && templateData.name) {
                  evalInfo.templateName = templateData.name
                  steps.value[targetIndex].title = templateData.name
                }
              } catch (templateError) {
                console.error('获取模板信息失败:', templateError)
                ElMessage.error('获取模板信息失败')
              }

              // 设置当前步骤为"进行中"状态（在更新标题之后）
              steps.value[targetIndex].description = '进行中'
            }
          } else {
            // 所有模板都已完成且没有暂存的模板，检查综合分析状态
            // 检查评估师综合分析和AI综合分析是否都有内容
            const hasEvaluatorAnalysis = data.evaluatorAnalysis && data.evaluatorAnalysis.trim() !== '';
            const hasAIAnalysis = data.aiAnalysis && data.aiAnalysis.trim() !== '';

            if (hasEvaluatorAnalysis && hasAIAnalysis) {
              // 综合分析已完成，标记为已完成
              steps.value[steps.value.length - 1].description = '已完成';
              steps.value[steps.value.length - 1].status = 'finish';
              message.success('所有评估表单已完成')
            } else {
              // 综合分析未完成，跳转到综合分析步骤
              activeStep.value = steps.value.length - 1;
              currentTemplateId.value = null;

              // 设置综合分析步骤为"进行中"状态
              if (hasEvaluatorAnalysis || hasAIAnalysis) {
                steps.value[activeStep.value].description = '进行中';
                steps.value[activeStep.value].status = 'process';
              } else {
                steps.value[activeStep.value].description = '待完成';
                steps.value[activeStep.value].status = 'wait';
              }
            }
          }
        }
      }

      // 如果有评估师分析，填充评估师分析
      if (data.evaluatorAnalysis) {
        evaluatorAnalysis.value = data.evaluatorAnalysis
      }

      // 根据elderId获取老人详细信息
      await loadElderInfo(data.elderId)

      // 初始化已完成的模板ID和结果ID数组
      if (data.completedTemplateIds) {
        templateIdsArray.value = data.completedTemplateIds.split(',')
      }
      if (data.resultIds) {
        resultIdsArray.value = data.resultIds.split(',')
      }

      // 检查API密钥，如果为空则尝试从第一个模板获取
      if (!data.apiKey && data.requiredTemplateIds) {
        try {
          const templateIds = data.requiredTemplateIds.split(',')
          if (templateIds.length > 0) {
            const firstTemplateId = Number(templateIds[0])
            if (!isNaN(firstTemplateId)) {
              const apiKeyRes = await TemplateApi.getTemplateApiKey(firstTemplateId)
              if (apiKeyRes) {
                // 更新清单执行记录中的API密钥
                await ListExecutionApi.updateListExecution({
                  id: data.id,
                  apiKey: apiKeyRes
                })
                // 更新本地数据
                if (listExecutionInfo.value) {
                  listExecutionInfo.value.apiKey = apiKeyRes
                }
                console.log('已自动设置API密钥')
              }
            }
          }
        } catch (error) {
          console.error('尝试获取模板API密钥失败:', error)
        }
      }
    }
  } catch (error) {
    // console.error('加载评估任务清单执行数据失败:', error)
    ElMessage.error('加载评估任务清单执行数据失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 创建进行中状态的记录
const createInProgressRecord = async (templateId: number) => {
  try {
    // 检查是否有清单执行ID
    if (!listExecutionInfo.value?.id) {
      console.log('无清单执行ID，跳过创建进行中记录')
      return
    }

    // 查询是否已存在该模板的记录
    const existingResults = await ResultApi.getResultPage({
      templateId: templateId,
      listExecutionId: listExecutionInfo.value.id,
      pageNo: 1,
      pageSize: 10
    })

    // 如果已存在记录，不重复创建
    if (existingResults.list && existingResults.list.length > 0) {
      console.log('模板记录已存在，跳过创建:', templateId)
      return
    }

    // 获取模板名称
    let templateName = `模板${templateId}`
    try {
      const templateData = await TemplateApi.getTemplate(templateId)
      if (templateData && templateData.name) {
        templateName = templateData.name
      }
    } catch (error) {
      console.error('获取模板名称失败:', error)
    }

    // 创建进行中状态的记录
    const resultData = {
      templateId: templateId,
      templateName: templateName,
      elderId: evalInfo.elderId,
      elderName: evalInfo.elderName,
      evaluatorId: evalInfo.evaluatorId,
      evaluatorName: evalInfo.evaluatorName,
      evaluationTime: new Date().getTime(), // 使用时间戳格式
      evaluationReason: evalInfo.evaluationReason || '',
      type: 1, // 评估清单类型
      status: 3, // 进行中状态
      result: JSON.stringify({
        options: {
          form: {
            templateType: 0, // 默认模板类型
            templateRule: '[]' // 空的评分规则
          }
        },
        rules: [],
        formData: {},
        hierarchicalResults: {},
        fullResult: {
          rawFormData: {},
          hierarchicalResults: {},
          aiInput: ''
        }
      }),
      listExecutionId: listExecutionInfo.value.id,
      evaluatorAnalysis: '',
      aiAnalysis: '',
      aiInputs: '' // 添加 aiInputs 字段
    }

    const newResult = await ResultApi.createResult(resultData)
    console.log('创建进行中记录成功:', newResult)

  } catch (error) {
    console.error('创建进行中记录失败:', error)
    // 不阻断流程，只记录错误
  }
}

// 加载模板数据
const loadTemplate = async (templateId: number) => {
  // 检查templateId是否为有效数字
  if (templateId === null) {
    // 如果是null，说明是综合分析步骤，不需要加载模板
    return
  }

  if (!templateId || isNaN(templateId) || templateId <= 0) {
    console.error('无效的模板ID:', templateId)
    ElMessage.error('无效的模板ID')
    return
  }

  try {
    loading.value = true // 开始加载，设置加载状态为true

    // 清空评估师分析，避免显示上一个模板的内容
    evaluatorAnalysis.value = ''

    // 清空AI分析相关变量
    aiReasoning.value = ''
    aiAnalysis.value = ''
    tempReasoning.value = ''
    tempAnalysis.value = ''
    isAnalysisFinish.value = false

    // 重要：清空表单数据，避免不同模板之间的数据污染
    previewForm.value = {}

    // 如果表单API已经初始化，重置表单
    if (previewApi.value) {
      try {
        previewApi.value.resetFields()
        previewApi.value.setValue({})
      } catch (error) {
        console.log('重置表单失败，这是正常的，因为表单可能还未完全初始化')
      }
    }

    // 获取模板详情
    const data = await TemplateApi.getTemplate(templateId)
    templateDetail.value = data

    // 在进入模板时创建"进行中"状态的记录
    await createInProgressRecord(templateId)

    // 解析formSchema提取有效期设置
    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        // console.log(schema)
        if (schema.option && schema.option.form) {
          templateDetail.value.validityPeriod = schema.option.form.validityPeriod || 3
          templateDetail.value.validityUnit = schema.option.form.validityUnit || 'month'
          templateDetail.value.validityStartTimeType =
            schema.option.form.validityStartTimeType || 'evaluationTime'
          templateDetail.value.validityStartTime = schema.option.form.validityStartTime || ''
        }
      } catch (e) {
        // console.error('解析模板数据失败:', e)
        ElMessage.error('解析模板数据失败')
      }
    }

    // 其他模板加载逻辑
    if (data.formSchema) {
      const schema = JSON.parse(data.formSchema)

      // 检查是否有存储的规则数据
      const storedRules = formDataMap.value[`${templateId}_rules`]
      if (storedRules && isViewingPreviousStep.value) {
        // console.log('使用已保存的规则数据:', storedRules)
        templateRule.value = storedRules
      } else {
        templateRule.value = schema.rule || []
      }

      // 保存完整的原始模板选项
      templateOption.value = {
        ...templateOption.value,
        ...schema.option,
        formName: schema.option?.formName || evalInfo.templateName,
        form: schema.option?.form || {}
      }

      // 隐藏默认的提交按钮
      templateOption.value.submitBtn = {
        show: false
      }

      // 获取模板类型
      templateType.value = schema.option?.form?.templateType || 0

      // 检查是否有保存的表单数据，如果有则恢复
      const savedFormData = formDataMap.value[templateId]
      if (savedFormData && Object.keys(savedFormData).length > 0) {
        // 等待组件渲染完成后设置表单数据
        nextTick(() => {
          if (previewApi.value) {
            try {
              // 先清空表单，确保没有残留数据
              previewApi.value.resetFields()
              previewApi.value.setValue({})

              // 然后设置新的表单数据
              // console.log('恢复保存的表单数据:', templateId, savedFormData)
              previewApi.value.setValue(savedFormData)
              previewForm.value = { ...savedFormData } // 使用展开运算符避免引用问题

              // 强制刷新表单
              nextTick(() => {
                if (previewApi.value) {
                  previewApi.value.refresh()
                }
              })
            } catch (error) {
              console.error('恢复表单数据失败:', error)
            }
          }
        })
      } else {
        // 如果没有保存的数据，确保表单是空的
        nextTick(() => {
          if (previewApi.value) {
            try {
              previewApi.value.resetFields()
              previewApi.value.setValue({})
              previewForm.value = {}
            } catch (error) {
              console.log('初始化空表单失败，这是正常的')
            }
          }
        })
      }

      // 滚动表单到顶部
      scrollToTop()
    }

    // 从数据库加载评估师分析和AI分析数据
    nextTick(async () => {
      // 清空所有分析数据，准备从数据库加载
      evaluatorAnalysis.value = ''
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false

      try {
        if (listExecutionInfo.value?.id) {
          const existingResults = await ResultApi.getResultPage({
            templateId: templateId,
            listExecutionId: listExecutionInfo.value.id,
            pageNo: 1,
            pageSize: 10
          })

          if (existingResults.list && existingResults.list.length > 0) {
            const result = existingResults.list[0]

            // 从数据库恢复评估师分析
            if (result.evaluatorAnalysis) {
              evaluatorAnalysis.value = result.evaluatorAnalysis
              console.log('从数据库加载评估师分析:', templateId, result.evaluatorAnalysis)
            }

            // 从数据库恢复AI分析数据
            if (result.aiAnalysis) {
              // 解析AI分析数据，提取推理过程和分析结果
              const aiAnalysisText = result.aiAnalysis

              // 检查是否包含推理过程标记
              const thinkMatch = aiAnalysisText.match(/\[think\](.*?)\[\/think\]/s)
              if (thinkMatch) {
                aiReasoning.value = thinkMatch[1].trim()
                aiAnalysis.value = aiAnalysisText.replace(/\[think\].*?\[\/think\]/s, '').trim()
              } else {
                aiAnalysis.value = aiAnalysisText
              }

              isAnalysisFinish.value = true
              console.log('从数据库加载AI分析数据:', templateId, {
                reasoning: aiReasoning.value,
                analysis: aiAnalysis.value
              })
            }
          }
        }
      } catch (error) {
        console.error('从数据库加载分析数据失败:', error)
      }
    })
  } catch (error) {
    // console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 加载老人信息
const loadElderInfo = async (elderId: number) => {
  if (!elderId) return

  try {
    loading.value = true // 开始加载，设置加载状态为true
    const elderData = await ArchivesProfileApi.getArchivesProfile(elderId)
    elderInfo.value = elderData
    // console.log('老人信息:', elderInfo.value)
  } catch (error) {
    // console.error('加载老人信息失败:', error)
    ElMessage.error('加载老人信息失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 加载评估基础信息
const loadEvalInfo = async () => {
  try {
    loading.value = true // 开始加载，设置加载状态为true
    // 首先尝试从URL参数加载数据
    const query = route.query

    // 如果有id，优先根据id加载数据
    if (id) {
      await loadListExecutionInfo()
      return
    }

    // 如果没有id但有其他参数，使用URL参数
    evalInfo.elderId = query.elderId ? parseInt(query.elderId as string) : undefined
    evalInfo.elderName = (query.elderName as string) || ''
    evalInfo.templateId = query.templateId ? parseInt(query.templateId as string) : undefined
    evalInfo.templateName = (query.templateName as string) || ''
    evalInfo.evaluatorId = query.evaluatorId ? parseInt(query.evaluatorId as string) : undefined
    evalInfo.evaluatorName = (query.evaluatorName as string) || ''
    evalInfo.evaluationTime = query.evaluationTime || ''
    evalInfo.evaluationReason = (query.evaluationReason as string) || ''

    // 根据 elderId 获取老人信息
    if (evalInfo.elderId) {
      await loadElderInfo(evalInfo.elderId)
    }
  } catch (error) {
    // console.error('加载评估信息失败:', error)
    ElMessage.error('加载评估信息失败')
  } finally {
    // console.log('loadEvalInfo - 完成加载')
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 处理表单提交
const handleSubmit = async (formData: any) => {
  // console.log('提交的表单数据:', formData)

  // 检查formData是否只包含_vts和isTrusted而没有实际的表单数据
  const hasOnlyMetaFields = Object.keys(formData).every((key) =>
    ['_vts', 'isTrusted'].includes(key)
  )

  // 如果只有元数据字段，尝试从form-create API获取完整的表单数据
  if (hasOnlyMetaFields && previewApi.value) {
    try {
      const completeFormData = previewApi.value.formData()
      // console.log('从API获取的完整表单数据:', completeFormData)

      // 如果获取到有效的表单数据，则使用它
      if (completeFormData && Object.keys(completeFormData).length > Object.keys(formData).length) {
        formData = completeFormData
      }
    } catch (error) {
      // console.error('获取完整表单数据失败:', error)
      ElMessage.error('获取完整表单数据失败')
    }
  }

  // 保存当前表单数据到对应的模板ID（使用深拷贝避免引用问题）
  if (currentTemplateId.value) {
    formDataMap.value[currentTemplateId.value] = JSON.parse(JSON.stringify(formData))
    console.log('=== 表单数据保存调试 ===')
    console.log('模板ID:', currentTemplateId.value)
    console.log('保存的表单数据:', formDataMap.value[currentTemplateId.value])
    console.log('========================')
  }

  // 处理并提交表单数据
  const success = await processAndSubmitFormData(formData, false)

  if (success) {
    // 提交成功后，需要等待步骤状态更新完成，然后再检查是否还有未完成的模板
    // 使用 nextTick 确保状态更新完成
    await nextTick()

    // 检查是否还有未完成的模板（排除当前已提交的步骤）
    const hasIncompleteTemplates = steps.value.some((step, index) => {
      // 排除当前步骤，因为它刚刚被提交
      if (index === activeStep.value) return false
      return step.description === '待完成' || step.description === '暂存'
    })

    if (!hasIncompleteTemplates) {
      // 所有模板都已完成
      ElMessage.success('评估完成！')
      // 使用新的标签页跳转函数
      handleEvaluationComplete()
    } else {
      // 还有未完成的模板，继续下一个
      ElMessage.success('提交成功！')

      // 查找下一个未完成的模板（优先级：最后一个进行中 > 暂存 > 待完成）
      let nextIncompleteStep = -1

      // 首先查找进行中的模板（从后往前查找，优先跳转到最后一个进行中的模板）
      for (let i = steps.value.length - 1; i >= 0; i--) {
        if (i !== activeStep.value && steps.value[i].description === '进行中') {
          nextIncompleteStep = i
          break
        }
      }

      // 如果没有找到进行中的，查找暂存的模板（从头开始按顺序查找）
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < steps.value.length; i++) {
          if (i !== activeStep.value && steps.value[i].description === '暂存') {
            nextIncompleteStep = i
            break
          }
        }
      }

      // 如果没有找到暂存的，查找待完成的模板（从头开始按顺序查找）
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < steps.value.length; i++) {
          if (i !== activeStep.value && steps.value[i].description === '待完成') {
            nextIncompleteStep = i
            break
          }
        }
      }

      // 如果没有找到后续的模板，从头开始查找暂存的
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < activeStep.value; i++) {
          if (steps.value[i].description === '暂存') {
            nextIncompleteStep = i
            break
          }
        }
      }

      // 如果还没有找到，从头开始查找待完成的
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < activeStep.value; i++) {
          if (steps.value[i].description === '待完成') {
            nextIncompleteStep = i
            break
          }
        }
      }

      // 如果找到了未完成的模板，跳转到该模板
      if (nextIncompleteStep !== -1) {
        activeStep.value = nextIncompleteStep
        currentTemplateId.value = Number(templateIds.value[nextIncompleteStep])

        // 更新模板名称
        evalInfo.templateName = steps.value[nextIncompleteStep]?.title || `评估表单 ${nextIncompleteStep + 1}`

        // 加载新模板
        await loadTemplate(currentTemplateId.value)

        // 设置当前步骤状态
        steps.value[nextIncompleteStep].description = '进行中'

        // 获取并更新模板名称
        try {
          const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
          if (templateData && templateData.name) {
            evalInfo.templateName = templateData.name
            steps.value[nextIncompleteStep].title = templateData.name
          }
        } catch (error) {
          console.error('获取模板信息失败:', error)
        }
      } else {
        // 如果没有找到其他未完成的模板，说明所有模板都已完成
        ElMessage.success('评估完成！')
        router.push('/evaluation/result')
      }
    }
  }

  return success
}

// 处理并提交表单数据
const processAndSubmitFormData = async (formData: any, isDraft: boolean = false) => {
  // 复制规则以避免修改原始数据
  const copyRules = formCreate.copyRules(templateRule.value)
  const options = templateOption.value

  // 常规提交处理...
  try {
    loading.value = true // 开始加载，设置加载状态为true

    let aiInput = `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 构建字段和标题的映射关系
    const fieldTitleMap = {}
    const fieldOptionsMap = {}
    const fieldParentMap = {}
    const fieldMidParentMap = {}
    const fieldTopParentMap = {}

    // 递归函数，用于从规则中提取字段信息
    const extractFieldInfo = (
      rules,
      parentTitle = '',
      midParentTitle = '',
      topParentTitle = ''
    ) => {
      if (!rules || !Array.isArray(rules)) return

      rules.forEach((rule) => {
        // 跳过elAlert类型
        if (rule._fc_drag_tag === 'elAlert') return

        // 获取当前项的标题
        let currentTitle = ''
        if (rule.props?.header) {
          currentTitle = rule.props.header
        } else if (rule.title && rule.title.trim() !== '') {
          currentTitle = rule.title
        } else if (rule.name?.startsWith('ref_') && rule.info && rule.info.trim() !== '') {
          currentTitle = rule.info
        } else if (rule.name && !rule.name.startsWith('ref_')) {
          currentTitle = rule.name
        }

        // 记录规则的基本信息
        // if (rule.field) {
        //   console.log(
        //     `提取字段信息: 字段=${rule.field}, 标题=${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // } else if (currentTitle) {
        //   console.log(
        //     `处理标题: ${currentTitle}, 父标题=${parentTitle}, 中间父标题=${midParentTitle}, 顶层标题=${topParentTitle}`
        //   )
        // }

        // 如果有字段，记录它的信息
        if (rule.field) {
          fieldTitleMap[rule.field] = currentTitle || ''
          fieldParentMap[rule.field] = parentTitle || ''
          fieldMidParentMap[rule.field] = midParentTitle || ''
          fieldTopParentMap[rule.field] = topParentTitle || ''

          if (rule.options && rule.options.length > 0) {
            fieldOptionsMap[rule.field] = rule.options
            // console.log(`字段${rule.field}的选项:`, rule.options)
          }
        }

        // 如果有子项，递归处理
        if (rule.children && rule.children.length > 0) {
          // 确定标题的层级关系
          let newTopParent = topParentTitle
          let newMidParent = midParentTitle

          // 如果是顶层卡片
          if (!parentTitle && rule._fc_drag_tag === 'elCard') {
            newTopParent = currentTitle
          }
          // 如果已有顶层父级，但没有中间父级
          else if (topParentTitle && !midParentTitle && rule._fc_drag_tag === 'elCard') {
            newMidParent = currentTitle
          }

          extractFieldInfo(rule.children, currentTitle || parentTitle, newMidParent, newTopParent)
        }
      })
    }

    // 提取字段信息
    // console.log('开始提取字段信息...')
    extractFieldInfo(copyRules)
    // console.log('字段标题映射:', fieldTitleMap)
    // console.log('字段父标题映射:', fieldParentMap)
    // console.log('字段中间父标题映射:', fieldMidParentMap)
    // console.log('字段顶层标题映射:', fieldTopParentMap)
    // console.log('字段选项映射:', fieldOptionsMap)

    // 处理formData，构建aiInput
    // console.log('开始构建aiInput...')

    // 将评估结果组织成层级结构，使用数组保持顺序
    const hierarchicalResults = {}
    // 记录处理顺序
    const processOrder = {
      topParents: [],
      midParents: {},
      parentTitles: {},
      fields: {}
    }

    // 对字段进行分组和层级化处理
    for (const field in formData) {
      if (fieldTopParentMap[field]) {
        const topParent = fieldTopParentMap[field]
        const midParent = fieldMidParentMap[field]
        const parentTitle = fieldParentMap[field]
        const fieldTitle = fieldTitleMap[field]

        // 记录处理顺序
        // 记录顶层标题顺序
        if (!processOrder.topParents.includes(topParent)) {
          processOrder.topParents.push(topParent)
        }

        // 记录中间标题顺序
        if (midParent && midParent !== topParent) {
          if (!processOrder.midParents[topParent]) {
            processOrder.midParents[topParent] = []
          }
          if (!processOrder.midParents[topParent].includes(midParent)) {
            processOrder.midParents[topParent].push(midParent)
          }

          // 记录直接父标题顺序（在中间标题下）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.parentTitles[midKey]) {
              processOrder.parentTitles[midKey] = []
            }
            if (!processOrder.parentTitles[midKey].includes(parentTitle)) {
              processOrder.parentTitles[midKey].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${midParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在中间标题下）
            const midKey = `${topParent}:${midParent}`
            if (!processOrder.fields[midKey]) {
              processOrder.fields[midKey] = []
            }
            processOrder.fields[midKey].push(field)
          }
        } else {
          // 记录直接父标题顺序（在顶层标题下）
          if (parentTitle && parentTitle !== topParent) {
            if (!processOrder.parentTitles[topParent]) {
              processOrder.parentTitles[topParent] = []
            }
            if (!processOrder.parentTitles[topParent].includes(parentTitle)) {
              processOrder.parentTitles[topParent].push(parentTitle)
            }

            // 记录字段顺序
            const parentKey = `${topParent}:${parentTitle}`
            if (!processOrder.fields[parentKey]) {
              processOrder.fields[parentKey] = []
            }
            processOrder.fields[parentKey].push(field)
          } else {
            // 记录字段顺序（直接在顶层标题下）
            if (!processOrder.fields[topParent]) {
              processOrder.fields[topParent] = []
            }
            processOrder.fields[topParent].push(field)
          }
        }

        // 处理值
        let resultValue = formData[field]
        let resultLabel = ''
        if (fieldOptionsMap[field]) {
          if (Array.isArray(resultValue)) {
            const labels = resultValue.map((val) => {
              const option = fieldOptionsMap[field].find((opt) => String(opt.value) === String(val))
              return option ? option.label : '未找到标签'
            })
            resultLabel = labels.join('、')
          } else {
            const option = fieldOptionsMap[field].find(
              (opt) => String(opt.value) === String(resultValue)
            )
            resultLabel = option ? option.label : '未找到标签'
          }
        } else {
          resultLabel = String(resultValue)
        }

        // 创建层级结构
        if (!hierarchicalResults[topParent]) {
          hierarchicalResults[topParent] = {
            title: topParent,
            children: [],
            childrenMap: {} // 用于快速查找
          }
        }

        // 添加中间父级（如果存在）
        if (midParent && midParent !== topParent) {
          // 检查中间父级是否已存在
          if (!hierarchicalResults[topParent].childrenMap[midParent]) {
            const midParentNode = {
              title: midParent,
              parent: topParent,
              children: [],
              childrenMap: {} // 用于快速查找
            }
            hierarchicalResults[topParent].children.push(midParentNode)
            hierarchicalResults[topParent].childrenMap[midParent] = midParentNode
          }

          // 添加直接父级（如果存在且与中间父级不同）
          if (parentTitle && parentTitle !== midParent && parentTitle !== topParent) {
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            // 检查直接父级是否已存在
            if (!midParentNode.childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: midParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              midParentNode.children.push(parentNode)
              midParentNode.childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = midParentNode.childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与中间父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: midParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const midParentNode = hierarchicalResults[topParent].childrenMap[midParent]
            midParentNode.children.push(fieldNode)
            midParentNode.childrenMap[fieldKey] = fieldNode
          }
        }
        // 如果没有中间父级
        else {
          // 添加直接父级（如果存在且与顶层父级不同）
          if (parentTitle && parentTitle !== topParent) {
            // 检查直接父级是否已存在
            if (!hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = {
                title: parentTitle,
                parent: topParent,
                children: [],
                childrenMap: {} // 用于快速查找
              }
              hierarchicalResults[topParent].children.push(parentNode)
              hierarchicalResults[topParent].childrenMap[parentTitle] = parentNode
            }

            // 添加字段信息
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: parentTitle,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
            parentNode.children.push(fieldNode)
            parentNode.childrenMap[fieldKey] = fieldNode
          }
          // 如果没有直接父级或直接父级与顶层父级相同
          else {
            const fieldKey = fieldTitle || field
            const fieldNode = {
              field: field,
              title: fieldTitle,
              parent: topParent,
              rawValue: resultValue,
              displayValue: resultLabel
            }
            hierarchicalResults[topParent].children.push(fieldNode)
            hierarchicalResults[topParent].childrenMap[fieldKey] = fieldNode
          }
        }

        // 构建文本输出
        aiInput += `${topParent}\n`
        if (midParent && midParent !== topParent) {
          aiInput += `${midParent}\n`
        }
        if (parentTitle && parentTitle !== topParent && parentTitle !== midParent) {
          aiInput += `${parentTitle}\n`
        }
        if (
          fieldTitle &&
          fieldTitle !== parentTitle &&
          fieldTitle !== midParent &&
          fieldTitle !== topParent
        ) {
          aiInput += `${fieldTitle}\n`
        }
        aiInput += `${resultLabel}\n\n`
      }
    }

    // 最终处理，根据处理顺序重新组织结果，并删除临时的childrenMap
    const orderedResults = {}
    processOrder.topParents.forEach((topParent) => {
      if (hierarchicalResults[topParent]) {
        // 创建有序的顶级结果
        const topResult = {
          title: hierarchicalResults[topParent].title,
          children: []
        }

        // 处理中间父级（如果有）
        if (processOrder.midParents[topParent]) {
          processOrder.midParents[topParent].forEach((midParent) => {
            if (hierarchicalResults[topParent].childrenMap[midParent]) {
              const midNode = hierarchicalResults[topParent].childrenMap[midParent]
              const orderedMidNode = {
                title: midNode.title,
                parent: midNode.parent,
                children: []
              }

              // 处理直接父级（如果有）
              const midKey = `${topParent}:${midParent}`
              if (processOrder.parentTitles[midKey]) {
                processOrder.parentTitles[midKey].forEach((parentTitle) => {
                  if (midNode.childrenMap[parentTitle]) {
                    const parentNode = midNode.childrenMap[parentTitle]
                    const orderedParentNode = {
                      title: parentNode.title,
                      parent: parentNode.parent,
                      children: []
                    }

                    // 处理字段
                    const parentKey = `${topParent}:${midParent}:${parentTitle}`
                    if (processOrder.fields[parentKey]) {
                      processOrder.fields[parentKey].forEach((field) => {
                        const fieldKey = fieldTitleMap[field] || field
                        if (parentNode.childrenMap[fieldKey]) {
                          orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                        }
                      })
                    }

                    orderedMidNode.children.push(orderedParentNode)
                  }
                })
              }

              // 处理直接在中间父级下的字段
              if (processOrder.fields[midKey]) {
                processOrder.fields[midKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (midNode.childrenMap[fieldKey] && !midNode.childrenMap[fieldKey].children) {
                    orderedMidNode.children.push(midNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedMidNode)
            }
          })
        }

        // 处理直接父级（如果没有中间父级）
        if (processOrder.parentTitles[topParent]) {
          processOrder.parentTitles[topParent].forEach((parentTitle) => {
            if (hierarchicalResults[topParent].childrenMap[parentTitle]) {
              const parentNode = hierarchicalResults[topParent].childrenMap[parentTitle]
              const orderedParentNode = {
                title: parentNode.title,
                parent: parentNode.parent,
                children: []
              }

              // 处理字段
              const parentKey = `${topParent}:${parentTitle}`
              if (processOrder.fields[parentKey]) {
                processOrder.fields[parentKey].forEach((field) => {
                  const fieldKey = fieldTitleMap[field] || field
                  if (parentNode.childrenMap[fieldKey]) {
                    orderedParentNode.children.push(parentNode.childrenMap[fieldKey])
                  }
                })
              }

              topResult.children.push(orderedParentNode)
            }
          })
        }

        // 处理直接在顶级下的字段
        if (processOrder.fields[topParent]) {
          processOrder.fields[topParent].forEach((field) => {
            const fieldKey = fieldTitleMap[field] || field
            if (
              hierarchicalResults[topParent].childrenMap[fieldKey] &&
              !hierarchicalResults[topParent].childrenMap[fieldKey].children
            ) {
              topResult.children.push(hierarchicalResults[topParent].childrenMap[fieldKey])
            }
          })
        }

        orderedResults[topParent] = topResult
      }
    })

    // console.log('原始层级化结果:', hierarchicalResults)
    // console.log('处理顺序:', processOrder)
    // console.log('有序层级化结果:', orderedResults)
    // console.log('最终AI输入:\n', aiInput)

    // 创建包含元数据和结构化结果的完整JSON对象
    const fullResultJSON = {
      metadata: {
        elderInfo: {
          id: evalInfo.elderId || 0,
          name: elderInfo.value?.name || '',
          gender: elderInfo.value?.gender,
          birthDate: elderInfo.value?.birthDate,
          age: elderInfo.value ? new Date().getFullYear() - elderInfo.value.birthDate[0] : ''
        },
        templateInfo: {
          id: evalInfo.templateId || 0,
          name: evalInfo.templateName,
          type: templateType.value,
          validityPeriod: templateDetail.value?.validityPeriod || 3,
          validityUnit: templateDetail.value?.validityUnit || 'month',
          validityStartTimeType: templateDetail.value?.validityStartTimeType || 'evaluationTime',
          validityStartTime: templateDetail.value?.validityStartTime || ''
        },
        evaluationInfo: {
          evaluatorId: evalInfo.evaluatorId || 0,
          evaluatorName: evalInfo.evaluatorName,
          evaluationReason: evalInfo.evaluationReason,
          evaluationTime: new Date().getTime()
        }
      },
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInput: aiInput,
      hierarchicalResults: orderedResults,
      rawFormData: formData
    }

    // 将完整结果转换为JSON字符串
    const fullResultJSONString = JSON.stringify(fullResultJSON, null, 2)
    // console.log('完整JSON结果:', fullResultJSONString)

    // if (templateType.value !== 0) {
    //   if (!evaluatorAnalysis.value) {
    //     message.error('评估师分析不能为空')
    //     return false
    //   }
    // }

    // 获取当前模板的名称
    let currentTemplateName = evalInfo.templateName
    try {
      if (currentTemplateId.value) {
        const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
        if (templateData && templateData.name) {
          currentTemplateName = templateData.name
        }
      }
    } catch (error) {
      console.error('获取模板名称失败:', error)
      // 使用默认名称
      currentTemplateName = evalInfo.templateName || '未知模板'
    }

    // 确定要操作的模板ID
    // 如果正在查看上一步，使用正在查看的模板ID；否则使用当前模板ID
    const targetTemplateId = isViewingPreviousStep.value && previousStepIndex.value >= 0
      ? Number(templateIds.value[previousStepIndex.value])
      : currentTemplateId.value || 0

    console.log('processAndSubmitFormData - 目标模板ID:', targetTemplateId, '是否正在查看上一步:', isViewingPreviousStep.value, '当前模板ID:', currentTemplateId.value)

    // 构建提交数据
    const resultData = {
      elderId: evalInfo.elderId || 0,
      elderName: elderInfo.value?.name || '',
      templateId: targetTemplateId,
      templateName: currentTemplateName,
      evaluationReason: evalInfo.evaluationReason,
      evaluatorId: evalInfo.evaluatorId || 0,
      evaluatorName: evalInfo.evaluatorName,
      evaluationTime: new Date().getTime(),
      type: 1,
      aiInputs: aiInput,
      aiAnalysis: aiAnalysis.value || '',
      evaluatorAnalysis: evaluatorAnalysis.value,
      status: isDraft ? 2 : 1, // 2表示暂存状态，1表示已提交状态，3表示进行中状态
      // 设置评估清单执行ID
      listExecutionId: listExecutionInfo.value?.id,
      result: JSON.stringify({
        options: options,
        rules: copyRules,
        formData: formData,
        hierarchicalResults: hierarchicalResults,
        fullResult: fullResultJSON
      })
    }

    let resultId

    // 判断是更新还是创建新结果
    if (isViewingPreviousStep.value && previousStepIndex.value >= 0) {
      // 更新操作：获取当前查看的模板和对应的结果ID
      const prevTemplateId = templateIds.value[previousStepIndex.value]
      let prevResultId = null

      console.log('查看暂存模板提交 - 模板ID:', prevTemplateId, '步骤索引:', previousStepIndex.value)

      // 首先通过模板ID在已完成列表中查找对应的结果ID
      const templateIndex = templateIdsArray.value.indexOf(String(prevTemplateId))
      if (templateIndex !== -1 && resultIdsArray.value[templateIndex]) {
        prevResultId = resultIdsArray.value[templateIndex]
        console.log('在已完成列表中找到结果ID:', prevResultId)
      }

      // 如果在已完成列表中找不到，尝试查询暂存的结果
      if (!prevResultId) {
        try {
          console.log('在已完成列表中未找到，查询数据库中的结果')
          const existingResults = await ResultApi.getResultPage({
            templateId: Number(prevTemplateId),
            listExecutionId: listExecutionInfo.value?.id,
            pageNo: 1,
            pageSize: 10
          })

          if (existingResults.list && existingResults.list.length > 0) {
            prevResultId = String(existingResults.list[0].id)
            console.log('在数据库中找到结果ID:', prevResultId)
          }
        } catch (error) {
          console.error('查询暂存结果失败:', error)
        }
      }

      if (prevResultId) {
        // console.log('更新结果:', prevResultId)
        // 使用updateResult API
        await ResultApi.updateResult({
          ...resultData,
          id: Number(prevResultId)
        })

        // 保存更新后的表单数据
        formDataMap.value[prevTemplateId] = formData

        // 如果是正式提交（非暂存），需要更新步骤状态和清单执行状态
        if (!isDraft) {
          console.log('查看暂存模板提交 - 更新步骤状态为已完成，步骤索引:', previousStepIndex.value)
          console.log('提交前步骤状态:', steps.value[previousStepIndex.value])

          // 将模板从暂存列表移除，添加到已完成列表
          if (listExecutionInfo.value) {
            // 检查是否已经在已完成列表中
            if (!templateIdsArray.value.includes(String(prevTemplateId))) {
              templateIdsArray.value.push(String(prevTemplateId))
              resultIdsArray.value.push(String(prevResultId))

              // 更新本地数据
              listExecutionInfo.value.completedTemplateIds = templateIdsArray.value.join(',')
              listExecutionInfo.value.resultIds = resultIdsArray.value.join(',')
            }

            // 从暂存列表中移除
            if (listExecutionInfo.value.draftTemplateIds) {
              const currentDraftIds = listExecutionInfo.value.draftTemplateIds.split(',').filter(id => id)
              const updatedDraftIds = currentDraftIds.filter(id => id !== String(prevTemplateId))
              listExecutionInfo.value.draftTemplateIds = updatedDraftIds.join(',')

              // 更新清单执行状态
              try {
                await ListExecutionApi.updateListExecution({
                  ...listExecutionInfo.value,
                  completedTemplateIds: templateIdsArray.value.join(','),
                  resultIds: resultIdsArray.value.join(','),
                  draftTemplateIds: updatedDraftIds.join(',')
                })

                console.log('清单执行状态更新成功')
              } catch (error) {
                console.error('更新清单执行状态失败:', error)
              }
            }
          }

          // 在数据更新完成后，强制更新步骤状态为已完成
          // 使用nextTick确保在DOM更新周期中执行
          await nextTick()
          steps.value[previousStepIndex.value].description = '已完成'
          steps.value[previousStepIndex.value].status = 'finish'

          console.log('提交后步骤状态:', steps.value[previousStepIndex.value])

          // 再次使用nextTick确保状态更新生效
          await nextTick()
          console.log('最终步骤状态:', steps.value[previousStepIndex.value])
        }

        message.success('更新成功')
        return true // 返回成功状态
      } else {
        // console.error('未找到结果ID进行更新')
        message.error('更新失败：未找到相应的结果记录')
        return false // 返回失败状态
      }
    } else {
      // 正常流程：检查当前模板是否已有结果（包括暂存状态）
      const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []

      // 检查当前模板是否已有结果（包括暂存状态和已完成状态）
      let existingResultId = null

      // 首先通过API直接查询是否存在该模板的评估结果
      try {
        console.log('查询模板是否已有评估结果，模板ID:', targetTemplateId, '清单执行ID:', listExecutionInfo.value?.id)
        const existingResults = await ResultApi.getResultPage({
          templateId: targetTemplateId,
          listExecutionId: listExecutionInfo.value?.id,
          pageNo: 1,
          pageSize: 10
        })

        if (existingResults.list && existingResults.list.length > 0) {
          // 找到现有结果，获取其ID
          existingResultId = existingResults.list[0].id
          console.log('找到现有评估结果，ID:', existingResultId)
        } else {
          console.log('未找到现有评估结果，将创建新记录')
        }
      } catch (error) {
        console.error('查询现有评估结果失败:', error)
      }

      // 如果API查询失败，再检查本地缓存的已完成列表
      if (!existingResultId && templateIdsArray.value.includes(String(targetTemplateId))) {
        const templateIndex = templateIdsArray.value.indexOf(String(targetTemplateId))
        if (templateIndex !== -1 && resultIdsArray.value[templateIndex]) {
          existingResultId = Number(resultIdsArray.value[templateIndex])
          console.log('从本地已完成列表找到结果ID:', existingResultId)
        }
      }

      // 根据是否找到现有结果来决定更新还是创建
      if (existingResultId) {
        // 更新现有结果
        resultId = await ResultApi.updateResult({
          id: existingResultId,
          ...resultData
        })

        // 如果是正式提交（非暂存），需要将该模板添加到已完成列表
        if (!isDraft) {
          // 检查是否已经在已完成列表中
          if (!templateIdsArray.value.includes(String(targetTemplateId))) {
            templateIdsArray.value.push(String(targetTemplateId))
            resultIdsArray.value.push(String(existingResultId))

            // 更新本地数据
            if (listExecutionInfo.value) {
              listExecutionInfo.value.completedTemplateIds = templateIdsArray.value.join(',')
              listExecutionInfo.value.resultIds = resultIdsArray.value.join(',')
            }
          }
        }

        // console.log('更新现有结果:', existingResultId)
      } else {
        // 创建新结果
        resultId = await ResultApi.createResult(resultData)
        // console.log('创建新结果:', resultId)
      }
    }

    // 更新评估任务清单执行状态
    // 注意：如果是查看上一步的提交，已经在上面处理了步骤状态更新，这里跳过
    if (listExecutionInfo.value && !(isViewingPreviousStep.value && previousStepIndex.value >= 0)) {
      // 只有在创建新结果或从暂存转为正式提交时才添加到已完成列表
      const shouldAddToCompleted = !templateIdsArray.value.includes(String(targetTemplateId)) && !isDraft

      if (shouldAddToCompleted) {
        // 添加新的ID到数组中
        templateIdsArray.value.push(String(targetTemplateId))
        resultIdsArray.value.push(String(resultId))
      }

      // 转换回逗号分隔的字符串
      const newCompletedTemplateIds = templateIdsArray.value.join(',')
      const newResultIds = resultIdsArray.value.join(',')

      // 检查是否完成了所有步骤（只有非暂存的模板才算完成）
      const completedCount = templateIdsArray.value.length
      const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []
      const totalTemplates = templateIds.value.length

      // 检查综合分析是否完成
      const hasEvaluatorAnalysis = listExecutionInfo.value?.evaluatorAnalysis &&
                                  listExecutionInfo.value.evaluatorAnalysis.trim() !== '';
      const hasAIAnalysis = listExecutionInfo.value?.aiAnalysis &&
                           listExecutionInfo.value.aiAnalysis.trim() !== '';
      const summaryAnalysisCompleted = hasEvaluatorAnalysis && hasAIAnalysis;

      // 检查所有模板是否都已完成（用于分数计算）
      const allTemplatesCompleted = completedCount === totalTemplates &&
                                   draftIds.length === 0 &&
                                   !isDraft

      // 只有当所有模板都完成且没有暂存且综合分析也完成时，才算整个清单完成
      const isAllCompleted = allTemplatesCompleted && summaryAnalysisCompleted

      console.log('清单完成状态检查:', {
        completedCount,
        totalTemplates,
        draftIdsLength: draftIds.length,
        isDraft,
        hasEvaluatorAnalysis,
        hasAIAnalysis,
        summaryAnalysisCompleted,
        allTemplatesCompleted,
        isAllCompleted
      })

      // 处理暂存模板ID列表
      let updateData = {
        ...listExecutionInfo.value,
        completedTemplateIds: newCompletedTemplateIds,
        resultIds: newResultIds
      }

      // 如果是暂存，更新暂存模板ID列表（如果字段存在）
      if (isDraft) {
        // 检查是否支持draftTemplateIds字段
        if (listExecutionInfo.value && 'draftTemplateIds' in listExecutionInfo.value) {
          const currentDraftIds = listExecutionInfo.value?.draftTemplateIds
            ? listExecutionInfo.value.draftTemplateIds.split(',')
            : []

          // 如果当前模板不在暂存列表中，添加它
          if (!currentDraftIds.includes(String(targetTemplateId))) {
            currentDraftIds.push(String(targetTemplateId))
          }

          updateData = {
            ...updateData,
            draftTemplateIds: currentDraftIds.join(','),
            status: 0 // 0表示进行中状态（有暂存模板）
          }
        } else {
          // 如果不支持draftTemplateIds字段，只设置状态
          updateData = {
            ...updateData,
            status: 0 // 0表示进行中状态（有暂存模板）
          }
        }
      } else {
        // 如果是正式提交，从暂存列表中移除当前模板（如果字段存在）
        if (listExecutionInfo.value && 'draftTemplateIds' in listExecutionInfo.value) {
          const currentDraftIds = listExecutionInfo.value?.draftTemplateIds
            ? listExecutionInfo.value.draftTemplateIds.split(',')
            : []

          const updatedDraftIds = currentDraftIds.filter(id => id !== String(targetTemplateId))

          updateData = {
            ...updateData,
            draftTemplateIds: updatedDraftIds.join(',')
          }
        }
      }

      // 如果所有模板都完成（用于分数计算），传递评分规则
      if (allTemplatesCompleted) {
        console.log('🎯 所有模板完成，传递评分规则')
        console.log('评分规则:', listInfo.value?.listRule)
        console.log('评分规则类型:', typeof listInfo.value?.listRule)

        updateData = {
          ...updateData,
          listRule: listInfo.value?.listRule || '' // 传递清单评分规则用于分数计算
        }

        console.log('传递给后端的完整数据:', updateData)
      }

      // 如果完成所有评估且不是草稿,更新完成时间和状态
      if (isAllCompleted) {
        updateData = {
          ...updateData,
          endTime: new Date().getTime(),
          status: 1 // 1表示已完成状态
        }
      }

      // 更新评估任务清单执行
      await ListExecutionApi.updateListExecution(updateData)

      // 更新本地数据
      if (isDraft) {
        listExecutionInfo.value.draftTemplateIds = updateData.draftTemplateIds
      }

      // 确定要更新的步骤索引
      // 如果正在查看上一步，则更新正在查看的步骤；否则更新当前活动步骤
      const targetStepIndex = isViewingPreviousStep.value ? previousStepIndex.value : activeStep.value

      console.log('更新步骤状态 - 目标步骤索引:', targetStepIndex, '是否正在查看上一步:', isViewingPreviousStep.value)

      // 更新步骤状态
      if (isDraft) {
        // 暂存状态
        steps.value[targetStepIndex].description = '暂存'
        steps.value[targetStepIndex].status = 'warning'
      } else {
        // 已完成状态
        steps.value[targetStepIndex].description = '已完成'
        steps.value[targetStepIndex].status = 'finish'

        // 如果是从暂存转为正式提交，需要更新本地的暂存列表
        if (listExecutionInfo.value?.draftTemplateIds) {
          const currentDraftIds = listExecutionInfo.value.draftTemplateIds.split(',').filter(id => id)
          const updatedDraftIds = currentDraftIds.filter(id => id !== String(targetTemplateId))
          listExecutionInfo.value.draftTemplateIds = updatedDraftIds.join(',')
        }

        // 注意：不再自动进入下一步，而是由submitForm方法控制
      }
    }

    // 只有在非草稿状态下才显示成功消息，草稿状态下的消息由saveDraft方法处理
    if (!isDraft) {
      message.success('保存成功')
    }
    return true
  } catch (error) {
    // console.error('保存评估结果失败:', error)
    ElMessage.error('保存评估结果失败')
    return false
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 获取有效期单位文本
const getValidityUnitText = (unit) => {
  switch (unit) {
    case 'day':
      return '天'
    case 'week':
      return '周'
    case 'month':
    default:
      return '个月'
  }
}

// 处理综合分析保存事件
const handleSummaryAnalysisSaved = async () => {
  // 更新步骤状态
  if (steps.value.length > 0 && activeStep.value === steps.value.length - 1) {
    // 检查评估师综合分析和AI综合分析是否都有内容
    const hasEvaluatorAnalysis = listExecutionInfo.value?.evaluatorAnalysis &&
                                listExecutionInfo.value.evaluatorAnalysis.trim() !== '';
    const hasAIAnalysis = listExecutionInfo.value?.aiAnalysis &&
                         listExecutionInfo.value.aiAnalysis.trim() !== '';

    // 如果两者都有内容，则标记为已完成
    if (hasEvaluatorAnalysis && hasAIAnalysis) {
      steps.value[activeStep.value].description = '已完成';
      steps.value[activeStep.value].status = 'finish';

      // 综合分析完成时，只更新状态为已完成，不重新计算分数
      try {
        console.log('🎯 综合分析完成，更新状态为已完成')

        // 准备更新数据，明确排除listRule字段避免重复计算分数
        const { listRule, ...updateDataWithoutListRule } = listExecutionInfo.value || {}
        const updateData = {
          ...updateDataWithoutListRule,
          status: 1, // 1表示已完成
          endTime: new Date().getTime()
        };

        await ListExecutionApi.updateListExecution(updateData);
      } catch (error) {
        console.error('更新清单状态失败:', error);
      }
    } else {
      // 否则标记为进行中
      steps.value[activeStep.value].description = '进行中';
      steps.value[activeStep.value].status = 'process';
    }
  }

  // 显示保存成功消息
  ElMessage.success('综合分析保存成功')
}

// 切换步骤
const handleStepChange = async (index: number) => {
  console.log('=== handleStepChange 开始 ===')
  console.log('目标步骤索引:', index)
  console.log('当前activeStep:', activeStep.value)
  console.log('当前currentTemplateId:', currentTemplateId.value)
  console.log('是否正在查看上一步:', isViewingPreviousStep.value)
  console.log('步骤总数:', steps.value.length)
  console.log('templateIds数组:', templateIds.value)
  console.log('resultIdsArray:', resultIdsArray.value)

  // 如果正在查看上一步，不允许点击步骤条
  if (isViewingPreviousStep.value) {
    ElMessage.warning('请先返回当前步骤，再进行其他操作')
    return
  }

  // 如果点击的是已完成、暂存或进行中的步骤（且不是当前步骤），调用查看上一步功能
  if ((steps.value[index].status === 'finish' || steps.value[index].description === '已完成' ||
       steps.value[index].description === '暂存' || steps.value[index].description === '进行中') && index !== activeStep.value) {
    console.log('点击已完成、暂存或进行中步骤，调用查看上一步功能:', index)
    await viewPreviousStep(index)
    return
  }

  // 如果点击的是当前步骤，不做任何操作
  if (index === activeStep.value) {
    return
  }

  // 如果点击的是未来的步骤，提示用户
  if (index > activeStep.value) {
    ElMessage.warning('请按顺序完成评估')
    return
  }

  // 检查是否是综合分析步骤（最后一个步骤）
  if (index === steps.value.length - 1) {
    // 检查当前页面上是否有暂存状态的步骤（排除综合分析步骤）
    const hasDraftSteps = steps.value.some((step, stepIndex) => {
      return stepIndex !== steps.value.length - 1 && // 排除综合分析步骤
             step.description === '暂存'
    })

    // 同时检查数据库中的暂存模板ID（作为备用检查）
    const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []

    // 如果页面上有暂存步骤或数据库中有暂存模板，都不能进入综合评估
    if (hasDraftSteps || draftIds.length > 0) {
      ElMessage.warning('存在暂存的模板，请先完成所有模板评估后再进行综合评估')
      return // 不进行步骤切换
    }
  }

  // 步骤切换时不再保存到sessionStorage，所有数据都从数据库获取

  // 立即更新activeStep，确保UI同步
  activeStep.value = index

  // 清空评估师分析，避免显示上一个模板的内容
  evaluatorAnalysis.value = ''

  // 使用nextTick确保DOM更新后再处理其他逻辑
  await nextTick()

  // 检查是否是综合分析步骤（最后一个步骤）
  if (index === steps.value.length - 1) {
    console.log('进入综合分析步骤')
    // 清空当前模板ID，确保设置为null而不是undefined，避免NaN问题
    currentTemplateId.value = null
    console.log('综合分析步骤：currentTemplateId设置为null')

    // 清空AI分析相关变量，避免显示之前模板的分析结果
    aiReasoning.value = ''
    aiAnalysis.value = ''
    tempReasoning.value = ''
    tempAnalysis.value = ''
    isAnalysisFinish.value = false

    // 不需要加载模板
    console.log('综合分析步骤：直接返回，不加载模板')
    return // 直接返回，不执行后续代码
  }
  // 否则是普通模板步骤
  else if (index < templateIds.value.length) {
    console.log('进入普通模板步骤，index:', index, 'templateIds.value.length:', templateIds.value.length)
    console.log('resultIdsArray.value.length:', resultIdsArray.value.length)

    // 如果有对应的评估结果，显示评估结果详情
    if (index < resultIdsArray.value.length) {
      console.log('有对应的评估结果，显示评估结果详情')
      // 设置当前模板ID为null，避免NaN问题
      currentTemplateId.value = null
      console.log('有评估结果：currentTemplateId设置为null')

      // 即使有评估结果，也要确保模板标题正确
      const templateId = templateIds.value[index]
      console.log('获取模板ID用于标题更新:', templateId)
      if (templateId && !isNaN(Number(templateId)) && Number(templateId) > 0) {
        try {
          const templateData = await TemplateApi.getTemplate(Number(templateId))
          if (templateData && templateData.name) {
            evalInfo.templateName = templateData.name
            steps.value[index].title = templateData.name
            console.log('更新模板标题成功:', templateData.name)
          }
        } catch (error) {
          console.error('获取模板信息失败:', error)
          ElMessage.error('获取模板信息失败')
        }
      }
    } else {
      console.log('没有评估结果，加载模板预览')
      // 如果没有评估结果，加载模板预览
      const templateId = templateIds.value[index]
      console.log('获取模板ID用于加载模板:', templateId)
      if (templateId && !isNaN(Number(templateId)) && Number(templateId) > 0) {
        currentTemplateId.value = Number(templateId)
        console.log('没有评估结果：currentTemplateId设置为:', currentTemplateId.value)

        // 获取并更新模板名称
        try {
          const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
          if (templateData && templateData.name) {
            evalInfo.templateName = templateData.name
            steps.value[index].title = templateData.name
            console.log('更新模板标题成功:', templateData.name)
          }
        } catch (error) {
          console.error('获取模板信息失败:', error)
          ElMessage.error('获取模板信息失败')
        }

        console.log('准备加载模板，currentTemplateId.value:', currentTemplateId.value)
        await loadTemplate(currentTemplateId.value)
        console.log('模板加载完成')
      } else {
        console.error('无效的模板ID:', templateId)
        ElMessage.error('无效的模板ID')
        return
      }
    }
  }

  // 滚动到当前步骤
  nextTick(() => {
    const stepElement = document.querySelector(`.el-step:nth-child(${index + 1})`)
    if (stepElement && stepElement.parentElement) {
      stepElement.parentElement.scrollTop = (stepElement as HTMLElement).offsetTop - 120
    }
  })
}

// 自定义提交表单方法
const submitForm = async () => {
  if (!previewApi.value) {
    ElMessage.error('表单未初始化')
    return
  }

  try {
    // 先进行表单验证
    const valid = await previewApi.value.validate()
    if (!valid) {
      ElMessage.error('请完成必填项')
      return
    }

    // 获取表单数据
    const formData = previewApi.value.formData()

    // 保存当前表单数据到对应的模板ID
    // 如果正在查看上一步，保存到正在查看的模板ID，否则保存到当前模板ID
    const targetTemplateId = isViewingPreviousStep.value && previousStepIndex.value >= 0
      ? templateIds.value[previousStepIndex.value]
      : currentTemplateId.value

    if (targetTemplateId) {
      // 深拷贝表单数据，避免引用问题
      formDataMap.value[targetTemplateId] = JSON.parse(JSON.stringify(formData))
      console.log('保存表单数据到模板ID:', targetTemplateId, '是否正在查看上一步:', isViewingPreviousStep.value)
      console.log('保存的表单数据:', formDataMap.value[targetTemplateId])
    }

    // 处理并提交表单数据
    const success = await processAndSubmitFormData(formData, false)

    // 如果正在查看上一步，提交成功后应该返回当前步骤
    if (success && isViewingPreviousStep.value) {
      console.log('正在查看状态下提交成功，准备返回当前步骤')
      ElMessage.success('更新成功，正在返回当前步骤...')

      // 等待一段时间再返回，让用户看到成功消息
      setTimeout(() => {
        returnToCurrentStep()
        scrollToTop()
      }, 1000)
      return
    }

    // 如果是最后一个模板，则完成整个评估
    if (success && activeStep.value === steps.value.length - 1) {
      // 更新评估任务清单执行状态
      if (listExecutionInfo.value) {
        try {
          // 检查是否还有暂存的模板
          const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []

          // 如果有暂存的模板，状态应该是"进行中"，否则是"已完成"
          const status = draftIds.length > 0 ? 0 : 1 // 0表示进行中，1表示已完成

          // 准备更新数据，明确排除listRule字段避免重复计算分数
          const { listRule, ...updateDataWithoutListRule } = listExecutionInfo.value || {}
          const updateData = {
            ...updateDataWithoutListRule,
            endTime: new Date().getTime(),
            status: status
          }

          await ListExecutionApi.updateListExecution(updateData)

          if (status === 0) {
            console.log('评估清单中还有暂存的模板，状态设置为进行中')
          }
        } catch (error) {
          console.error('更新评估任务清单执行状态失败:', error)
        }
      }

      ElMessage.success('评估完成！')

      // 使用新的标签页跳转函数
      handleEvaluationComplete()
    } else if (success) {
      // 如果不是最后一个模板，跳转到下一个模板
      let nextIncompleteStep = -1

      // 查找下一个未完成的模板（优先级：进行中 > 暂存 > 待完成）
      // 首先查找进行中的模板（从头开始按顺序查找）
      for (let i = 0; i < steps.value.length; i++) {
        if (i !== activeStep.value && steps.value[i].description === '进行中') {
          nextIncompleteStep = i
          break
        }
      }

      // 如果没有找到进行中的，查找暂存的模板（从头开始按顺序查找）
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < steps.value.length; i++) {
          if (i !== activeStep.value && steps.value[i].description === '暂存') {
            nextIncompleteStep = i
            break
          }
        }
      }

      // 如果没有找到暂存的，查找待完成的模板（从头开始按顺序查找）
      if (nextIncompleteStep === -1) {
        for (let i = 0; i < steps.value.length; i++) {
          if (i !== activeStep.value && steps.value[i].description === '待完成') {
            nextIncompleteStep = i
            break
          }
        }
      }

      console.log('submitForm - 调试信息:')
      console.log('  activeStep.value:', activeStep.value)
      console.log('  steps.value.length:', steps.value.length)
      console.log('  nextIncompleteStep:', nextIncompleteStep)
      console.log('  isFromApplication:', isFromApplication)
      console.log('  listExecutionInfo.value?.draftTemplateIds:', listExecutionInfo.value?.draftTemplateIds)

      // 检查是否是最后一个模板（不是综合评估步骤）
      const isLastTemplate = activeStep.value === steps.value.length - 2
      console.log('  isLastTemplate:', isLastTemplate)

      // 如果是最后一个模板，无论是否找到其他未完成的模板，都要检查是否有暂存的模板
      if (isLastTemplate) {
        console.log('  进入最后一个模板的暂存检查逻辑')

        // 检查当前页面上是否有暂存状态的步骤（排除当前模板和综合分析步骤）
        const hasDraftSteps = steps.value.some((step, index) => {
          return index !== activeStep.value &&
                 index !== steps.value.length - 1 && // 排除综合分析步骤
                 step.description === '暂存'
        })
        console.log('  页面上是否有暂存步骤:', hasDraftSteps)

        // 同时检查数据库中的暂存模板ID（作为备用检查）
        const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []
        console.log('  数据库中的draftIds:', draftIds)

        // 如果有暂存的模板（不包括当前模板，因为当前模板已经提交了）
        const currentTemplateIdStr = String(currentTemplateId.value)
        const otherDraftIds = draftIds.filter(id => id !== currentTemplateIdStr)
        console.log('  currentTemplateIdStr:', currentTemplateIdStr)
        console.log('  otherDraftIds:', otherDraftIds)

        // 如果页面上有暂存步骤或数据库中有其他暂存模板，都不能进入综合评估
        if (hasDraftSteps || otherDraftIds.length > 0) {
          console.log('  发现暂存模板，跳转到评估记录页面')

          // 有暂存模板，提示用户并跳转到评估记录页面
          ElMessage.warning('存在暂存的模板，无法进行综合评估。请先完成所有模板评估后再进行综合评估。')

          // 收集所有暂存的模板ID（包括页面状态和数据库状态）
          const allDraftIds = new Set(otherDraftIds)

          // 从页面状态中收集暂存的模板ID
          steps.value.forEach((step, index) => {
            if (index !== activeStep.value &&
                index !== steps.value.length - 1 && // 排除综合分析步骤
                step.description === '暂存') {
              const templateId = templateIds.value[index]
              if (templateId) {
                allDraftIds.add(String(templateId))
              }
            }
          })

          // 更新清单状态为"进行中"
          if (listExecutionInfo.value) {
            try {
              // 准备更新数据，明确排除listRule字段避免重复计算分数
              const { listRule, ...updateDataWithoutListRule } = listExecutionInfo.value || {}
              const updateData = {
                ...updateDataWithoutListRule,
                status: 0, // 0表示进行中
                draftTemplateIds: Array.from(allDraftIds).join(',')
              }
              await ListExecutionApi.updateListExecution(updateData)
              console.log('  清单状态已更新为进行中，暂存模板ID:', Array.from(allDraftIds))
            } catch (error) {
              console.error('更新评估任务清单状态失败:', error)
            }
          }

          // 使用新的标签页跳转函数
          handleEvaluationComplete()
          return
        } else {
          console.log('  没有暂存模板，可以继续进入综合评估')
        }
      }

      // 如果是从评估应用进入，直接按顺序进入下一个模板
      if (isFromApplication) {
        // 直接进入下一个模板
        nextIncompleteStep = activeStep.value + 1

        // 确保不超出模板数量范围
        if (nextIncompleteStep >= steps.value.length) {
          nextIncompleteStep = -1
        }
      } else {
        // 如果是从评估记录进入，查找下一个未完成的模板（优先级：暂存 > 待完成）

        // 首先查找暂存的模板
        for (let i = 0; i < steps.value.length; i++) {
          if (i !== activeStep.value && steps.value[i].description === '暂存') {
            nextIncompleteStep = i
            break
          }
        }

        // 如果没有找到暂存的，查找待完成的模板
        if (nextIncompleteStep === -1) {
          for (let i = 0; i < steps.value.length; i++) {
            if (i !== activeStep.value && steps.value[i].description === '待完成') {
              nextIncompleteStep = i
              break
            }
          }
        }
      }

      // 更新当前步骤状态为已完成
      steps.value[activeStep.value].description = '已完成'
      steps.value[activeStep.value].status = 'finish'

      // 如果找到了未完成的模板，跳转到该模板
      if (nextIncompleteStep !== -1) {
        activeStep.value = nextIncompleteStep

        // 检查是否是最后一个步骤（清单综合分析）
        if (nextIncompleteStep === steps.value.length - 1) {
          // 清单综合分析步骤不需要加载模板
          currentTemplateId.value = null
        } else {
          // 确保模板ID是有效的数字
          const templateId = templateIds.value[nextIncompleteStep]
          if (templateId && !isNaN(Number(templateId)) && Number(templateId) > 0) {
            currentTemplateId.value = Number(templateId)
          } else {
            console.error('无效的模板ID:', templateId)
            ElMessage.error('无效的模板ID')
            return false
          }
        }
      } else {
        // 如果没有找到未完成的模板，按顺序进入下一个模板
        activeStep.value++

        // 检查是否是最后一个步骤（清单综合分析）
        if (activeStep.value === steps.value.length - 1) {
          // 清单综合分析步骤不需要加载模板
          currentTemplateId.value = null
        } else {
          // 确保模板ID是有效的数字
          const templateId = templateIds.value[activeStep.value]
          if (templateId && !isNaN(Number(templateId)) && Number(templateId) > 0) {
            currentTemplateId.value = Number(templateId)
          } else {
            console.error('无效的模板ID:', templateId)
            ElMessage.error('无效的模板ID')
            return false
          }
        }
      }

      // 清空AI分析结果和评估师分析
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false
      evaluatorAnalysis.value = ''

      // 将新的当前步骤设置为"进行中"
      steps.value[activeStep.value].description = '进行中'
      steps.value[activeStep.value].status = 'process'

      // 检查是否是最后一个步骤（清单综合分析）
      if (activeStep.value === steps.value.length - 1) {
        // 清单综合分析步骤不需要加载模板
        // 滚动表单到顶部
        scrollToTop()
      } else {
        // 获取并更新模板名称
        try {
          if (currentTemplateId.value && !isNaN(currentTemplateId.value) && currentTemplateId.value > 0) {
            const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
            if (templateData && templateData.name) {
              evalInfo.templateName = templateData.name
              steps.value[activeStep.value].title = templateData.name
            }
          }
        } catch (error) {
          console.error('获取模板信息失败:', error)
          ElMessage.error('获取模板信息失败')
        }

        // 加载新模板
        if (currentTemplateId.value && !isNaN(currentTemplateId.value) && currentTemplateId.value > 0) {
          await loadTemplate(currentTemplateId.value)
        }

        // 滚动表单到顶部
        scrollToTop()
      }
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('提交表单失败')
  }
}

// 暂存评估结果
const saveDraft = async () => {
  if (savingDraft.value) return

  savingDraft.value = true
  try {
    // 获取当前表单数据
    const formData = previewApi.value.formData()

    // 保存当前表单数据到对应的模板ID
    // 如果正在查看上一步，保存到正在查看的模板ID，否则保存到当前模板ID
    const targetTemplateId = isViewingPreviousStep.value && previousStepIndex.value >= 0
      ? templateIds.value[previousStepIndex.value]
      : currentTemplateId.value

    if (targetTemplateId) {
      // 深拷贝表单数据，避免引用问题
      formDataMap.value[targetTemplateId] = JSON.parse(JSON.stringify(formData))
      console.log('暂存表单数据到模板ID:', targetTemplateId, '是否正在查看上一步:', isViewingPreviousStep.value)
      console.log('暂存的表单数据:', formDataMap.value[targetTemplateId])
    }

    // 处理表单数据，准备提交为草稿
    const success = await processAndSubmitFormData(formData, true) // 传入true表示是草稿

    // 更新最后保存时间
    lastSaveTime.value = new Date().toLocaleTimeString()
    autoSaveStatus.value = '已暂存'

    ElMessage.success('暂存成功')

    // 检查是否是最后一项
    if (success && activeStep.value === steps.value.length - 1) {
      // 如果是最后一项，提交整个评估清单
      try {
        // 更新评估任务清单执行状态
        if (listExecutionInfo.value) {
          // 检查是否还有暂存的模板
          const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []

          // 如果当前模板是暂存状态且不是综合分析步骤，需要将其添加到暂存列表中
          if (targetTemplateId && !isNaN(Number(targetTemplateId)) &&
              !draftIds.includes(String(targetTemplateId))) {
            draftIds.push(String(targetTemplateId))
          }

          // 如果有暂存的模板（包括当前模板），状态应该是"进行中"
          const status = draftIds.length > 0 ? 0 : 1 // 0表示进行中，1表示已完成

          const updateData = {
            ...listExecutionInfo.value,
            endTime: new Date().getTime(),
            status: status,
            draftTemplateIds: draftIds.join(',')
          }

          await ListExecutionApi.updateListExecution(updateData)

          if (status === 1) {
            ElMessage.success('评估完成！')
          } else {
            ElMessage.success('评估已暂存！')
          }

          // 在localStorage中设置标记，表示需要刷新评估记录页面
          localStorage.setItem('evaluation_result_refresh', 'true')
          localStorage.setItem('evaluation_result_timestamp', new Date().getTime().toString())

          // 如果是从其他页面打开的，则关闭当前页面并返回上一页
          if (window.opener) {
            // 如果是从其他窗口打开的，关闭当前窗口
            window.close()
          } else {
            // 使用新的标签页跳转函数
            handleEvaluationComplete()
          }
        }
      } catch (error) {
        console.error('提交评估清单失败:', error)
        ElMessage.error('提交评估清单失败')
      }
    } else if (success) {
      // 如果不是最后一项，跳转到下一个模板
      let nextIncompleteStep = -1

      // 更新正确的步骤状态为暂存
      // 如果正在查看上一步，更新正在查看的步骤；否则更新当前活动步骤
      const targetStepIndex = isViewingPreviousStep.value && previousStepIndex.value >= 0
        ? previousStepIndex.value
        : activeStep.value

      console.log('saveDraft - 更新步骤状态，目标步骤索引:', targetStepIndex, '是否正在查看上一步:', isViewingPreviousStep.value)

      steps.value[targetStepIndex].description = '暂存'
      steps.value[targetStepIndex].status = 'warning'

      // 检查是否是最后一个模板（不是综合评估步骤）
      const isLastTemplate = activeStep.value === steps.value.length - 2

      // 如果是最后一个模板，暂存后应该跳转到评估记录页面
      if (isLastTemplate) {
        console.log('最后一个模板暂存，准备跳转到评估记录页面')

        // 检查是否有暂存的模板（包括当前模板，因为当前模板已经暂存了）
        const draftIds = listExecutionInfo.value?.draftTemplateIds?.split(',').filter(id => id) || []
        const currentTemplateIdStr = String(targetTemplateId)

        // 确保当前模板ID在暂存列表中
        if (!draftIds.includes(currentTemplateIdStr)) {
          draftIds.push(currentTemplateIdStr)
        }

        // 更新清单状态为"进行中"
        if (listExecutionInfo.value) {
          try {
            // 准备更新数据，明确排除listRule字段避免重复计算分数
            const { listRule, ...updateDataWithoutListRule } = listExecutionInfo.value || {}
            const updateData = {
              ...updateDataWithoutListRule,
              status: 0, // 0表示进行中
              draftTemplateIds: draftIds.join(',') // 包括当前模板
            }
            await ListExecutionApi.updateListExecution(updateData)
            console.log('清单状态已更新为进行中，暂存模板ID:', draftIds)
          } catch (error) {
            console.error('更新评估任务清单状态失败:', error)
          }
        }

        // 使用新的标签页跳转函数
        handleEvaluationComplete()
        return
      }

      // 如果正在查看上一步，暂存后应该返回到正在进行的模板
      if (isViewingPreviousStep.value) {
        console.log('正在查看上一步暂存，准备返回到正在进行的模板，activeStep:', activeStep.value)
        nextIncompleteStep = activeStep.value
      } else if (isFromApplication) {
        // 如果是从评估应用进入，直接按顺序进入下一个模板
        nextIncompleteStep = activeStep.value + 1

        // 确保不超出模板数量范围
        if (nextIncompleteStep >= templateIds.value.length) {
          nextIncompleteStep = -1
        }
      } else {
        // 如果是从评估记录进入，查找下一个未完成的模板（优先级：最后一个进行中 > 暂存 > 待完成）

        // 首先查找进行中的模板（从后往前查找，优先跳转到最后一个进行中的模板，排除当前模板和综合分析步骤）
        for (let i = templateIds.value.length - 1; i >= 0; i--) {
          if (i !== activeStep.value && steps.value[i].description === '进行中') {
            nextIncompleteStep = i
            break
          }
        }

        // 如果没有找到进行中的，查找暂存的模板（从头开始按顺序查找，排除当前模板和综合分析步骤）
        if (nextIncompleteStep === -1) {
          for (let i = 0; i < templateIds.value.length; i++) {
            if (i !== activeStep.value && steps.value[i].description === '暂存') {
              nextIncompleteStep = i
              break
            }
          }
        }

        // 如果没有找到暂存的，查找待完成的模板（从头开始按顺序查找，排除当前模板和综合分析步骤）
        if (nextIncompleteStep === -1) {
          for (let i = 0; i < templateIds.value.length; i++) {
            if (i !== activeStep.value && steps.value[i].description === '待完成') {
              nextIncompleteStep = i
              break
            }
          }
        }
      }

      // 如果找到了未完成的模板，跳转到该模板
      if (nextIncompleteStep !== -1) {
        // 清除查看上一步的状态
        isViewingPreviousStep.value = false
        previousStepIndex.value = -1

        activeStep.value = nextIncompleteStep

        // 检查是否是最后一个步骤（清单综合分析）
        if (nextIncompleteStep === steps.value.length - 1) {
          // 清单综合分析步骤不需要加载模板
          currentTemplateId.value = null

          // 清空AI分析结果
          aiReasoning.value = ''
          aiAnalysis.value = ''
          tempReasoning.value = ''
          tempAnalysis.value = ''
          isAnalysisFinish.value = false

          // 将新的当前步骤设置为"进行中"
          steps.value[nextIncompleteStep].description = '进行中'
          steps.value[nextIncompleteStep].status = 'process'

          // 滚动表单到顶部
          scrollToTop()
        } else {
          // 确保模板ID是有效的数字
          const templateId = templateIds.value[nextIncompleteStep]
          if (templateId && !isNaN(Number(templateId))) {
            currentTemplateId.value = Number(templateId)

            // 清空AI分析结果
            aiReasoning.value = ''
            aiAnalysis.value = ''
            tempReasoning.value = ''
            tempAnalysis.value = ''
            isAnalysisFinish.value = false

            // 将新的当前步骤设置为"进行中"
            steps.value[nextIncompleteStep].description = '进行中'
            steps.value[nextIncompleteStep].status = 'process'

            // 获取并更新模板名称
            try {
              const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
              if (templateData && templateData.name) {
                evalInfo.templateName = templateData.name
                steps.value[nextIncompleteStep].title = templateData.name
              }
            } catch (error) {
              console.error('获取模板信息失败:', error)
            }

            // 加载新模板
            await loadTemplate(currentTemplateId.value)

            // 滚动表单到顶部
            scrollToTop()
          } else {
            console.error('无效的模板ID:', templateId)
            ElMessage.error('无效的模板ID')
          }
        }
      } else {
        // 如果没有未完成的模板，显示所有模板已完成或暂存
        ElMessage.info('所有模板已完成或暂存')
      }
    }
  } catch (error) {
    console.error('暂存失败:', error)
    ElMessage.error('暂存失败')
    autoSaveStatus.value = '保存失败'
  } finally {
    savingDraft.value = false
  }
}

// 生成AI分析
const generateAIAnalysis = async () => {
  if (generatingAI.value) return

  generatingAI.value = true
  try {
    // 获取当前表单数据
    const formData = previewApi.value.formData()

    // 保存当前表单数据到对应的模板ID
    if (currentTemplateId.value) {
      formDataMap.value[currentTemplateId.value] = formData
    }

    // 检查是否有执行ID
    if (!listExecutionInfo.value?.id) {
      // 如果没有执行ID，先自动保存草稿获取ID
      try {
        ElMessage.info('正在自动保存评估结果...')
        await processAndSubmitFormData(formData, true)
        if (!listExecutionInfo.value?.id) {
          ElMessage.error('无法生成AI分析，自动保存评估结果失败')
          generatingAI.value = false
          return
        }
        ElMessage.success('评估结果已自动保存')
      } catch (error) {
        console.error('自动保存评估结果失败:', error)
        ElMessage.error('无法生成AI分析，自动保存评估结果失败')
        generatingAI.value = false
        return
      }
    }

    ElMessage.info('正在生成AI分析，请稍候...')

    // 获取API密钥
    let apiKey = ''
    try {
      // 尝试获取API密钥
      if (currentTemplateId.value && !isNaN(currentTemplateId.value) && currentTemplateId.value > 0) {
        const apiKeyRes = await TemplateApi.getTemplateApiKey(currentTemplateId.value)
        if (apiKeyRes) {
          apiKey = apiKeyRes
        }
      } else {
        console.error('无效的模板ID:', currentTemplateId.value)
        ElMessage.error('无法获取API密钥：无效的模板ID')
      }
    } catch (error) {
      console.error('获取API密钥失败:', error)
      ElMessage.warning('获取API密钥失败，将使用模拟数据')
    }

    // 检查是否选择了AI模型
    if (!selectedModelId.value) {
      ElMessage.error('请先选择AI模型')
      generatingAI.value = false
      return
    }

    // 准备AI输入数据基本信息
    let aiInputData = `老人信息：\n姓名：${elderInfo.value?.name}，身份证号：${elderInfo.value?.idNumber}，性别：${elderInfo.value?.gender === 1 ? '男' : '女'}，出生日期：${elderInfo.value?.birthDate ? (Array.isArray(elderInfo.value.birthDate) ? elderInfo.value.birthDate.join('-') : elderInfo.value.birthDate) : ''}\n`
    aiInputData += `评估表标题：${evalInfo.templateName || '未命名表单'}\n\n`

    // 调用后端接口转换表单数据为可读文本
    try {
      // 将表单规则转换为JSON字符串
      const formRuleJson = JSON.stringify(templateRule.value || [])

      // 调用后端接口
      const convertResponse = await ResultApi.convertFormData({
        formData: JSON.stringify(formData),
        formRule: formRuleJson
      })

      // 如果转换成功，使用转换后的文本
      if (convertResponse) {
        aiInputData += convertResponse
      } else {
        // 如果转换失败，使用JSON格式
        aiInputData += `评估数据：\n${JSON.stringify(formData, null, 2)}`
      }
    } catch (error) {
      console.error('转换表单数据失败:', error)
      // 如果转换失败，使用JSON格式
      aiInputData += `评估数据：\n${JSON.stringify(formData, null, 2)}`
    }

    // 显示对话框，让用户知道正在处理
    finishDialogVisible.value = true
    tempReasoning.value = "正在分析数据，请稍候..."

    // 调用后端AI分析接口
    const controller = new AbortController()
    let currentContent = ''

    try {
      await AiEvaluationApi.generateAnalysisStream({
        data: {
          modelId: selectedModelId.value,
          content: aiInputData,
          stream: true
        },
        onMessage: (event) => {
          try {
            const data = JSON.parse(event.data)
            if (data.code === 0 && data.data) {
              const content = data.data.content || ''
              currentContent += content

              // 检查是否包含 [think] 和 [/think] 标签
              if (currentContent.includes('[/think]')) {
                const parts = currentContent.split('[/think]')
                if (parts.length === 2) {
                  // 提取 think 标签中的内容
                  tempReasoning.value = parts[0].replace('[think]', '').trim()
                  // 提取 think 标签后的内容
                  tempAnalysis.value = parts[1].trim()
                } else {
                  // 如果分割失败，将所有内容作为分析结果
                  tempAnalysis.value = currentContent
                  tempReasoning.value = ''
                }
              } else {
                // 如果没有标签，将所有内容作为分析结果
                tempAnalysis.value = currentContent
                tempReasoning.value = ''
              }

              // 检查是否完成
              if (data.data.finished) {
                isAnalysisFinish.value = true
                aiReasoning.value = tempReasoning.value
                aiAnalysis.value = tempAnalysis.value
                isReasoningCollapsed.value = ['1']

                // 自动保存AI分析到数据库（异步执行，不阻塞UI）
                saveAIAnalysisToDatabase().catch(error => {
                  console.error('保存AI分析失败:', error)
                })

                generatingAI.value = false
              }
            }
          } catch (error) {
            console.error('解析AI响应失败:', error)
          }
        },
        onError: (error) => {
          console.error('AI分析流式请求失败:', error)
          ElMessage.error('AI分析生成失败，请稍后重试')
          generatingAI.value = false
          finishDialogVisible.value = false
        },
        onClose: () => {
          console.log('AI分析流式连接关闭')
          if (!isAnalysisFinish.value) {
            generatingAI.value = false
          }
        },
        ctrl: controller
      })
    } catch (error) {
      console.error('调用AI分析接口失败:', error)
      ElMessage.error('AI分析生成失败，请稍后重试')
      generatingAI.value = false
      finishDialogVisible.value = false
    }
  } catch (error) {
    console.error('生成AI分析失败:', error)
    ElMessage.error('生成AI分析失败')
    generatingAI.value = false
  }
}

// 保存AI分析到数据库
const saveAIAnalysisToDatabase = async () => {
  try {
    // 检查是否有当前模板ID
    if (!currentTemplateId.value) {
      console.log('无当前模板ID，跳过保存AI分析')
      return
    }

    // 检查是否有清单执行ID
    if (!listExecutionInfo.value?.id) {
      console.log('无清单执行ID，跳过保存AI分析')
      return
    }

    // 检查是否有AI分析内容
    if (!aiAnalysis.value) {
      console.log('无AI分析内容，跳过保存')
      return
    }

    // 查找当前模板对应的评估结果ID（包括进行中状态的记录）
    let resultId = null

    try {
      const existingResults = await ResultApi.getResultPage({
        templateId: currentTemplateId.value,
        listExecutionId: listExecutionInfo.value.id,
        pageNo: 1,
        pageSize: 10
      })

      if (existingResults.list && existingResults.list.length > 0) {
        resultId = existingResults.list[0].id
      }
    } catch (error) {
      console.error('查询评估结果失败:', error)
    }

    // 如果找到了结果ID，保存AI分析
    if (resultId) {
      const aiAnalysisData = `[think]${aiReasoning.value}[/think]\n${aiAnalysis.value}`

      await ResultApi.updateAiAnalysis({
        id: resultId,
        aiAnalysis: aiAnalysisData
      })

      ElMessage.success('AI分析生成并保存成功')
    } else {
      ElMessage.warning('AI分析生成成功，但无法找到对应的评估记录')
    }
  } catch (error) {
    console.error('保存AI分析失败:', error)
    ElMessage.error('AI分析生成成功，但保存失败')
  }
}

// 使用模拟数据生成AI分析
const useSimulatedAIAnalysis = async () => {
  // 生成示例AI分析内容
  const analysis = `## 评估分析\n\n根据对老人的评估结果，分析如下：\n\n1. **身体状况**：老人身体状况总体${Math.random() > 0.5 ? '良好' : '一般'}，需要注意${Math.random() > 0.5 ? '血压控制' : '血糖管理'}。\n\n2. **心理状态**：老人心理状态${Math.random() > 0.5 ? '稳定' : '需要关注'}，建议增加${Math.random() > 0.5 ? '社交活动' : '家庭陪伴'}。\n\n3. **生活能力**：老人日常生活能力${Math.random() > 0.5 ? '自理' : '部分需要帮助'}，建议提供${Math.random() > 0.5 ? '适当辅助' : '定期照护'}。\n\n## 建议\n\n1. 定期复查健康状况\n2. 增加户外活动时间\n3. 保持良好的社交关系\n4. 均衡饮食，保证营养摄入`;

  // 模拟思考过程
  aiReasoning.value = "分析老人的评估数据...\n根据评估表单中的数据，老人的生活自理能力、认知功能和情绪状态都处于正常范围。"

  // 更新AI分析内容
  aiAnalysis.value = analysis
  tempAnalysis.value = analysis
  isAnalysisFinish.value = true

  // 保存AI分析到本地变量，不调用后端接口
  if (listExecutionInfo.value?.id) {
    // 只在本地显示成功消息，不更新listExecutionInfo对象
    // 这样可以避免在提交表单时将AI分析结果保存到清单库表中
    // listExecutionInfo.value.aiAnalysis = `[think]${aiReasoning.value}[/think]\n` + analysis
    ElMessage.success('AI分析生成成功')
  }

  generatingAI.value = false
}

// 设置自动保存
const setupAutoSave = () => {
  // 禁用自动保存功能
  if (autoSaveInterval.value !== null) {
    clearInterval(autoSaveInterval.value)
    autoSaveInterval.value = null
  }

  // 不再设置新的自动保存定时器
}

// 在组件卸载前清除自动保存定时器
onBeforeUnmount(() => {
  if (autoSaveInterval.value !== null) {
    clearInterval(autoSaveInterval.value)
  }
})

// 查看上一步表单结果
const viewPreviousStep = async (index: number) => {
  console.log('=== viewPreviousStep 开始 ===')
  console.log('目标查看步骤索引:', index)
  console.log('当前activeStep:', activeStep.value)
  console.log('当前currentTemplateId:', currentTemplateId.value)
  console.log('当前isViewingPreviousStep:', isViewingPreviousStep.value)

  if (index < 0 || index >= activeStep.value) {
    message.error('无效的步骤索引')
    return
  }

  try {
    loading.value = true // 开始加载，设置加载状态为true

    // 保存当前的评估师分析内容，以便返回时恢复
    const currentEvaluatorAnalysis = evaluatorAnalysis.value

    // 记录当前查看的步骤索引
    previousStepIndex.value = index
    isViewingPreviousStep.value = true

    // 获取上一步的模板ID和结果ID
    const templateId = templateIds.value[index]
    console.log('获取上一步的模板ID:', templateId, '索引:', index)
    console.log('templateIds数组:', templateIds.value)

    if (!templateId || isNaN(Number(templateId))) {
      console.error('无效的模板ID:', templateId)
      ElMessage.error('无效的模板ID')
      loading.value = false
      return
    }

    const prevTemplateId = Number(templateId)
    const prevResultId = resultIdsArray.value[index]
    console.log('设置prevTemplateId:', prevTemplateId, 'prevResultId:', prevResultId)

    // 加载上一步模板
    console.log('准备加载模板，prevTemplateId:', prevTemplateId)
    await loadTemplate(prevTemplateId)
    console.log('模板加载完成')

    // 隐藏form-create默认提交按钮
    if (templateOption.value.submitBtn) {
      templateOption.value.submitBtn.show = false
    } else {
      templateOption.value.submitBtn = { show: false }
    }

    // 获取上一步的表单数据并设置
    let prevFormData = formDataMap.value[templateIds.value[index]]
    let prevEvaluatorAnalysis = ''
    let prevAiAnalysis = ''

    // 无论是否有缓存数据，都要从API获取分析数据
    if (prevResultId) {
      try {
        // console.log('从API获取表单结果:', prevResultId)
        const resultData = await ResultApi.getResult(Number(prevResultId))

        if (resultData) {
          // 获取评估师分析和AI分析数据
          if (resultData.evaluatorAnalysis) {
            prevEvaluatorAnalysis = resultData.evaluatorAnalysis
            console.log('获取到评估师分析数据:', prevEvaluatorAnalysis)
          }
          if (resultData.aiAnalysis) {
            prevAiAnalysis = resultData.aiAnalysis
            console.log('获取到AI分析数据:', prevAiAnalysis)
          }

          // 如果缓存中没有表单数据，则从API获取
          if (!prevFormData && resultData.result) {
            // 解析结果数据
            const parsedResult = JSON.parse(resultData.result)
            // console.log('API获取的表单结果数据:', parsedResult)

            // 处理表单数据 - 兼容多种数据结构
            if (parsedResult.formData) {
              // 新数据结构直接使用 formData
              prevFormData = parsedResult.formData
            }
            // 再检查是否有 hierarchicalResults 或 fullResult，这是新的层次化结构
            else if (parsedResult.fullResult && parsedResult.fullResult.rawFormData) {
              // 从完整的 JSON 结构中提取 rawFormData
              prevFormData = parsedResult.fullResult.rawFormData
            }
            // 最后尝试旧的数据结构
            else if (
              parsedResult.assessmentResults &&
              Array.isArray(parsedResult.assessmentResults)
            ) {
              prevFormData = {}
              try {
                parsedResult.assessmentResults.forEach((result: any) => {
                  if (result.items && Array.isArray(result.items)) {
                    result.items.forEach((item: any) => {
                      const rule = item.props && item.props.field ? item.props.field : item.name
                      prevFormData[rule] = item.value
                    })
                  }
                })
              } catch (error) {
                // console.error('处理旧格式评估结果时出错:', error)
                ElMessage.error('处理旧格式评估结果时出错')
              }
            }

            // 如果有规则数据，保存到规则缓存中
            if (parsedResult.rules) {
              formDataMap.value[`${prevTemplateId}_rules`] = parsedResult.rules
            }

            // 保存到缓存中
            if (prevFormData) {
              formDataMap.value[templateIds.value[index]] = prevFormData
            }
          }
        }
      } catch (apiError) {
        // console.error('从API获取表单结果失败:', apiError)
        ElMessage.error('无法获取表单历史数据')
      }
    }

    // 不再使用sessionStorage保存临时数据，所有数据都从数据库获取

    // 设置上一步的分析数据（只从数据库获取）
    if (prevEvaluatorAnalysis) {
      evaluatorAnalysis.value = prevEvaluatorAnalysis
      console.log('从数据库恢复评估师分析:', prevEvaluatorAnalysis)
    } else {
      evaluatorAnalysis.value = ''
    }

    // 设置AI分析数据（只从数据库获取）
    if (prevAiAnalysis) {
      console.log('开始处理数据库AI分析数据:', prevAiAnalysis)
      // 解析AI分析数据，提取思考过程和分析结果
      if (prevAiAnalysis.includes('[think]') && prevAiAnalysis.includes('[/think]')) {
        const thinkMatch = prevAiAnalysis.match(/\[think\](.*?)\[\/think\]/s)
        if (thinkMatch) {
          aiReasoning.value = thinkMatch[1].trim()
          aiAnalysis.value = prevAiAnalysis.replace(/\[think\].*?\[\/think\]\n?/s, '').trim()
          console.log('解析AI思考过程:', aiReasoning.value)
          console.log('解析AI分析结果:', aiAnalysis.value)
        } else {
          aiAnalysis.value = prevAiAnalysis
          console.log('设置完整AI分析:', aiAnalysis.value)
        }
      } else {
        aiAnalysis.value = prevAiAnalysis
        console.log('设置完整AI分析:', aiAnalysis.value)
      }
      isAnalysisFinish.value = true
      console.log('AI分析完成状态:', isAnalysisFinish.value)
    } else {
      // 清空AI分析相关变量
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false
      console.log('清空AI分析数据')
    }

    // 更新进度条状态为"正在查看"
    if (steps.value[index]) {
      steps.value[index].originalDescription = steps.value[index].description
      steps.value[index].originalStatus = steps.value[index].status
      steps.value[index].description = '正在查看'
      steps.value[index].status = 'process'
    }

    if (prevFormData) {
      // 保存当前步骤的表单数据到formDataMap，以便返回时恢复
      if (currentTemplateId.value && previewApi.value) {
        const currentFormData = previewApi.value.formData()
        if (currentFormData && Object.keys(currentFormData).length > 0) {
          formDataMap.value[currentTemplateId.value] = JSON.parse(JSON.stringify(currentFormData))
          console.log('保存当前步骤表单数据到formDataMap:', currentTemplateId.value, currentFormData)
        }
      }

      // 保存当前表单数据，以便返回时恢复
      previousStepFormData.value = JSON.parse(JSON.stringify(prevFormData))

      // console.log('设置上一步表单数据:', prevFormData)

      // 先设置表单值引用
      previewForm.value = prevFormData

      // 等待组件渲染完成后设置表单数据
      nextTick(() => {
        if (previewApi.value) {
          try {
            // 先重置表单，避免数据混淆
            previewApi.value.resetFields()

            // 然后使用setValue方法设置表单值
            previewApi.value.setValue(prevFormData)

            // 额外确认表单数据是否被正确设置
            const currentFormData = previewApi.value.formData()
            // console.log('设置后的表单数据确认:', currentFormData)

            // 如果需要，强制更新表单
            nextTick(() => {
              if (previewApi.value) {
                previewApi.value.refresh()
              }
            })
          } catch (error) {
            // console.error('设置表单数据失败:', error)
            ElMessage.error('设置表单数据失败')
          }
        }
      })
    } else {
      message.warning('未找到表单历史数据')
    }

    // 更新活动步骤，但不影响已完成状态
    console.log('设置currentTemplateId为prevTemplateId:', prevTemplateId)
    currentTemplateId.value = prevTemplateId
    console.log('currentTemplateId设置完成，当前值:', currentTemplateId.value)

    // 先更新评估表单名称，确保标题和内容同步
    evalInfo.templateName = steps.value[index]?.title || `评估表单 ${index + 1}`
    console.log('更新评估表单名称:', evalInfo.templateName)

    // 获取当前模板的完整信息以更新类型和有效期
    try {
      if (prevTemplateId && !isNaN(Number(prevTemplateId)) && Number(prevTemplateId) > 0) {
        const templateData = await TemplateApi.getTemplate(prevTemplateId)
        if (templateData && templateData.name) {
          evalInfo.templateName = templateData.name
          // 同时更新步骤标题，确保一致性
          steps.value[index].title = templateData.name

          // 解析formSchema提取模板类型和有效期设置
          if (templateData.formSchema) {
            try {
              const schema = JSON.parse(templateData.formSchema)
              if (schema.option && schema.option.form) {
                templateType.value = schema.option.form.templateType || 0
              }
            } catch (e) {
              console.error('解析模板类型数据失败:', e)
              ElMessage.error('解析模板类型数据失败')
            }
          }
        }
      }
    } catch (templateError) {
      console.error('获取模板详情信息失败:', templateError)
      ElMessage.error('获取模板详情信息失败')
    }

    message.success(`正在查看${steps.value[index]?.title || `第${index + 1}步表单`}`)
  } catch (error) {
    // console.error('查看上一步表单失败:', error)
    ElMessage.error('查看上一步表单失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 返回当前步骤
const returnToCurrentStep = async () => {
  console.log('=== returnToCurrentStep 开始 ===')
  console.log('当前activeStep:', activeStep.value)
  console.log('当前currentTemplateId:', currentTemplateId.value)
  console.log('是否正在查看上一步:', isViewingPreviousStep.value)
  console.log('previousStepIndex:', previousStepIndex.value)
  console.log('templateIds数组:', templateIds.value)

  if (!isViewingPreviousStep.value) {
    console.log('不在查看上一步状态，直接返回')
    return
  }

  try {
    loading.value = true // 开始加载，设置加载状态为true

    // 恢复之前查看步骤的原始状态
    // 但是如果步骤已经被更新为"已完成"，则不恢复原始状态
    if (previousStepIndex.value >= 0 && steps.value[previousStepIndex.value]) {
      const prevStep = steps.value[previousStepIndex.value]
      if (prevStep.originalDescription) {
        // 检查当前状态是否已经是"已完成"，如果是则不恢复原始状态
        if (prevStep.description !== '已完成' && prevStep.status !== 'finish') {
          console.log('恢复步骤原始状态:', prevStep.originalDescription)
          prevStep.description = prevStep.originalDescription
          prevStep.status = prevStep.originalStatus
        } else {
          console.log('步骤已完成，不恢复原始状态，保持已完成状态')
        }
        delete prevStep.originalDescription
        delete prevStep.originalStatus
      }
    }

    // 重置查看上一步的状态
    isViewingPreviousStep.value = false
    previousStepIndex.value = -1

    // 保持隐藏form-create默认提交按钮
    if (templateOption.value.submitBtn) {
      templateOption.value.submitBtn.show = false
    } else {
      templateOption.value.submitBtn = { show: false }
    }

    // 检查当前步骤是否是综合分析步骤（最后一个步骤）
    console.log('检查当前步骤类型，activeStep.value:', activeStep.value, 'steps.value.length:', steps.value.length)

    if (activeStep.value === steps.value.length - 1) {
      // 综合分析步骤，不需要加载模板
      console.log('当前是综合分析步骤，不需要加载模板')
      currentTemplateId.value = null
      console.log('综合分析步骤：currentTemplateId设置为null')

      // 清空AI分析相关变量，避免显示之前模板的分析结果
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false

      // 更新模板名称为综合分析
      evalInfo.templateName = '综合分析'
    } else {
      // 普通模板步骤，需要加载模板
      console.log('当前是普通模板步骤，需要加载模板')
      const targetTemplateId = templateIds.value[activeStep.value]
      console.log('准备设置currentTemplateId，activeStep.value:', activeStep.value)
      console.log('从templateIds获取的ID:', targetTemplateId)
      console.log('转换为数字前的验证:', targetTemplateId, 'isNaN:', isNaN(Number(targetTemplateId)))

      if (targetTemplateId && !isNaN(Number(targetTemplateId)) && Number(targetTemplateId) > 0) {
        currentTemplateId.value = Number(targetTemplateId)
        console.log('currentTemplateId设置成功:', currentTemplateId.value)
      } else {
        console.error('无效的模板ID，无法设置currentTemplateId:', targetTemplateId)
        ElMessage.error('无效的模板ID')
        return
      }

      // 先更新模板名称，确保标题和内容同步
      evalInfo.templateName = steps.value[activeStep.value]?.title || `评估表单 ${activeStep.value + 1}`

      console.log('准备加载模板，currentTemplateId.value:', currentTemplateId.value)
      await loadTemplate(currentTemplateId.value)
      console.log('模板加载完成')
    }

    // 恢复当前步骤的表单数据（如果有的话）
    // 只有在非综合分析步骤时才恢复表单数据
    if (previewApi.value && currentTemplateId.value !== null) {
      const currentStepFormData = formDataMap.value[currentTemplateId.value]
      if (currentStepFormData) {
        console.log('恢复当前步骤的表单数据:', currentStepFormData)
        previewApi.value.setValue(currentStepFormData)
      } else {
        console.log('当前步骤无保存的表单数据，重置表单')
        previewApi.value.resetFields()
      }
    } else if (currentTemplateId.value === null) {
      console.log('综合分析步骤，跳过表单数据恢复')
    }

    // 从数据库加载当前步骤的评估师分析
    // 只有在非综合分析步骤时才加载评估师分析
    if (activeStep.value !== steps.value.length - 1) {
      try {
        const currentResultId = resultIdsArray.value[activeStep.value]
        console.log('尝试加载评估师分析，currentResultId:', currentResultId)
        if (currentResultId) {
          const resultData = await ResultApi.getResult(Number(currentResultId))
          if (resultData && resultData.evaluatorAnalysis) {
            evaluatorAnalysis.value = resultData.evaluatorAnalysis
            console.log('从数据库恢复评估师分析:', resultData.evaluatorAnalysis)
          } else {
            evaluatorAnalysis.value = ''
          }
        } else {
          evaluatorAnalysis.value = ''
        }
      } catch (error) {
        console.error('从数据库加载评估师分析失败:', error)
        evaluatorAnalysis.value = ''
      }
    } else {
      console.log('综合分析步骤，跳过评估师分析加载')
      evaluatorAnalysis.value = ''
    }

    // 恢复当前步骤的AI分析数据
    await restoreCurrentStepAIAnalysis()

    // 获取当前模板的完整信息以更新评估表单名称
    try {
      if (currentTemplateId.value && !isNaN(currentTemplateId.value) && currentTemplateId.value > 0) {
        const templateData = await TemplateApi.getTemplate(currentTemplateId.value)
        if (templateData && templateData.name) {
          evalInfo.templateName = templateData.name
          // 同时更新步骤标题，确保一致性
          steps.value[activeStep.value].title = templateData.name

          // 解析formSchema提取模板类型和有效期设置
          if (templateData.formSchema) {
            try {
              const schema = JSON.parse(templateData.formSchema)
              if (schema.option && schema.option.form) {
                templateType.value = schema.option.form.templateType || 0
              }
            } catch (e) {
              console.error('解析模板类型数据失败:', e)
              ElMessage.error('解析模板类型数据失败')
            }
          }
        }
      }
    } catch (templateError) {
      console.error('获取模板详情信息失败:', templateError)
      ElMessage.error('获取模板详情信息失败')
    }

    message.success('已返回当前步骤')
  } catch (error) {
    // console.error('返回当前步骤失败:', error)
    ElMessage.error('返回当前步骤失败')
  } finally {
    loading.value = false // 完成加载，设置加载状态为false
  }
}

// 手动更新步骤 - 按钮事件处理函数
const manualUpdateStep = async () => {
  try {
    // 显示加载状态
    loading.value = true

    if (!previewApi.value) {
      message.error('表单API未初始化')
      return
    }

    // 直接从form-create API获取完整表单数据
    const formData = previewApi.value.formData()
    // console.log('手动更新 - 获取到的表单数据:', formData)

    // 验证表单数据是否为空或只包含元数据
    const fieldKeys = Object.keys(formData).filter((key) => !['_vts', 'isTrusted'].includes(key))
    if (fieldKeys.length === 0) {
      message.error('无法获取有效的表单数据，请检查表单')
      return
    }

    // 调用提交处理函数
    const success = await handleSubmit(formData)

    // 如果更新成功，自动返回当前步骤
    if (success) {
      // 等待一段时间再返回，让用户看到成功消息
      setTimeout(() => {
        returnToCurrentStep()
        scrollToTop()
      }, 1000)
    }
  } catch (error) {
    // console.error('手动更新表单数据失败:', error)
    ElMessage.error('手动更新表单数据失败')
  } finally {
    loading.value = false
  }
}

// 保存当前评估师分析
const saveCurrentEvaluatorAnalysis = async () => {
  try {
    if (!evaluatorAnalysis.value) {
      ElMessage.warning('评估师分析为空，无需保存')
      return
    }

    // 确定要操作的模板ID
    // 如果正在查看上一步，使用正在查看的模板ID；否则使用当前模板ID
    const targetTemplateId = isViewingPreviousStep.value && previousStepIndex.value >= 0
      ? Number(templateIds.value[previousStepIndex.value])
      : currentTemplateId.value

    console.log('保存评估师分析 - 目标模板ID:', targetTemplateId, '是否正在查看上一步:', isViewingPreviousStep.value)

    // 检查是否有目标模板ID
    if (!targetTemplateId) {
      ElMessage.error('无法保存评估师分析，模板ID为空')
      return
    }

    // 检查是否有清单执行ID
    if (!listExecutionInfo.value?.id) {
      ElMessage.error('无法保存评估师分析，清单执行ID为空')
      return
    }

    // 查找目标模板对应的评估结果ID（包括进行中状态的记录）
    let resultId = null

    try {
      const existingResults = await ResultApi.getResultPage({
        templateId: targetTemplateId,
        listExecutionId: listExecutionInfo.value.id,
        pageNo: 1,
        pageSize: 10
      })

      if (existingResults.list && existingResults.list.length > 0) {
        resultId = existingResults.list[0].id
        console.log('找到评估结果ID:', resultId)
      }
    } catch (error) {
      console.error('查询评估结果失败:', error)
    }

    // 如果找到了结果ID，直接更新评估师分析
    if (resultId) {
      await ResultApi.updateEvaluatorAnalysis({
        id: resultId,
        evaluatorAnalysis: evaluatorAnalysis.value
      })

      ElMessage.success('评估师分析已保存')
    } else {
      ElMessage.error('保存评估师分析失败，无法找到评估记录')
    }
  } catch (error) {
    console.error('保存评估师分析失败:', error)
    ElMessage.error('保存评估师分析失败')
  }
}

// 恢复当前步骤的AI分析数据
const restoreCurrentStepAIAnalysis = async () => {
  try {
    // 检查是否是综合分析步骤
    if (activeStep.value === steps.value.length - 1) {
      console.log('综合分析步骤，跳过AI分析数据恢复')
      // 综合分析步骤，清空AI分析相关变量
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false
      return
    }

    // 从API获取当前步骤的AI分析数据
    const currentResultId = resultIdsArray.value[activeStep.value]

    if (currentResultId) {
      console.log('从API恢复当前步骤AI分析，结果ID:', currentResultId)
      const resultData = await ResultApi.getResult(Number(currentResultId))

      if (resultData && resultData.aiAnalysis) {
        console.log('从API恢复当前步骤AI分析数据:', resultData.aiAnalysis)

        // 解析AI分析数据，提取思考过程和分析结果
        if (resultData.aiAnalysis.includes('[think]') && resultData.aiAnalysis.includes('[/think]')) {
          const thinkMatch = resultData.aiAnalysis.match(/\[think\](.*?)\[\/think\]/s)
          if (thinkMatch) {
            aiReasoning.value = thinkMatch[1].trim()
            aiAnalysis.value = resultData.aiAnalysis.replace(/\[think\].*?\[\/think\]\n?/s, '').trim()
            console.log('恢复AI思考过程:', aiReasoning.value)
            console.log('恢复AI分析结果:', aiAnalysis.value)
          } else {
            aiAnalysis.value = resultData.aiAnalysis
            console.log('恢复完整AI分析:', aiAnalysis.value)
          }
        } else {
          aiAnalysis.value = resultData.aiAnalysis
          console.log('恢复完整AI分析:', aiAnalysis.value)
        }
        isAnalysisFinish.value = true
        console.log('AI分析恢复完成状态:', isAnalysisFinish.value)
      } else {
        // 当前步骤没有AI分析数据，清空相关变量
        aiReasoning.value = ''
        aiAnalysis.value = ''
        tempReasoning.value = ''
        tempAnalysis.value = ''
        isAnalysisFinish.value = false
        console.log('当前步骤无AI分析数据，已清空')
      }
    } else {
      // 当前步骤没有结果ID，清空AI分析相关变量
      aiReasoning.value = ''
      aiAnalysis.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false
      console.log('当前步骤无结果ID，已清空AI分析数据')
    }
  } catch (error) {
    console.error('恢复当前步骤AI分析失败:', error)
    // 发生错误时清空AI分析相关变量
    aiReasoning.value = ''
    aiAnalysis.value = ''
    tempReasoning.value = ''
    tempAnalysis.value = ''
    isAnalysisFinish.value = false
  }
}

// 添加计算剩余有效期的函数
const getRemainingValidity = () => {
  if (
    !templateDetail.value ||
    templateDetail.value.validityStartTimeType !== 'fixedDate' ||
    !templateDetail.value.validityStartTime
  ) {
    return null
  }

  try {
    // 获取固定起始日期
    const startDate = new Date(templateDetail.value.validityStartTime)
    // 获取有效期和单位
    const validityPeriod = templateDetail.value.validityPeriod || 3
    const validityUnit = templateDetail.value.validityUnit || 'month'

    // 计算到期日期
    let expiryDate = new Date(startDate)
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 计算剩余天数
    const today = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

    return daysLeft > 0 ? daysLeft : 0
  } catch (error) {
    // console.error('计算剩余有效期失败:', error)
    ElMessage.error('计算剩余有效期失败')
    return null
  }
}

// 声明引用
const resultContainer = ref(null)
const reasoningContainer = ref(null)

// 监听tempAnalysis的变化，确保内容更新时自动滚动到底部
watch(
  () => tempAnalysis.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (resultContainer.value) {
          // 自动滚动到底部，确保用户能看到最新内容
          resultContainer.value.scrollTop = resultContainer.value.scrollHeight
        }
      })
    }
  }
)

// 添加对tempReasoning的监听
watch(
  () => tempReasoning.value,
  (newVal, oldVal) => {
    if (newVal && (!oldVal || newVal.length > oldVal.length)) {
      nextTick(() => {
        if (reasoningContainer.value) {
          // 自动滚动到底部，确保用户能看到最新思考内容
          reasoningContainer.value.scrollTop = reasoningContainer.value.scrollHeight
        }
      })
    }
  }
)

// 初始化
onMounted(() => {
  loading.value = true // 开始加载，设置加载状态为true

  templateTypeOptions.value = getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)
  loadEvalInfo()

  // 设置自动保存
  setupAutoSave()

  // 加载AI模型列表
  loadAiModels()
})

// 工具函数：脱敏处理
const maskString = (str: string, start: number, end: number) => {
  if (!str) return ''
  const maskLength = end - start
  const maskStr = '*'.repeat(maskLength)
  return str.substring(0, start) + maskStr + str.substring(end)
}

// 计算属性：脱敏后的身份证号
const maskedIdNumber = computed(() => {
  if (!elderInfo.value?.idNumber) return ''
  return maskString(elderInfo.value.idNumber, 6, 14)
})

// 计算属性：脱敏后的联系电话
const maskedPhone = computed(() => {
  if (!elderInfo.value?.contactPhone) return ''
  return maskString(elderInfo.value.contactPhone, 3, 7)
})

// 计算属性：可查看的步骤（已完成和暂存的步骤）
const viewableSteps = computed(() => {
  const viewable = []

  // 遍历所有步骤，找出已完成和暂存的步骤
  for (let i = 0; i < steps.value.length - 1; i++) { // 排除最后的综合分析步骤
    const step = steps.value[i]
    // 只显示已完成和暂存的步骤，且不是当前正在进行的步骤
    if ((step.description === '已完成' || step.description === '暂存') && i !== activeStep.value) {
      viewable.push({
        index: i,
        title: step.title,
        description: step.description
      })
    }
  }

  return viewable
})

// 计算属性：是否有可查看的步骤
const hasViewableSteps = computed(() => {
  return viewableSteps.value.length > 0
})
</script>

<template>
  <div>
    <div class="flex flex-col h-full" v-loading="loading" element-loading-text="数据加载中...">
      <div class="flex flex-1">
        <div class="flex-1 mr-4 form-container">
          <!-- 添加步骤条 -->
          <el-card class="mb-1">
            <div class="flex justify-between items-center mb-2">
              <el-button @click="router.back()" :icon="ArrowLeft">返回</el-button>
              <!-- 查看上一步相关按钮 -->
              <div v-if="isViewingPreviousStep" class="flex items-center">
                <el-tag type="warning" class="mr-2"
                  >正在查看{{
                    steps[previousStepIndex]?.title || `第${previousStepIndex + 1}步表单`
                  }}，可以修改后提交更新</el-tag
                >
                <el-button type="primary" @click="returnToCurrentStep">返回当前步骤</el-button>
              </div>
            </div>
            <el-steps :active="activeStep" finish-status="success" class="mb-4 custom-steps">
              <el-step
                v-for="(step, index) in steps"
                :key="index"
                :title="step.title"
                :description="step.description"
                :status="step.status"
                :class="{ 'viewing-step': step.description === '正在查看' }"
              >
                <template #icon v-if="step.description === '正在查看'">
                  <div class="step-icon-clickable" @click="handleStepChange(index)">
                    <el-icon color="#FF6B35" size="20">
                      <InfoFilled />
                    </el-icon>
                  </div>
                </template>
                <template #icon v-else-if="step.description === '暂存'">
                  <div class="step-icon-clickable" @click="handleStepChange(index)">
                    <el-icon color="#E6A23C" size="20">
                      <Warning />
                    </el-icon>
                  </div>
                </template>
                <template #icon v-else-if="step.description === '已完成'">
                  <div class="step-icon-clickable" @click="handleStepChange(index)">
                    <div class="step-finished-icon">{{ index + 1 }}</div>
                  </div>
                </template>
                <template #icon v-else>
                  <div class="step-icon-clickable" @click="handleStepChange(index)">
                    <div class="step-default-icon">{{ index + 1 }}</div>
                  </div>
                </template>
                <template #title>
                  <div class="step-title-clickable" @click="handleStepChange(index)">
                    {{ step.title }}
                  </div>
                </template>
                <template #description>
                  <div class="step-description-clickable" @click="handleStepChange(index)">
                    {{ step.description }}
                  </div>
                </template>
              </el-step>
            </el-steps>
          </el-card>

          <!-- 普通模板评估表单 -->
          <el-card v-if="activeStep < (steps.length - 1) || isViewingPreviousStep">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <h2>{{ evalInfo.templateName }}</h2>
              </div>
              <div class="flex">
                <!-- 暂存按钮 -->
                <el-button
                  type="warning"
                  :loading="savingDraft"
                  @click="saveDraft"
                  class="mr-2"
                >
                  暂存
                </el-button>



                <!-- 提交按钮 -->
                <el-button
                  type="primary"
                  @click="submitForm"
                  class="mr-2"
                >
                  提交
                </el-button>

                <!-- 显示已完成和暂存步骤的查看按钮 -->
                <div v-if="!isViewingPreviousStep">
                  <el-dropdown trigger="click" :disabled="!hasViewableSteps">
                    <el-button type="primary" :disabled="!hasViewableSteps">
                      查看上一步 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-for="(step, index) in viewableSteps"
                          :key="index"
                          @click="viewPreviousStep(step.index)"
                        >
                          <div class="flex items-center justify-between w-full">
                            <span>{{ step.title }}</span>
                            <el-tag
                              v-if="step.description === '暂存'"
                              type="warning"
                              size="small"
                              class="ml-2"
                            >
                              暂存
                            </el-tag>
                            <el-tag
                              v-else-if="step.description === '已完成'"
                              type="success"
                              size="small"
                              class="ml-2"
                            >
                              已完成
                            </el-tag>
                          </div>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
            <div class="text-gray-500 text-sm mt-2 mb-2">
              <span class="mr-4">表单类型: {{ getTemplateTypeName }}</span>
              <span class="mr-4">版本号: {{ templateDetail.version || '1.0.0' }}</span>
              <span>
                <template
                  v-if="
                    templateDetail.validityStartTimeType === 'fixedDate' &&
                    templateDetail.validityStartTime
                  "
                >
                  剩余有效期: {{ getRemainingValidity() }} 天 (总有效期
                  {{ templateDetail.validityPeriod || 3 }}
                  {{ getValidityUnitText(templateDetail.validityUnit || 'month') }}，从
                  {{ templateDetail.validityStartTime }}
                  起)
                </template>
                <template v-else>
                  有效期: {{ templateDetail.validityPeriod || 3 }}
                  {{ getValidityUnitText(templateDetail.validityUnit || 'month') }} (从评估时间起)
                </template>
              </span>
            </div>
            <el-divider />
            <div class="evaluation-form" style="max-height: 50vh; overflow-y: auto">
              <form-create
                v-model:modelValue="previewForm"
                v-model:api="previewApi"
                :rule="templateRule"
                :option="templateOption"
                @submit="handleSubmit"
              />
            </div>

            <!-- 自定义提交按钮 - 仅用于查看上一步时 -->
            <div class="mt-4 flex justify-end">
              <template v-if="isViewingPreviousStep">
                <el-button type="primary" @click="manualUpdateStep">更新此步骤</el-button>
                <el-button @click="returnToCurrentStep">取消修改</el-button>
              </template>
            </div>
          </el-card>

          <!-- 清单综合分析组件 -->
          <SummaryAnalysis
            v-if="activeStep === (steps.length - 1) && !isViewingPreviousStep"
            :listExecutionInfo="listExecutionInfo"
            :readonly="false"
            @update:listExecutionInfo="listExecutionInfo = $event"
            @saved="handleSummaryAnalysisSaved"
            @evaluationComplete="handleEvaluationComplete"
          />
        </div>

        <!-- 老人信息 -->
        <el-card class="bg-white p-3 rounded-lg w-110 right-card">
          <!-- 评估信息 -->

          <div class="eval-info">
            <div class="info-item">
              <span class="label">评估师：</span>
              <span class="value">{{ evalInfo.evaluatorName }}</span>
            </div>
            <div class="info-item">
              <span class="label">评估原因：</span>
              <span class="value">{{ evalInfo.evaluationReason }}</span>
            </div>
            <!-- <div class="info-item">
              <span class="label">评估时间：</span>
              <span class="value">{{ timestampToDate(evalInfo.evaluationTime) }}</span>
            </div> -->
          </div>
          <div class="flex flex-col items-center">
            <!-- <div class="w-24 h-24 rounded-full overflow-hidden mb-4">
              <img
                src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
                class="w-full h-full object-cover"
                alt="老人头像"
              />
            </div>
            <h3 class="text-lg font-medium">{{ elderName }}</h3> -->
          </div>
          <div class="mt-1 space-y-4">
            <div class="flex justify-between">
              <span>老人姓名:</span>
              <span>{{ elderInfo?.name }}</span>
            </div>
            <div class="flex justify-between">
              <span>年龄:</span>
              <span>{{ elderInfo ? new Date().getFullYear() - elderInfo.birthDate[0] : '' }}岁</span>
            </div>
<!--            <div class="flex justify-between">-->
<!--              <span>身份证号:</span>-->
<!--              <span>{{ maskedIdNumber }}</span>-->
<!--            </div>-->
            <div class="flex justify-between">
              <span>性别:</span>
              <span>{{ elderInfo?.gender === 1 ? '男' : '女' }}</span>
            </div>
            <div class="flex justify-between">
              <span>出生日期:</span>
              <span>{{
                elderInfo?.birthDate
                  ? Array.isArray(elderInfo.birthDate)
                    ? elderInfo.birthDate.join('-')
                    : formatDate(elderInfo.birthDate)
                  : ''
              }}</span>
            </div>
<!--            <div class="flex justify-between">-->
<!--              <span>联系电话:</span>-->
<!--              <span>{{ maskedPhone }}</span>-->
<!--            </div>-->
          </div>
          <el-divider />
<!--          <div>-->
<!--            &lt;!&ndash; 评估结果展示 - 仅在 templateType 为 1 或 2 时显示 &ndash;&gt;-->
<!--            &lt;!&ndash; <div v-if="templateType === 1 || templateType === 2" class="mt-1 mb-4">-->
<!--              <div class="text-lg font-bold mb-3">评估结果</div>-->
<!--              <div class="bg-gray-50 p-4 rounded-lg text-sm">-->
<!--                <div class="flex justify-between mb-2">-->
<!--                  <span>总分：</span>-->
<!--                  <span class="font-medium">{{ assessmentResult.totalScore }}</span>-->
<!--                </div>-->
<!--                <div class="flex justify-between">-->
<!--                  <span>评估等级：</span>-->
<!--                  <span class="font-medium">{{ assessmentResult.result }}</span>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div> &ndash;&gt;-->

<!--            &lt;!&ndash; 评估师分析 - 仅在 templateType 为 1 或 2 时显示 &ndash;&gt;-->
<!--            <div v-if="(templateType === 1 || templateType === 2) && activeStep < steps.length - 1" class="mt-4">-->
<!--              <div class="mb-2 flex justify-between items-center">-->
<!--                <span>评估师分析</span>-->
<!--                <el-button-->
<!--                  type="primary"-->
<!--                  size="small"-->
<!--                  @click="saveCurrentEvaluatorAnalysis"-->
<!--                >-->
<!--                  保存-->
<!--                </el-button>-->
<!--              </div>-->
<!--              <div class="rounded-lg text-sm">-->
<!--                <div class="">-->
<!--                  <el-input-->
<!--                    type="textarea"-->
<!--                    v-model="evaluatorAnalysis"-->
<!--                    :rows="3"-->
<!--                    placeholder="请输入评估师分析"-->
<!--                  />-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->

<!--            &lt;!&ndash; AI分析区域 &ndash;&gt;-->
<!--            <div v-if="(templateType === 1 || templateType === 2) && activeStep < steps.length - 1" class="mt-4">-->
<!--              <div class="mb-3">-->
<!--                <div class="mb-2">-->
<!--                  <div class="text-lg font-bold">-->
<!--                    AI 分析-->
<!--                  </div>-->
<!--                </div>-->
<!--                &lt;!&ndash; AI模型选择 &ndash;&gt;-->
<!--                <div class="mb-3">-->
<!--                  <el-select-->
<!--                    v-model="selectedModelId"-->
<!--                    placeholder="选择AI模型"-->
<!--                    style="width: 200px"-->
<!--                    size="small"-->
<!--                  >-->
<!--                    <el-option-->
<!--                      v-for="model in aiModelList"-->
<!--                      :key="model.id"-->
<!--                      :label="model.name"-->
<!--                      :value="model.id"-->
<!--                    />-->
<!--                  </el-select>-->
<!--                </div>-->
<!--                <div class="flex space-x-2">-->
<!--                  <el-button plain type="primary" @click="viewAIAnalysis" v-if="aiAnalysis !== ''">-->
<!--                    查看-->
<!--                  </el-button>-->
<!--                  <el-button plain type="primary" @click="generateAIAnalysis" :loading="generatingAI">-->
<!--                    生成AI意见-->
<!--                  </el-button>-->
<!--                  <el-button plain type="success" @click="saveAIAnalysis"> 保存 </el-button>-->
<!--                </div>-->
<!--              </div>-->
<!--              <div class="bg-gray-50 pl-6 pt-3 pb-3 rounded-lg text-sm leading-6">-->
<!--                <div-->
<!--                  class="text-gray-700 hide-scrollbar"-->
<!--                  v-html="formattedAIAnalysis"-->
<!--                  v-if="aiAnalysis !== ''"-->
<!--                  style="max-height: 28vh; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none;"-->
<!--                >-->
<!--                </div>-->
<!--                <div class="text-gray-700" v-else>暂无AI分析结果</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
        </el-card>
      </div>
    </div>

    <!-- AI分析结果对话框 -->
    <el-dialog v-model="finishDialogVisible" title="AI 分析结果" width="70%">
      <div>
        <el-card class="custom-collapse-card reasoning-card" shadow="hover">
          <template #header>
            <div
              class="custom-collapse-header"
              @click="isReasoningCollapsed = isReasoningCollapsed.includes('1') ? [] : ['1']"
            >
              <div class="flex justify-between w-full pr-4">
                <div class="flex items-center">
                  <h3 class="m-0">分析过程</h3>
                  <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 mr-2">
                    {{ isReasoningCollapsed.includes('1') ? '点击展开' : '点击折叠' }}
                  </span>
                  <el-icon
                    class="transition-all"
                    :class="isReasoningCollapsed.includes('1') ? 'transform rotate-180' : ''"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
            </div>
          </template>
          <div
            class="custom-collapse-content"
            :class="{ hidden: isReasoningCollapsed.includes('1') }"
          >
            <div class="reasoning-container">
              <div
                class="text-wrap text-base whitespace-pre-wrap reasoning-content"
                ref="reasoningContainer"
              >
                {{ isAnalysisFinish ? aiReasoning : tempReasoning }}
              </div>
            </div>
          </div>
        </el-card>

        <el-divider />

        <el-card class="result-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="flex items-center">
                <h3>分析结果</h3>
                <el-tooltip content="AI分析的结论" placement="top" class="ml-2">
                  <el-icon class="ml-1">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="result-container">
            <div
              v-html="marked(isAnalysisFinish ? aiAnalysis : tempAnalysis)"
              class="text-wrap leading-7 text-base result-content"
              ref="resultContainer"
              style="overflow-y: auto; max-height: 100%;"
            ></div>
          </div>
        </el-card>
      </div>
      <template #footer>
        <el-button @click="finishDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.eval-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--el-border-color-lighter);

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 5px;

    .label {
      color: var(--el-text-color-secondary);
      margin-right: 8px;
      font-size: 14px;
    }

    .value {
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

:deep(.el-step__title) {
  font-size: 14px;
  line-height: 25px;
}

// 自定义步骤条样式
:deep(.custom-steps) {
  // 禁用整个步骤的点击事件
  .el-step {
    cursor: default;
  }

  // 连接线不可点击
  .el-step__line {
    pointer-events: none;
  }
}

// 可点击区域的样式
.step-icon-clickable,
.step-title-clickable,
.step-description-clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
    transform: scale(1.02);
  }
}

.step-icon-clickable {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;

  &:hover {
    background-color: rgba(64, 158, 255, 0.15);
    transform: scale(1.1);
  }
}

.step-default-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #c0c4cc;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.step-finished-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #67c23a;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

// 正在查看状态的样式
:deep(.viewing-step) {
  .el-step__head {
    border-color: #FF6B35 !important;
  }

  .el-step__title {
    color: #FF6B35 !important;
    font-weight: 600 !important;
  }

  .el-step__description {
    color: #FF6B35 !important;
    font-weight: 500 !important;
  }

  // 明确重置连接线颜色为默认值
  .el-step__line {
    background-color: #c0c4cc !important;
  }

  .el-step__line-inner {
    background-color: #c0c4cc !important;
  }

  // 正在查看状态的可点击区域特殊样式
  .step-icon-clickable,
  .step-title-clickable,
  .step-description-clickable {
    &:hover {
      background-color: rgba(255, 107, 53, 0.1);
    }
  }

  .step-icon-clickable {
    &:hover {
      background-color: rgba(255, 107, 53, 0.15);
    }
  }
}

// 已完成状态的样式
:deep(.el-step.is-finish) {
  .el-step__title {
    color: #67c23a !important;
    font-weight: 600 !important;
  }

  .el-step__description {
    color: #67c23a !important;
    font-weight: 500 !important;
  }

  // 连接线也设置为绿色
  .el-step__line {
    background-color: #67c23a !important;
  }

  .el-step__line-inner {
    background-color: #67c23a !important;
  }
}



.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.reasoning-card,
.result-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .reasoning-container,
  .result-container {
    height: 350px;
    overflow-y: auto;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
  }

  .reasoning-content,
  .result-content {
    animation: fadeIn 0.5s ease-in-out;
    line-height: 1.6;
  }
}

.result-card {
  .result-container {
    background-color: #f0f9eb;

    &:hover {
      background-color: #e6f7df;
    }
  }

  .result-content {
    padding: 0 12px;

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
      color: #303133;
    }

    :deep(p) {
      margin-bottom: 12px;
    }

    :deep(ul),
    :deep(ol) {
      padding-left: 24px;
      margin-bottom: 12px;
    }

    :deep(blockquote) {
      padding: 0 12px;
      color: #606266;
      border-left: 4px solid #dcdfe6;
      margin: 16px 0;
    }
  }
}

/* 左侧表单容器宽度控制 */
.form-container {
  max-width: 75%; /* 限制左侧容器最大宽度 */

  .evaluation-form {
    max-width: 100%;
    word-wrap: break-word;
    word-break: break-all;

    /* 确保表单内容不会超出容器宽度 */
    :deep(.el-form) {
      max-width: 100%;
    }

    :deep(.el-form-item) {
      max-width: 100%;

      .el-form-item__label {
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        line-height: 1.4;
      }

      .el-form-item__content {
        max-width: 100%;

        .el-input,
        .el-textarea,
        .el-select,
        .el-radio-group,
        .el-checkbox-group {
          max-width: 100%;
        }

        .el-input__inner,
        .el-textarea__inner {
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }
  }
}

/* 左右卡片高度一致的样式 */
.left-card {
  height: 83vh;
  display: flex;
  flex-direction: column;
}

/* 右侧卡片高度调整，考虑到左侧有两个卡片 */
.right-card {
  height: calc(83vh + 23px); /* 调整高度以匹配左侧两个卡片的总高度 */
  display: flex;
  flex-direction: column;
  margin-top: 0px; /* 向上微调，使顶部对齐 */
  min-width: 25%; /* 确保右侧容器最小宽度 */
  max-width: 25%; /* 限制右侧容器最大宽度 */
}

.left-card :deep(.el-card__body),
.right-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

/* 右侧卡片内容区域不滚动，固定显示 */
.right-card .eval-info,
.right-card .flex.flex-col,
.right-card .mt-1.space-y-4,
.right-card .mt-1 {
  overflow-y: visible;
}

/* 只有评估师分析和AI分析区域保留滚动，但隐藏滚动条 */
.right-card .mt-4 {
  overflow-y: auto;

  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* 确保AI分析内容区域在卡片内滚动，但隐藏滚动条 */
.right-card .bg-gray-50 {
  max-height: 28vh;
  overflow-y: auto;

  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.7;
  }

  to {
    opacity: 1;
  }
}

.custom-collapse-card {
  .custom-collapse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
    padding: 8px 0px;
    border-radius: 4px;
  }

  .custom-collapse-content {
    transition: all 0.3s ease-in-out;
    overflow: hidden;

    &.hidden {
      max-height: 0;
      padding: 0;
      opacity: 0;
    }
  }
}

/* 隐藏滚动条但保留滚动功能的通用类 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}
</style>
