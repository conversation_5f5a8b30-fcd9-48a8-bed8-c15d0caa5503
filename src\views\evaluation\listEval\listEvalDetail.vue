<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { ArrowLef<PERSON>, ArrowRight, Download } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { marked } from 'marked'

defineOptions({ name: 'ListEvalDetail' })

import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import { TemplateApi } from '@/api/evaluation/template'
import { ResultApi } from '@/api/evaluation/result'
import { useMessage } from '@/hooks/web/useMessage'
import AiEvalDetail from '@/views/evaluation/aiEval/aiEvalDetail.vue'
import download from '@/utils/download'

const route = useRoute()
const message = useMessage()

const id = route.query.id

// 评估任务清单执行数据
const listExecutionInfo = ref<ListExecutionVO | null>(null)

// 是否显示综合分析
const showSummaryAnalysis = ref(false)

// 定义模板类型接口
interface TemplateItem {
  id: number | string
  name: string
}

// 模板列表
const templateList = ref<TemplateItem[]>([])

// 存储已完成的模板ID和结果ID数组
const templateIdsArray = ref<string[]>([])
const resultIdsArray = ref<string[]>([])

// 当前选中的结果ID
const currentResultId = ref('')

// 获取步骤条的当前激活步骤
const activeStep = ref(0)

// 添加加载状态
const loading = ref(true)
const exportLoading = ref(false)

// 模板预览相关
const currentTemplateId = ref('')
const templateDetail = ref(null)
const templateRule = ref([])
const templateOption = ref({
  form: {
    labelPosition: 'top',
    size: 'default',
    labelWidth: '100px'
  },
  submitBtn: false,
  resetBtn: false
})
const previewForm = ref({})
const previewApi = ref(null)

// 重新排序结果ID，使其与清单构建时的模板顺序一致
const reorderResultIdsByTemplateOrder = async (templateIds: string[], resultIds: string[]) => {
  try {
    // 创建一个映射，存储每个模板ID对应的结果ID
    const templateToResultMap = new Map<string, string>()

    // 通过API查询每个结果ID对应的模板ID
    for (const resultId of resultIds) {
      if (resultId && resultId.trim()) {
        try {
          const resultDetail = await ResultApi.getResult(Number(resultId))
          if (resultDetail && resultDetail.templateId) {
            templateToResultMap.set(String(resultDetail.templateId), resultId)
            console.log(`结果ID ${resultId} 对应模板ID ${resultDetail.templateId}`)
          }
        } catch (error) {
          console.error(`查询结果ID ${resultId} 失败:`, error)
        }
      }
    }

    // 按照清单构建时的模板顺序重新排列结果ID
    const reorderedResultIds: string[] = []
    for (const templateId of templateIds) {
      const resultId = templateToResultMap.get(templateId)
      if (resultId) {
        reorderedResultIds.push(resultId)
        console.log(`模板ID ${templateId} 对应结果ID ${resultId}`)
      } else {
        console.log(`模板ID ${templateId} 没有对应的评估结果`)
      }
    }

    return reorderedResultIds
  } catch (error) {
    console.error('重新排序结果ID失败:', error)
    // 如果排序失败，返回原始顺序
    return resultIds
  }
}

// 获取列表执行详情
const getListExecutionDetail = async () => {
  try {
    if (!id) {
      message.error('未提供评估任务清单执行ID')
      return
    }

    loading.value = true

    // 获取模板列表
    const templates = await TemplateApi.getSimpleTemplateList()
    templateList.value = templates
    // console.log('模板列表数据:', templateList.value)

    // 获取评估任务清单执行详情
    const data = await ListExecutionApi.getListExecution(Number(id))
    listExecutionInfo.value = data

    // 解析模板ID和结果ID
    if (data.requiredTemplateIds) {
      templateIdsArray.value = data.requiredTemplateIds.split(',')
      console.log('清单构建时的模板ID顺序:', templateIdsArray.value)
    }

    if (data.resultIds) {
      const originalResultIds = data.resultIds.split(',')
      console.log('数据库中的结果ID顺序:', originalResultIds)

      // 重新排序结果ID，使其与清单构建时的模板顺序一致
      resultIdsArray.value = await reorderResultIdsByTemplateOrder(templateIdsArray.value, originalResultIds)
      console.log('重新排序后的结果ID顺序:', resultIdsArray.value)

      if (resultIdsArray.value.length > 0) {
        // 默认选中第一个结果
        currentResultId.value = resultIdsArray.value[0]
        activeStep.value = 0
      }
    }
  } catch (error) {
    // console.error('获取评估任务清单执行详情失败:', error)
    message.error('获取评估任务清单执行详情失败')
  } finally {
    loading.value = false
  }
}

// 获取模板名称
const getTemplateName = (templateId: string) => {
  // 添加更多的调试信息
  // console.log(`查找模板 ID: ${templateId}，类型: ${typeof templateId}`)
  // console.log('所有模板:', templateList.value)

  // 将两边都转为字符串进行比较
  const template = templateList.value.find((t) => String(t.id) === String(templateId))

  if (template) {
    // console.log(`找到模板: ${template.name}`)
    return template.name
  } else {
    // console.log(`未找到模板 ${templateId}`)
    ElMessage.error(`未找到模板 ${templateId}`)
    return `模板 ${templateId}`
  }
}

// 获取步骤状态
const getStepStatus = (index: number) => {
  if (index === activeStep.value) {
    return 'process' // 当前进行中的步骤
  } else if (index < resultIdsArray.value.length) {
    return 'finish' // 已完成的步骤
  } else {
    return 'wait' // 等待中的步骤
  }
}

// 计算步骤条的 active 值，确保连接线正确显示
const computedActiveStep = computed(() => {
  // Element Plus 的步骤条 active 属性表示"已完成的步骤数"
  // 为了让连接线正确显示到当前步骤，我们需要返回 activeStep + 1
  return activeStep.value + 1
})

// 显示综合分析
const showSummaryView = () => {
  showSummaryAnalysis.value = true
  currentResultId.value = ''
  currentTemplateId.value = ''
}

// 从综合分析返回到模板列表
const handleReturnToTemplates = () => {
  showSummaryAnalysis.value = false
  // 如果有当前选中的步骤，则显示该步骤
  if (activeStep.value < templateIdsArray.value.length) {
    handleStepChange(activeStep.value)
  }
}

// 格式化AI分析内容
const formatAiAnalysis = (aiAnalysis: string) => {
  if (!aiAnalysis) return ''

  // 移除[think]...[/think]部分
  let cleanedAnalysis = aiAnalysis.replace(/\[think\][\s\S]*?\[\/think\]/g, '')

  // 转换Markdown为HTML
  try {
    return marked(cleanedAnalysis)
  } catch (error) {
    console.error('Markdown转换失败:', error)
    return cleanedAnalysis
  }
}

// 切换步骤
const handleStepChange = async (index: number) => {
  // 隐藏综合分析视图
  showSummaryAnalysis.value = false

  // 允许查看所有模板，不仅仅是已完成的
  if (index < templateIdsArray.value.length) {
    // 立即更新activeStep，确保UI同步
    activeStep.value = index

    // 使用nextTick确保DOM更新后再处理其他逻辑
    await nextTick()

    // 如果有对应的评估结果，显示评估结果详情
    if (index < resultIdsArray.value.length) {
      currentResultId.value = resultIdsArray.value[index]
      currentTemplateId.value = ''
    } else {
      // 如果没有评估结果，清空当前结果ID，加载模板预览
      currentResultId.value = ''
      currentTemplateId.value = templateIdsArray.value[index]
      await loadTemplatePreview(currentTemplateId.value)
    }

    // 滚动到当前步骤
    nextTick(() => {
      const stepElement = document.querySelector(`.el-step:nth-child(${index + 1})`)
      if (stepElement && stepElement.parentElement) {
        stepElement.parentElement.scrollTop = (stepElement as HTMLElement).offsetTop - 120
      }
    })
  }
}

// 加载模板预览
const loadTemplatePreview = async (templateId: string) => {
  try {
    const data = await TemplateApi.getTemplate(Number(templateId))
    templateDetail.value = data

    // 解析模板数据
    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        templateRule.value = schema.rule || []
        // 可以选择是否使用保存的 option
        // templateOption.value = { ...templateOption.value, ...schema.option }
      } catch (error) {
        console.error('解析模板数据失败:', error)
        ElMessage.error('模板数据格式错误')
        templateRule.value = []
      }
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    ElMessage.error('获取模板详情失败')
  }
}

// 上一个评估结果
const handlePrev = async () => {
  if (activeStep.value > 0) {
    await handleStepChange(activeStep.value - 1)
  }
}

// 下一个评估结果
const handleNext = async () => {
  if (activeStep.value < templateIdsArray.value.length - 1) {
    await handleStepChange(activeStep.value + 1)
  }
}

// 导出评估报告
const exportReport = async () => {
  if (!id) {
    message.error('评估任务清单执行ID不能为空')
    return
  }

  exportLoading.value = true
  try {
    // 导出的二次确认
    await message.exportConfirm('是否确认导出评估报告？')

    // 构建文件名
    const fileName = `${listExecutionInfo.value?.elderName || '老人'}_${
      listExecutionInfo.value?.listName || '评估清单'
    }_评估报告.docx`

    // 显示全屏加载
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在生成评估报告，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 调用导出API
      const data = await ResultApi.exportListExecutionWord({
        id: Number(id)
      })

      // 下载文件
      download.word(data, fileName)

      // 提示成功
      ElMessage.success('评估报告导出成功')
    } finally {
      // 关闭加载
      loadingInstance.close()
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loading.value = true // 开始加载，设置加载状态为true
  getListExecutionDetail()
})
</script>

<template>
  <div class="flex flex-col h-full" v-loading="loading" element-loading-text="数据加载中..." >
    <div class="flex flex-row h-full" v-if="!loading">
      <!-- 左侧步骤条 -->
      <div class="w-40 mr-2 left-steps-container">
        <el-card class="h-full left-nav-card left-card">
          <template #header>
            <div class="card-header">
              <span class="font-bold">评估任务清单</span>
              <span class="text-gray-500 text-sm">{{ listExecutionInfo?.listName }}</span>
            </div>
          </template>

          <div class="steps-container">
            <el-steps direction="vertical" :active="computedActiveStep" :finish-status="'success'">
              <el-step
                v-for="(templateId, index) in templateIdsArray"
                :key="templateId"
                :title="getTemplateName(templateId)"
                :status="getStepStatus(index)"
                :class="{ 'current-step': index === activeStep }"
              >
                <template #icon>
                  <div
                    class="step-icon cursor-pointer"
                    @click="handleStepChange(index)"
                    :class="{
                      'step-active': index === activeStep,
                      'step-completed': index < resultIdsArray.length,
                      'step-pending': index >= resultIdsArray.length
                    }"
                  >
                    {{ index + 1 }}
                  </div>
                </template>
              </el-step>
            </el-steps>
          </div>

          <!-- 添加上一个、下一个按钮 -->
          <div class="mt-4 flex justify-between navigation-buttons">
            <el-button
              type="primary"
              :disabled="activeStep <= 0"
              @click="handlePrev"
              plain
              class="prev-btn"
            >
              <el-icon class="mr-1"><ArrowLeft /></el-icon
            ></el-button>
            <el-button
              type="primary"
              :disabled="activeStep >= templateIdsArray.length - 1 || templateIdsArray.length === 0"
              @click="handleNext"
              plain
              class="next-btn"
              ><el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>

          <!-- 添加清单综合分析按钮 -->
          <div class="mt-4 flex justify-center">
            <el-button
              type="primary"
              @click="showSummaryView"
              class="summary-btn w-full"
              :class="{ 'is-active': showSummaryAnalysis }"
            >
              查看清单综合分析
            </el-button>
          </div>

          <!-- 添加导出按钮 -->
          <div class="mt-4 flex justify-center">
            <el-button
              type="success"
              @click="exportReport"
              :loading="exportLoading"
              :disabled="resultIdsArray.length === 0"
              class="export-btn w-full"
            >
              <el-icon class="mr-1"><Download /></el-icon>导出评估报告
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧评估详情 -->
      <div class="flex-1 right-content-container">
        <transition name="fade" mode="out-in">
          <!-- 显示评估结果详情 -->
          <div class="right-card ai-eval-wrapper" v-if="currentResultId" style="height: 83vh;">
            <AiEvalDetail :resultId="currentResultId" :isInListDetail="true" :key="currentResultId" style="height: 100%;" />
          </div>

          <!-- 显示清单综合分析 -->
          <div v-else-if="showSummaryAnalysis && listExecutionInfo" class="summary-analysis-container right-card">
            <el-card class="h-full">
              <template #header>
                <div class="flex justify-between items-center">
                  <h3 class="m-0">清单综合分析</h3>
                  <el-button type="primary" text @click="handleReturnToTemplates">
                    <el-icon class="mr-1"><ArrowLeft /></el-icon>返回模板列表
                  </el-button>
                </div>
              </template>

              <div class="summary-content">
                <!-- 评估总体结果 -->
                <div class="mb-6" v-if="listExecutionInfo.totalScore !== undefined || listExecutionInfo.assessmentResult">
                  <h4 class="text-lg font-medium mb-3">评估总体结果</h4>
                  <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="grid grid-cols-2 gap-4">
                      <div v-if="listExecutionInfo.totalScore !== undefined">
                        <span class="text-gray-600">总体评分：</span>
                        <span class="text-xl font-bold text-blue-600">{{ listExecutionInfo.totalScore }}</span>
                      </div>
                      <div v-if="listExecutionInfo.assessmentResult">
                        <span class="text-gray-600">评估等级：</span>
                        <el-tag type="success" size="large">{{ listExecutionInfo.assessmentResult }}</el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 评估师分析 -->
                <div class="mb-4">
                  <h4 class="text-lg font-medium mb-2">评估师综合分析</h4>
                  <div class="analysis-text">
                    <div v-if="listExecutionInfo.evaluatorAnalysis" class="whitespace-pre-wrap">
                      {{ listExecutionInfo.evaluatorAnalysis }}
                    </div>
                    <el-empty v-else description="暂无评估师综合分析" />
                  </div>
                </div>

                <!-- AI分析 -->
                <div class="mt-6">
                  <h4 class="text-lg font-medium mb-2">AI综合分析</h4>
                  <div class="analysis-text">
                    <div
                      v-if="listExecutionInfo.aiAnalysis"
                      v-html="formatAiAnalysis(listExecutionInfo.aiAnalysis)"
                      class="markdown-content"
                    ></div>
                    <el-empty v-else description="暂无AI综合分析" />
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 显示模板预览 -->
          <div v-else-if="currentTemplateId && templateDetail" class="template-preview-container right-card">
            <el-card class="h-full">
              <template #header>
                <div class="template-header">
                  <h3>{{ templateDetail.name }}</h3>
                  <div class="template-meta">
                    <el-tag size="small" type="info" class="mr-2">模板预览</el-tag>
                    <el-tag size="small" type="warning">未完成评估</el-tag>
                  </div>
                </div>
              </template>
              <div class="template-content">
                <form-create
                  v-if="templateRule.length > 0"
                  :value="previewForm"
                  @input="(val) => previewForm = val"
                  @mounted="(api) => previewApi = api"
                  :rule="templateRule"
                  :option="templateOption"
                />
                <div v-else class="no-schema">
                  <el-empty description="暂无表单结构或表单结构解析失败" />
                </div>
              </div>
            </el-card>
          </div>
          <!-- 默认空状态 -->
          <div v-else class="flex items-center justify-center h-full">
            <el-empty description="请选择一个评估项目查看详情" />
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 25px;
  border-radius: 50%;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  left: 2px;
  top: 2px;
  z-index: 2;

  &.step-active {
    background-color: var(--el-color-primary);
    color: white;
    font-weight: bold;
  }

  &.step-completed {
    background-color: var(--el-color-success);
    color: white;
  }

  &.step-pending {
    background-color: var(--el-color-warning-light-5);
    color: var(--el-color-warning);
  }

  &:hover:not(.step-disabled) {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: scale(0.6);
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}

/* 左侧导航卡片特定样式 */
.left-nav-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  font-size: 14px;

  :deep(.el-card__header) {
    padding: 14px 16px;
    // border-bottom: 1px solid var(--el-border-color-light);
    // background-color: var(--el-fill-color-light);
    border-radius: 6px 6px 0 0;
  }

  :deep(.el-card__body) {
    // padding: 16px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 70px);
    overflow-y: auto;
  }

  :deep(.el-steps) {
    flex: 1;
  }

  :deep(.el-step) {
    margin-bottom: 8px;

    &.is-process {
      .el-step__title {
        font-weight: bold;
        color: var(--el-color-primary);
      }

      .el-step__description {
        color: var(--el-color-primary-light-3);
      }

      &::before {
        content: '';
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: var(--el-color-primary);
        border-radius: 0 3px 3px 0;
      }

      // 添加背景色
      .el-step__main {
        background-color: rgba(64, 158, 255, 0.08);
        padding: 8px 12px;
        border-radius: 4px;
        margin-left: -8px;
        transition: all 0.3s;
      }
    }

    &.is-finish {
      .el-step__title {
        color: var(--el-color-success);
      }
    }

    &.is-wait {
      .el-step__title {
        color: var(--el-text-color-regular);
      }
    }
  }

  :deep(.el-step__title) {
    font-size: 14px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    padding-right: 10px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  :deep(.el-step__description) {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 2px;
    transition: all 0.3s ease;
  }

  :deep(.el-step__line) {
    background-color: #e4e7ed;

    &.is-finish {
      background-color: var(--el-color-success);
    }
  }

  :deep(.el-step.is-vertical .el-step__line) {
    left: 13px;
    top: 16px;
    height: calc(100% - 8px);
  }

  // 确保已完成步骤的连接线显示为绿色
  :deep(.el-step.is-finish .el-step__line) {
    background-color: var(--el-color-success);
  }

  // 当前步骤的连接线显示为蓝色
  :deep(.el-step.is-process .el-step__line) {
    background-color: var(--el-color-primary);
  }

  // 强制显示第一个步骤的连接线
  :deep(.el-step:first-child .el-step__line) {
    display: block !important;
  }

  // 当第一个步骤是当前步骤时，连接线为蓝色
  :deep(.el-step:first-child.is-process .el-step__line) {
    background-color: var(--el-color-primary);
  }

  // 当第一个步骤已完成时，连接线为绿色
  :deep(.el-step:first-child.is-finish .el-step__line) {
    background-color: var(--el-color-success);
  }
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.steps-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 16px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #909399;
  }
}

.navigation-buttons {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed var(--el-border-color-light);

  .prev-btn,
  .next-btn {
    // min-width: 90px;
    transition: all 0.2s ease;

    &:not([disabled]):hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .export-btn {
    transition: all 0.3s ease;

    &:not([disabled]):hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &:not([disabled]):active {
      transform: translateY(0);
    }
  }
}

.current-step {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--el-color-primary);
    border-radius: 0 2px 2px 0;
    z-index: 5;
  }

  // 放大图标
  .step-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}

/* 内容区域的过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 模板预览样式 */
.template-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .template-meta {
      display: flex;
      gap: 8px;
    }
  }

  .template-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    max-width: 100%;
    word-wrap: break-word;
    word-break: break-all;

    /* 确保表单内容不会超出容器宽度 */
    :deep(.el-form) {
      max-width: 100%;
    }

    :deep(.el-form-item) {
      max-width: 100%;

      .el-form-item__label {
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        line-height: 1.4;
      }

      .el-form-item__content {
        max-width: 100%;

        .el-input,
        .el-textarea,
        .el-select,
        .el-radio-group,
        .el-checkbox-group {
          max-width: 100%;
        }

        .el-input__inner,
        .el-textarea__inner {
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }

    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  .no-schema {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}

/* 左侧步骤条容器宽度控制 */
.left-steps-container {
  min-width: 160px; /* 确保左侧步骤条最小宽度 */
  max-width: 25%; /* 限制左侧步骤条最大宽度 */
  flex-shrink: 0; /* 防止被挤压 */
}

/* 右侧内容容器宽度控制 */
.right-content-container {
  /* 移除最大宽度限制，让右侧容器正常占用剩余空间 */
  min-width: 0; /* 允许收缩 */
  flex: 1; /* 占用剩余空间 */
}

/* AiEvalDetail组件包装器样式 */
.ai-eval-wrapper {
  width: 100%;
  overflow: hidden;

  /* 确保AiEvalDetail内部的左侧表单区域不会过度扩展 */
  :deep(.form-container) {
    max-width: 75% !important; /* 限制左侧表单区域最大宽度 */
  }

  /* 确保AiEvalDetail内部的右侧信息区域可见 */
  :deep(.info-container) {
    min-width: 25% !important; /* 确保右侧信息区域最小宽度 */
    max-width: 25% !important; /* 限制右侧信息区域最大宽度 */
    flex-shrink: 0 !important; /* 防止被挤压 */
  }

  :deep(.form-content-container) {
    max-width: 100% !important;
    word-wrap: break-word;
    word-break: break-all;
  }

  :deep(.el-form) {
    max-width: 100% !important;
  }

  :deep(.el-form-item) {
    max-width: 100% !important;

    .el-form-item__label {
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      line-height: 1.4;
    }

    .el-form-item__content {
      max-width: 100% !important;

      .el-input,
      .el-textarea,
      .el-select,
      .el-radio-group,
      .el-checkbox-group {
        max-width: 100% !important;
      }

      .el-input__inner,
      .el-textarea__inner {
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }
}

/* 左右卡片高度一致的样式 */
.left-card,
.right-card {
  height: 83vh;
  display: flex;
  flex-direction: column;
}

.left-card :deep(.el-card__body),
.right-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

/* 综合分析样式 */
.summary-analysis-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-card__body) {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .summary-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
    width: 100%;

    /* 美化滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
  }

  .analysis-text {
    min-height: 100px;
    max-height: 300px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f5f7fa;
    word-break: break-word;
    overflow-wrap: break-word;
    overflow-y: auto;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;

    /* 美化滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #909399;
    }
  }

  .markdown-content {
    word-break: break-word;
    overflow-wrap: break-word;
    word-wrap: break-word;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    /* 移除内部滚动，让外层容器处理滚动 */
    overflow: visible;
    /* 强制不允许水平溢出 */
    overflow-x: hidden;

    /* 强制所有子元素不超出容器宽度 */
    * {
      max-width: 100% !important;
      word-break: break-word !important;
      overflow-wrap: break-word !important;
      box-sizing: border-box !important;
      /* 强制不允许水平溢出 */
      overflow-x: hidden !important;
    }

    h1, h2, h3, h4, h5, h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: 600;
      max-width: 100%;
      word-break: break-word;
    }

    p {
      margin-bottom: 1em;
      line-height: 1.6;
      max-width: 100%;
      word-break: break-word;
      overflow-wrap: break-word;
    }

    ul, ol {
      padding-left: 2em;
      margin-bottom: 1em;
      max-width: 100%;
      box-sizing: border-box;
    }

    li {
      margin-bottom: 0.5em;
      word-break: break-word;
      overflow-wrap: break-word;
    }

    blockquote {
      border-left: 4px solid #dfe2e5;
      padding-left: 1em;
      color: #6a737d;
      margin-bottom: 1em;
      max-width: 100%;
      box-sizing: border-box;
      word-break: break-word;
    }

    code {
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
      padding: 0.2em 0.4em;
      font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
      white-space: pre-wrap;
      word-break: break-word;
      overflow-wrap: break-word;
    }

    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: hidden; /* 完全隐藏溢出 */
      margin-bottom: 1em;
      max-width: 100%;
      width: 100%;
      white-space: pre-wrap;
      word-break: break-word;
      word-wrap: break-word;
      box-sizing: border-box;

      /* 强制代码块内容换行 */
      code {
        white-space: pre-wrap !important;
        word-break: break-word !important;
        word-wrap: break-word !important;
      }
    }

    /* 处理表格溢出 */
    table {
      max-width: 100%;
      table-layout: fixed;
      word-break: break-word;
    }

    td, th {
      word-break: break-word;
      overflow-wrap: break-word;
    }

    /* 处理长链接 */
    a {
      word-break: break-all;
      overflow-wrap: break-word;
    }

    /* 处理其他可能导致溢出的元素 */
    img {
      max-width: 100% !important;
      height: auto !important;
    }

    div, span, strong, em, b, i {
      word-break: break-word !important;
      overflow-wrap: break-word !important;
      max-width: 100% !important;
    }

    /* 处理可能的内联样式宽度 */
    [style*="width"] {
      max-width: 100% !important;
    }
  }
}

/* 按钮高亮样式 */
.summary-btn.is-active {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-5);
  color: white;
  font-weight: bold;
}
</style>
