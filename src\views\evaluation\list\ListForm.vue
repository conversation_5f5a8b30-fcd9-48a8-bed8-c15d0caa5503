<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px" :close-on-click-modal="false">
    <div style="max-height: 70vh; overflow-y: auto;">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="formLoading"
      >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
        <!-- <Editor v-model="formData.description" height="300px" /> -->
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择类型"
          style="width: 100%"
          filterable
          allow-create
          default-first-option
          @change="handleTypeChange"
        >
          <el-option
            v-for="dict in typeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <div class="text-xs text-gray-400 mt-1">可选择已有类型或输入新类型名称</div>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评估模板" prop="selectedTemplates">
        <el-select
          v-model="selectedTemplates"
          multiple
          filterable
          placeholder="请选择评估模板"
          style="width: 100%"
          @change="handleTemplateChange"
        >
          <el-option
            v-for="item in templateOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <div class="text-xs text-gray-400 mt-1"> 已选择 {{ selectedTemplates.length }} 个模板 </div>
      </el-form-item>

      <!-- 清单评分规则设置 -->
      <el-form-item label="评分规则">
        <div class="w-full">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm text-gray-600">设置清单总分的评估等级</span>
            <el-button type="primary" size="small" @click="addRule">添加规则</el-button>
          </div>

          <div v-if="listRules.length === 0" class="text-gray-400 text-sm py-4 text-center border border-dashed rounded">
            暂无评分规则，点击"添加规则"开始设置
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="(rule, index) in listRules"
              :key="index"
              class="flex items-center gap-2 p-3 border rounded"
            >
              <div class="flex items-center gap-2 flex-1">
                <span class="text-sm">分数范围:</span>
                <el-input-number
                  v-model="rule.min"
                  :min="0"
                  :max="9999"
                  size="small"
                  placeholder="最低分"
                  style="width: 100px"
                />
                <span class="text-sm">-</span>
                <el-input-number
                  v-model="rule.max"
                  :min="0"
                  :max="9999"
                  size="small"
                  placeholder="最高分"
                  style="width: 100px"
                />
                <span class="text-sm">评估结果:</span>
                <el-input
                  v-model="rule.result"
                  size="small"
                  placeholder="请输入评估结果"
                  style="width: 150px"
                />
              </div>
              <el-button
                type="danger"
                size="small"
                @click="removeRule(index)"
                :icon="Delete"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    </div>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { nextTick } from 'vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ListApi, ListVO } from '@/api/evaluation/list'
import { TemplateApi } from '@/api/evaluation/template'
import { Delete } from '@element-plus/icons-vue'
import * as DictDataApi from '@/api/system/dict/dict.data'
import { useDictStore } from '@/store/modules/dict'

defineOptions({ name: 'ListForm' })

// 国际化
const { t } = useI18n()
// 消息弹窗
const message = useMessage()
// 字典store
const dictStore = useDictStore()

// 弹窗的是否展示
const dialogVisible = ref(false)
// 弹窗的标题
const dialogTitle = ref('')
// 表单的加载
const formLoading = ref(false)
// 表单的类型
const formType = ref('')
// 表单的数据
const formData = ref({
  id: undefined,
  name: undefined,
  description: undefined,
  type: undefined,
  status: undefined,
  templateIds: '',
  listRule: ''
})

// 评分规则接口
interface ListRule {
  min: number
  max: number
  result: string
}

// 评分规则数组
const listRules = ref<ListRule[]>([])

// 类型选项数组
const typeOptions = ref<any[]>([])

// 获取类型选项
const getTypeOptions = async () => {
  try {
    const options = getIntDictOptions(DICT_TYPE.EVALUATE_LIST_TYPE)
    typeOptions.value = [...options] // 创建新数组确保响应式更新
  } catch (error) {
    console.error('获取类型选项失败:', error)
  }
}

// 处理类型变化，如果是新类型则创建字典数据
const handleTypeChange = async (value: any) => {
  if (!value) return

  // 如果是字符串，说明是用户输入的新类型
  if (typeof value === 'string') {
    try {
      // 获取当前字典类型的最大排序值和最大键值
      const existingOptions = getIntDictOptions(DICT_TYPE.EVALUATE_LIST_TYPE)
      const maxSort = existingOptions.reduce((max: number, option: any) => {
        return Math.max(max, option.sort || 0)
      }, 0)

      const maxValue = existingOptions.reduce((max: number, option: any) => {
        return Math.max(max, parseInt(option.value) || 0)
      }, 0)

      const newValue = maxValue + 1

      // 创建新的字典数据
      const newDictData: DictDataApi.DictDataVO = {
        id: undefined,
        dictType: 'evaluate_list_type',
        label: value,
        value: newValue.toString(),
        sort: maxSort + 1,
        status: 0, // 启用状态
        colorType: '',
        cssClass: '',
        remark: '用户创建的新类型',
        createTime: new Date()
      }

      await DictDataApi.createDictData(newDictData)
      message.success('新类型创建成功')

      // 刷新字典缓存
      await dictStore.resetDict()

      // 重新获取类型选项
      await getTypeOptions()

      // 更新表单数据为新创建的数字值
      formData.value.type = newValue

      // 强制触发响应式更新
      nextTick(() => {
        // 确保选择框显示正确的标签
        formData.value.type = newValue
      })

    } catch (error) {
      console.error('创建新类型失败:', error)
      message.error('创建新类型失败，请重试')
      // 重置类型字段
      formData.value.type = undefined
    }
  }
}


// 表单的规则
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
})
// 表单的 Ref
const formRef = ref()

// 添加选中的模板ID数组和模板选项列表
const selectedTemplates = ref<number[]>([])
// 定义模板选项的接口，与getSimpleTemplateList返回的结构匹配
interface TemplateOption {
  id: number
  name: string
}
// 模板选项列表
const templateOptions = ref<TemplateOption[]>([])

// 获取模板列表
const getTemplateOptions = async () => {
  try {
    // 使用模板API获取所有可用模板
    const res = await TemplateApi.getSimpleTemplateList()
    templateOptions.value = res || []
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  }
}

// 处理模板选择变化
const handleTemplateChange = () => {
  // 将选中的模板ID数组转换为逗号分隔的字符串
  if (selectedTemplates.value.length > 0) {
    formData.value.templateIds = selectedTemplates.value.join(',')
  } else {
    formData.value.templateIds = ''
  }
}



// 添加评分规则
const addRule = () => {
  listRules.value.push({
    min: 0,
    max: 100,
    result: ''
  })
}

// 删除评分规则
const removeRule = (index: number) => {
  listRules.value.splice(index, 1)
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取类型选项和模板选项
  await getTypeOptions()
  await getTemplateOptions()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ListApi.getList(id)
      formData.value = {
        ...data,
        // 确保类型字段是数字类型
        type: typeof data.type === 'string' ? parseInt(data.type) : data.type
      }

      // 如果有模板IDs，转换为数组并设置选中状态
      if (formData.value.templateIds && typeof formData.value.templateIds === 'string') {
        selectedTemplates.value = formData.value.templateIds
          .split(',')
          .filter((id: string) => id.trim() !== '')
          .map((id: string) => parseInt(id))
      }

      // 如果有评分规则，解析并设置
      if (formData.value.listRule && typeof formData.value.listRule === 'string') {
        try {
          const rules = JSON.parse(formData.value.listRule)
          if (Array.isArray(rules)) {
            listRules.value = rules
          }
        } catch (e) {
          console.error('解析评分规则失败:', e)
          listRules.value = []
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}

// 暴露 open 方法，用于打开弹窗
defineExpose({ open })

// 定义 success 事件
const emit = defineEmits(['success'])

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 确保模板ID已转换为逗号分隔的字符串
  formData.value.templateIds = selectedTemplates.value.join(',')

  // 确保评分规则已转换为JSON字符串
  formData.value.listRule = JSON.stringify(listRules.value)

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ListVO
    if (formType.value === 'create') {
      await ListApi.createList(data)
      message.success(t('common.createSuccess'))
    } else {
      await ListApi.updateList(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    description: undefined,
    type: undefined,
    status: undefined,
    templateIds: '',
    listRule: ''
  }
  selectedTemplates.value = [] // 重置选中的模板
  listRules.value = [] // 重置评分规则
  formRef.value?.resetFields()
}
</script>
