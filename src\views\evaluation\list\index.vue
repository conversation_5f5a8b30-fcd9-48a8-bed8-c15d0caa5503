<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATE_LIST_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_LIST_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['evaluation:list:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['evaluation:list:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column type="index" label="序号" width="60" align="center" /> -->
      <el-table-column label="清单名称" align="center" prop="name" min-width="300">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleView(scope.row.id)"
          >
            {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="描述" align="center" prop="description" />-->
      <el-table-column label="类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATE_LIST_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_LIST_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
<!--      <el-table-column label="关联的评估模板" align="center" prop="templateIds">-->
<!--        <template #default="scope">-->
<!--          {{ formatTemplateNames(scope.row.templateIds) }}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--        label="创建时间"-->
<!--        align="center"-->
<!--        prop="createTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="180px"-->
<!--      />-->
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleView(scope.row.id)"
            v-hasPermi="['evaluation:list:query']"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['evaluation:list:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:list:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ListForm ref="formRef" @success="getList" />

  <!-- 查看详情弹窗 -->
  <el-dialog v-model="detailDialogVisible" title="评估任务清单详情" width="80%" :close-on-click-modal="false">
    <div style="max-height: 70vh; overflow-y: auto;">
      <!-- 基础信息卡片 -->
      <el-card class="mb-4" header="基础信息">
        <el-descriptions :column="2" border label-align="right">
          <el-descriptions-item label="清单ID">{{ detailData.id }}</el-descriptions-item>
          <el-descriptions-item label="清单名称">{{ detailData.name }}</el-descriptions-item>
          <el-descriptions-item label="清单类型">
            <dict-tag :type="DICT_TYPE.EVALUATE_LIST_TYPE" :value="detailData.type" />
          </el-descriptions-item>
          <el-descriptions-item label="清单状态">
            <dict-tag :type="DICT_TYPE.EVALUATION_LIST_STATUS" :value="detailData.status" />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateOnly(detailData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateOnly(detailData.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ detailData.creator || '-' }}</el-descriptions-item>
          <el-descriptions-item label="更新者">{{ detailData.updater || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 描述信息卡片 -->
      <el-card class="mb-4" header="描述信息">
        <div class="text-gray-600" style="line-height: 1.6;">
          {{ detailData.description || '暂无描述' }}
        </div>
      </el-card>

      <!-- 关联模板卡片 -->
      <el-card class="mb-4" header="关联的评估模板">
        <div v-if="templateDetails.length > 0">
          <el-table :data="templateDetails" border style="width: 100%">
            <el-table-column prop="id" label="模板ID" width="80" align="center" />
            <el-table-column prop="name" label="模板名称" min-width="200" />
            <el-table-column prop="type" label="模板类型" width="120" align="center">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.EVALUATION_TEMPLATE_TYPE" :value="scope.row.type" />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="模板状态" width="100" align="center">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.EVALUATION_TEMPLATE_STATUS" :value="scope.row.status" />
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="text-gray-400 text-center py-4">
          暂无关联的评估模板
        </div>
      </el-card>

      <!-- 评分规则卡片 -->
      <el-card header="评分规则">
        <div v-if="parsedListRules.length > 0">
          <el-table :data="parsedListRules" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="min" label="最低分" width="100" align="center" />
            <el-table-column prop="max" label="最高分" width="100" align="center" />
            <el-table-column prop="result" label="评估结果" min-width="200" />
            <el-table-column label="分数范围" width="150" align="center">
              <template #default="scope">
                <el-tag type="info">{{ scope.row.min }} - {{ scope.row.max }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="text-gray-400 text-center py-4">
          暂无评分规则
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="detailDialogVisible = false">关 闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ListApi, ListVO } from '@/api/evaluation/list'
import { TemplateApi } from '@/api/evaluation/template'
import ListForm from './ListForm.vue'
import dayjs from 'dayjs'

defineOptions({ name: 'List' })

// 消息弹窗
const message = useMessage()
// 国际化
const { t } = useI18n()

// 列表的加载
const loading = ref(true)
// 列表的数据
const list = ref<ListVO[]>([])
// 列表的总页数
const total = ref(0)
// 搜索的参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  description: undefined,
  type: undefined,
  status: undefined,
  templateIds: undefined,
  createTime: []
})
// 搜索的表单
const queryFormRef = ref()
// 导出的加载
const exportLoading = ref(false)
// 模板ID到名称的映射
const templateMap = ref<Record<string, string>>({})

// 控制查看详情弹窗是否可见
const detailDialogVisible = ref(false)

// 存储详情数据
const detailData = ref<ListVO>({} as ListVO)

// 存储模板详细信息
const templateDetails = ref<any[]>([])

// 解析后的评分规则
const parsedListRules = ref<any[]>([])

/** 格式化日期为年月日格式 */
const formatDateOnly = (dateValue: any) => {
  if (!dateValue) return '-'

  // 如果是时间戳（数字）
  if (typeof dateValue === 'number') {
    return dayjs(dateValue).format('YYYY-MM-DD')
  }

  // 如果是日期字符串或Date对象
  return dayjs(dateValue).format('YYYY-MM-DD')
}

/** 查看按钮操作 */
const handleView = async (id: number) => {
  try {
    const data = await ListApi.getList(id) // 调用接口获取详情数据
    detailData.value = data

    // 获取关联模板的详细信息
    await getTemplateDetails(data.templateIds)

    // 解析评分规则
    parseListRules(data.listRule)

    detailDialogVisible.value = true
  } catch (error) {
    message.error('获取详情失败')
  }
}

/** 获取模板列表 */
const getTemplateOptions = async () => {
  try {
    const res = await TemplateApi.getSimpleTemplateList()
    if (res && Array.isArray(res)) {
      // 创建ID到名称的映射
      templateMap.value = res.reduce((acc, item) => {
        acc[item.id] = item.name
        return acc
      }, {})
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

/** 模板名称格式化方法 */
const formatTemplateNames = (templateIds: string) => {
  if (!templateIds) return '-'
  const ids = templateIds.split(',')
  return ids.map((id) => templateMap.value[id] || id).join(', ')
}

/** 获取模板详细信息 */
const getTemplateDetails = async (templateIds: string) => {
  templateDetails.value = []
  if (!templateIds) return

  try {
    const ids = templateIds.split(',').filter(id => id.trim() !== '')
    const templatePromises = ids.map(async (id) => {
      try {
        const template = await TemplateApi.getTemplate(parseInt(id))
        return template
      } catch (error) {
        console.error(`获取模板${id}详情失败:`, error)
        return null
      }
    })

    const results = await Promise.all(templatePromises)
    templateDetails.value = results.filter(template => template !== null)
  } catch (error) {
    console.error('获取模板详细信息失败:', error)
  }
}

/** 解析评分规则 */
const parseListRules = (listRule: string) => {
  parsedListRules.value = []
  if (!listRule) return

  try {
    const rules = JSON.parse(listRule)
    if (Array.isArray(rules)) {
      parsedListRules.value = rules
    }
  } catch (error) {
    console.error('解析评分规则失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取模板列表
    await getTemplateOptions()

    const data = await ListApi.getListPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ListApi.deleteList(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ListApi.exportList(queryParams)
    download.excel(data, '评估任务清单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1rem;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-center {
  text-align: center;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
</style>
