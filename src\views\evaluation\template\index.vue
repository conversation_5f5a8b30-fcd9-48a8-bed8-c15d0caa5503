<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="版本号" prop="version">-->
<!--        <el-input-->
<!--          v-model="queryParams.version"-->
<!--          placeholder="请输入版本号"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="模板JSON" prop="formSchema">
        <el-input v-model="queryParams.formSchema" placeholder="请输入模板JSON" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item> -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" class="mr-5px" /> 搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> 重置 </el-button>
        <el-button
          type="primary"
          plain
          @click="router.push('/evaluation/build')"
          v-hasPermi="['evaluation:template:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['evaluation:template:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!--<el-table-column label="模板ID" align="center" prop="id" />-->
      <el-table-column label="模板名称" align="center" prop="name" min-width="300">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handlePreview(scope.row)"
          >
            {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="版本号" align="center" prop="version" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_TEMPLATE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EVALUATION_TEMPLATE_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
<!--      <el-table-column label="有效期状态" align="center" width="300">-->
<!--        <template #default="scope">-->
<!--          <el-tag :type="isTemplateExpired(scope.row) ? 'danger' : 'success'" effect="light">-->
<!--            {{ getTemplateValidityTip(scope.row) }}-->
<!--          </el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="模板预览" align="center" width="85">-->
<!--        <template #default="scope">-->
<!--          <el-button link type="primary" @click="handlePreview(scope.row)">-->
<!--            <Icon icon="ep:view" class="mr-5px" />查看-->
<!--          </el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--        label="创建时间"-->
<!--        align="center"-->
<!--        prop="createTime"-->
<!--        :formatter="dateFormatter"-->
<!--        width="250"-->
<!--      />-->
      <el-table-column label="操作" align="center" min-width="200px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handlePreview(scope.row)"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['evaluation:template:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['evaluation:template:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 模板预览对话框 -->
  <el-dialog
    v-model="previewVisible"
    title="模板预览"
    width="60%"
    destroy-on-close
    :close-on-click-modal="false"
    @closed="handlePreviewClose"
  >
    <div class="preview-form" v-if="currentTemplate">
      <form-create
        v-if="previewVisible"
        v-model:value="previewForm"
        v-model:api="previewApi"
        :rule="templateRule"
        :option="templateOption"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TemplateApi, TemplateVO } from '@/api/evaluation/template'
import { TemplateItemApi } from '@/api/evaluation/templateitem'
import { useRouter, useRoute } from 'vue-router'
import formCreate from '@form-create/element-ui'

defineOptions({ name: 'Template' })

// 消息弹窗
const message = useMessage()
// 国际化
const { t } = useI18n()
// 路由
const router = useRouter()
// 当前路由
const route = useRoute()

// 列表的加载中
const loading = ref(true)
// 列表的数据
const list = ref<TemplateVO[]>([])
// 列表的总页数
const total = ref(0)
// 搜索的参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  version: undefined,
  status: undefined,
  formSchema: undefined,
  createTime: [],
  type: undefined
})
// 搜索的表单
const queryFormRef = ref()
// 导出的加载中
const exportLoading = ref(false)

// 预览相关的变量
const previewVisible = ref(false)
const previewForm = ref({})
const previewApi = ref(null)
const currentTemplate = ref<TemplateVO | null>(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: { show: false },
  resetBtn: { show: false }
})

// 处理预览
const handlePreview = (row: TemplateVO) => {
  currentTemplate.value = row
  previewVisible.value = true

  // 解析模板数据
  try {
    const schema = JSON.parse(row.formSchema)
    templateRule.value = schema.rule || []
    // 可以选择是否使用保存的 option
    // templateOption.value = { ...templateOption.value, ...schema.option }
  } catch (error) {
    // console.error('解析模板数据失败:', error)
    ElMessage.error('模板数据格式错误')
  }
}


// 处理预览对话框关闭
const handlePreviewClose = () => {
  // 清空预览相关的数据
  currentTemplate.value = null
  previewForm.value = {}
  previewApi.value = null
  templateRule.value = []
}

// 检查模板是否过期
const isTemplateExpired = (row: TemplateVO) => {
  try {
    if (!row.formSchema) return false

    // 解析表单模式
    const schema = typeof row.formSchema === 'string' ? JSON.parse(row.formSchema) : row.formSchema

    // 获取有效期信息
    const validityPeriod = schema.option?.form?.validityPeriod || 3 // 默认3
    const validityUnit = schema.option?.form?.validityUnit || 'month' // 默认月
    const validityStartTimeType = schema.option?.form?.validityStartTimeType || 'evaluationTime' // 默认为评估时间
    const validityStartTime = schema.option?.form?.validityStartTime || '' // 固定日期

    // 如果是评估时间类型，模板本身不过期
    if (validityStartTimeType === 'evaluationTime') {
      return false
    }

    // 计算剩余天数
    const today = new Date()
    const day = 24 * 60 * 60 * 1000 // 一天的毫秒数

    // 确定起始日期
    let startDate
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 如果是固定日期模式且有设置日期
      startDate = new Date(validityStartTime)
    } else {
      // 默认使用今天的日期
      return false
    }

    // 根据单位计算到期日期
    let expiryDate = new Date(startDate)

    switch (validityUnit) {
      case 'day':
        expiryDate = new Date(startDate.getTime() + validityPeriod * day)
        break
      case 'week':
        expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
        break
      case 'month':
      default:
        expiryDate.setMonth(startDate.getMonth() + validityPeriod)
        break
    }

    // 检查是否已过期
    return today > expiryDate
  } catch (error) {
    // console.error('计算模板是否过期失败:', error, row)
    return false
  }
}

// 获取模板有效期提示
const getTemplateValidityTip = (row: TemplateVO) => {
  try {
    if (!row.formSchema) return '未设置有效期'

    // 解析表单模式
    const schema = typeof row.formSchema === 'string' ? JSON.parse(row.formSchema) : row.formSchema

    // 获取有效期信息
    const validityPeriod = schema.option?.form?.validityPeriod || 3 // 默认3
    const validityUnit = schema.option?.form?.validityUnit || 'month' // 默认月
    const validityStartTimeType = schema.option?.form?.validityStartTimeType || 'evaluationTime' //
    const validityStartTime = schema.option?.form?.validityStartTime || '' // 固定日期

    // 生成有效期文本
    let validityText = `${validityPeriod}`
    switch (validityUnit) {
      case 'day':
        validityText += validityPeriod > 1 ? '天' : '天'
        break
      case 'week':
        validityText += validityPeriod > 1 ? '周' : '周'
        break
      case 'month':
      default:
        validityText += validityPeriod > 1 ? '个月' : '个月'
        break
    }

    // 根据起始时间类型返回不同的提示
    if (validityStartTimeType === 'fixedDate' && validityStartTime) {
      // 计算剩余天数
      const today = new Date()
      const day = 24 * 60 * 60 * 1000 // 一天的毫秒数
      const startDate = new Date(validityStartTime)

      // 计算到期日期
      let expiryDate = new Date(startDate)
      switch (validityUnit) {
        case 'day':
          expiryDate = new Date(startDate.getTime() + validityPeriod * day)
          break
        case 'week':
          expiryDate = new Date(startDate.getTime() + validityPeriod * 7 * day)
          break
        case 'month':
        default:
          expiryDate.setMonth(startDate.getMonth() + validityPeriod)
          break
      }

      // 计算剩余天数
      const daysLeft = Math.ceil((expiryDate.getTime() - today.getTime()) / day)

      if (daysLeft <= 0) {
        return `已过期 (有效期${validityText}，从 ${validityStartTime} 起)`
      } else {
        return `剩余${daysLeft}天 (有效期${validityText}，从 ${validityStartTime} 起)`
      }
    } else {
      return `有效期${validityText} (从评估时间起)`
    }
  } catch (error) {
    // console.error('计算有效期提示失败:', error, row)
    return '未设置有效期'
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TemplateApi.getTemplatePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = (type: string, id?: number) => {
  if (type === 'create') {
    // 跳转到表单设计器页面
    router.push('/evaluation/build')
  } else {
    // 编辑时带上 id 参数
    router.push(`/evaluation/build?id=${id}`)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()

    // 先删除模板项，再删除模板
    await Promise.all([
      // 删除模板项
      TemplateItemApi.deleteTemplateItemByTemplateId(id),
      // 删除模板
      TemplateApi.deleteTemplate(id)
    ])

    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TemplateApi.exportTemplate(queryParams)
    download.excel(data, '评估模板.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

// 监听路由变化，当从其他页面返回时刷新列表
watch(
  () => route.path,
  (newPath) => {
    if (newPath === '/evaluation/template') {
      getList()
    }
  }
)
</script>

<style lang="scss" scoped>
.preview-form {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;

  :deep(.el-form) {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style>
