<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入机构名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构简称" prop="shortName">
                <el-input v-model="formData.shortName" placeholder="请输入机构简称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="机构类型" prop="type">
                <el-select v-model="formData.type" placeholder="请选择机构类型" class="w-full">
                  <el-option
                    v-for="dict in getStrDictOptions('organization_type')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构等级" prop="level">
                <el-select v-model="formData.level" placeholder="请选择机构等级" class="w-full">
                  <el-option
                    v-for="dict in getStrDictOptions('organization_level')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="成立时间" prop="establishDate">
                <el-date-picker
                  v-model="formData.establishDate"
                  type="date"
                  value-format="x"
                  placeholder="选择成立时间"
                  class="w-full"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机构logo" prop="logo">
                <UploadImg v-model="formData.logo" :file-size="2" directory="institution/logo" />
                <template #tip>
                  <div style="color: #999">支持 jpg、png、gif 格式，大小不超过 2MB</div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="营业信息" name="business">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="营业执照号码" prop="businessLicense">
                <el-input v-model="formData.businessLicense" placeholder="请输入营业执照号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="营业执照图片" prop="businessLicenseImg">
                <UploadImg v-model="formData.businessLicenseImg" :file-size="5" directory="institution/license" />
                <template #tip>
                  <div style="color: #999">支持 jpg、png、gif 格式，大小不超过 5MB</div>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="法人代表" prop="legalPerson">
                <el-input v-model="formData.legalPerson" placeholder="请输入法人代表" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人联系方式" prop="legalPersonContact">
                <el-input v-model="formData.legalPersonContact" placeholder="请输入法人联系方式" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="床位数量" prop="bedCount">
                <el-input-number v-model="formData.bedCount" :min="0" placeholder="请输入床位数量" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="占地面积(㎡)" prop="areaSize">
                <el-input-number v-model="formData.areaSize" :min="0" :precision="2" placeholder="请输入占地面积" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="联系方式" name="contact">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急联系电话" prop="emergencyPhone">
                <el-input v-model="formData.emergencyPhone" placeholder="请输入紧急联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="formData.email" placeholder="请输入电子邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="官方网站" prop="website">
                <el-input v-model="formData.website" placeholder="请输入官方网站" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入详细地址" />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input-number v-model="formData.longitude" :precision="6" placeholder="请输入经度" class="w-full" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input-number v-model="formData.latitude" :precision="6" placeholder="请输入纬度" class="w-full" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="机构介绍" name="introduction">
          <el-form-item label="机构简介" prop="introduction">
            <el-input v-model="formData.introduction" type="textarea" :rows="3" placeholder="请输入机构简介" />
          </el-form-item>
          <el-form-item label="机构特色" prop="features">
            <el-input v-model="formData.features" type="textarea" :rows="3" placeholder="请输入机构特色" />
          </el-form-item>
          <el-form-item label="机构文化" prop="culture">
            <el-input v-model="formData.culture" type="textarea" :rows="3" placeholder="请输入机构文化" />
          </el-form-item>
          <el-form-item label="机构荣誉" prop="honors">
            <el-input v-model="formData.honors" type="textarea" :rows="3" placeholder="请输入机构荣誉" />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { InfoApi, InfoVO } from '@/api/institution/info'
import { getStrDictOptions } from '@/utils/dict'
import UploadImg from '@/components/UploadFile/src/UploadImg.vue'
import './index.css'

// 增强的日期处理函数（支持多种输入格式）
const convertToTimestamp = (dateValue: any): number | undefined => {
  console.log('🔄 convertToTimestamp 开始转换:', dateValue, '类型:', typeof dateValue)

  if (!dateValue) {
    console.log('❌ convertToTimestamp: 输入为空')
    return undefined
  }

  try {
    let date: Date

    if (typeof dateValue === 'string') {
      // 字符串格式处理
      const dateStr = dateValue.trim()
      console.log('🔍 处理字符串:', `"${dateStr}"`)

      // 验证YYYY-MM-DD格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        date = new Date(dateStr + 'T00:00:00')
        console.log('✅ LocalDate字符串格式处理')
      } else {
        // 尝试其他字符串格式
        date = new Date(dateStr)
        console.log('🔄 尝试通用字符串格式处理')
      }
    } else if (typeof dateValue === 'number') {
      // 数字格式（可能是时间戳）
      date = new Date(dateValue)
      console.log('🔄 数字格式处理（时间戳）')
    } else if (dateValue instanceof Date) {
      // Date对象
      date = dateValue
      console.log('🔄 Date对象处理')
    } else if (typeof dateValue === 'object' && dateValue !== null) {
      // 对象格式处理（可能是序列化的Date对象）
      console.log('🔍 对象格式详细信息:', dateValue)

      // 尝试获取对象的字符串表示
      if (dateValue.toString && typeof dateValue.toString === 'function') {
        const dateStr = dateValue.toString()
        console.log('🔄 对象toString结果:', dateStr)
        date = new Date(dateStr)
      } else if (dateValue.valueOf && typeof dateValue.valueOf === 'function') {
        const dateValue_valueOf = dateValue.valueOf()
        console.log('🔄 对象valueOf结果:', dateValue_valueOf)
        date = new Date(dateValue_valueOf)
      } else {
        // 尝试JSON序列化后处理
        const jsonStr = JSON.stringify(dateValue)
        console.log('🔄 对象JSON序列化:', jsonStr)
        date = new Date(JSON.parse(jsonStr))
      }
    } else {
      console.error('❌ convertToTimestamp: 不支持的数据类型:', typeof dateValue)
      return undefined
    }

    console.log('📅 创建的Date对象:', date)

    if (isNaN(date.getTime())) {
      console.error('❌ convertToTimestamp: Date对象无效')
      return undefined
    }

    const timestamp = date.getTime()
    console.log('✅ convertToTimestamp 转换成功:', {
      输入: dateValue,
      输入类型: typeof dateValue,
      输出: timestamp,
      验证: new Date(timestamp).toISOString(),
      本地显示: new Date(timestamp).toLocaleDateString('zh-CN')
    })

    return timestamp
  } catch (error) {
    console.error('❌ convertToTimestamp 转换异常:', error)
    return undefined
  }
}

const convertTimestampToLocalDate = (timestamp: number | null | undefined): string | undefined => {
  if (!timestamp) return undefined
  try {
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return undefined
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch {
    return undefined
  }
}

/** 机构信息 表单 */
defineOptions({ name: 'InfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const activeTab = ref('basic') // 当前激活的标签页

const formData = ref({
  id: undefined,
  name: undefined,
  shortName: undefined,
  logo: undefined,
  type: undefined,
  level: undefined,
  establishDate: undefined,
  businessLicense: undefined,
  businessLicenseImg: undefined,
  legalPerson: undefined,
  legalPersonContact: undefined,
  bedCount: undefined,
  areaSize: undefined,
  contactPhone: undefined,
  emergencyPhone: undefined,
  email: undefined,
  website: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  introduction: undefined,
  features: undefined,
  culture: undefined,
  honors: undefined
})

// 表单校验规则
const formRules = reactive({
  name: [{ required: true, message: '机构名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '机构类型不能为空', trigger: 'change' }],
  level: [{ required: true, message: '机构等级不能为空', trigger: 'change' }],
  contactPhone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  email: [
    { pattern: /^[\w.-]+@[\w.-]+\.\w+$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  website: [
    { pattern: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/, message: '请输入正确的网址', trigger: 'blur' }
  ]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const response = await InfoApi.getInfo(id)
      console.log('🔍 API响应数据:', response)
      console.log('📅 原始成立时间:', response?.establishDate, '类型:', typeof response?.establishDate)

      // 验证数据库数据
      if (!response?.establishDate) {
        console.warn('⚠️ 数据库中成立时间为空或undefined')
      } else {
        console.log('✅ 数据库成立时间存在:', response.establishDate, '类型:', typeof response.establishDate)

        // 详细分析对象类型的成立时间
        if (typeof response.establishDate === 'object') {
          console.log('🔍 对象类型成立时间详细信息:')
          console.log('  - 构造函数:', response.establishDate.constructor?.name)
          console.log('  - toString():', response.establishDate.toString?.())
          console.log('  - valueOf():', response.establishDate.valueOf?.())
          console.log('  - JSON.stringify():', JSON.stringify(response.establishDate))

          if (response.establishDate instanceof Date) {
            console.log('  - 是Date对象')
          } else {
            console.log('  - 不是Date对象，可能是序列化的日期对象')
          }
        }
      }

      // 处理成立时间：支持多种格式 → 时间戳
      const establishDateTimestamp = convertToTimestamp(response?.establishDate)
      console.log('🔄 转换后时间戳:', establishDateTimestamp)

      if (establishDateTimestamp) {
        const verifyDate = new Date(establishDateTimestamp)
        console.log('✅ 时间戳验证 - 对应日期:', verifyDate.toISOString())
        console.log('✅ 时间戳验证 - 本地显示:', verifyDate.toLocaleDateString('zh-CN'))
      } else if (response?.establishDate) {
        console.error('❌ 成立时间转换失败，原始值:', response.establishDate)
      }

      // 确保数据正确赋值
      formData.value = {
        ...formData.value,
        ...response,
        // 特别处理字段
        logo: response?.logo || '',
        businessLicenseImg: response?.businessLicenseImg || '',
        establishDate: establishDateTimestamp
      }

      console.log('📝 表单数据赋值后:', formData.value)
      console.log('🎯 最终成立时间字段:', formData.value.establishDate)

      // 验证表单数据
      if (formData.value.establishDate) {
        console.log('✅ 表单成立时间赋值成功')
      } else if (response?.establishDate) {
        console.error('❌ 表单成立时间赋值失败，数据库有值但表单为空')
      } else {
        console.log('ℹ️ 数据库成立时间为空，表单也为空（正常）')
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // 处理成立时间：时间戳 → LocalDate字符串
    const establishDateString = convertTimestampToLocalDate(formData.value.establishDate)
    console.log('提交前成立时间转换:', formData.value.establishDate, '→', establishDateString)

    const submitData = {
      ...formData.value,
      establishDate: establishDateString
    }
    const data = submitData as unknown as InfoVO

    console.log('提交的数据:', data)

    if (formType.value === 'create') {
      await InfoApi.createInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await InfoApi.updateInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    shortName: undefined,
    logo: undefined,
    type: undefined,
    level: undefined,
    establishDate: undefined,
    businessLicense: undefined,
    businessLicenseImg: undefined,
    legalPerson: undefined,
    legalPersonContact: undefined,
    bedCount: undefined,
    areaSize: undefined,
    contactPhone: undefined,
    emergencyPhone: undefined,
    email: undefined,
    website: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    introduction: undefined,
    features: undefined,
    culture: undefined,
    honors: undefined
  }
  formRef.value?.resetFields()
  activeTab.value = 'basic' // 重置为第一个标签页
}
</script>
