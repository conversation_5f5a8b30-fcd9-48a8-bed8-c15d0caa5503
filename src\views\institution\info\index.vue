<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="机构名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入机构名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="机构类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择机构类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions('organization_type')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="机构等级" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="请选择机构等级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions('organization_level')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button @click="() => showAdvanced = !showAdvanced">
          <Icon :icon="showAdvanced ? 'ep:arrow-up' : 'ep:arrow-down'" class="mr-5px" />
          {{ showAdvanced ? '收起' : '展开' }}
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['institution:info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['institution:info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>

      <!-- 高级搜索区域 -->
      <template v-if="showAdvanced">
        <el-divider content-position="center">高级搜索</el-divider>
        <el-form-item label="机构简称" prop="shortName">
          <el-input
            v-model="queryParams.shortName"
            placeholder="请输入机构简称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="成立时间" prop="establishDate">
          <el-date-picker
            v-model="queryParams.establishDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="法人代表" prop="legalPerson">
          <el-input
            v-model="queryParams.legalPerson"
            placeholder="请输入法人代表"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="床位数量" prop="bedCount">
          <el-input
            v-model="queryParams.bedCount"
            placeholder="请输入床位数量"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input
            v-model="queryParams.address"
            placeholder="请输入详细地址"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </template>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="机构名称" align="center" prop="name" min-width="200">
        <template #default="scope">
          <el-button
            link
            @click="handleViewDetail(scope.row)"
          >
            {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="机构简称" align="center" prop="shortName" min-width="120" />
      <el-table-column label="机构类型" align="center" prop="type" min-width="150">
        <template #default="scope">
          <DictTag :type="'organization_type'" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="机构等级" align="center" prop="level" min-width="150">
        <template #default="scope">
          <DictTag :type="'organization_level'" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column label="床位数量" align="center" prop="bedCount" min-width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" min-width="120" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleViewDetail(scope.row)"
          >
            查看
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['institution:info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['institution:info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InfoForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <el-dialog
    v-model="detailVisible"
    title="机构详情"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="机构名称">{{ detailData.name }}</el-descriptions-item>
      <el-descriptions-item label="机构简称">{{ detailData.shortName }}</el-descriptions-item>
      <el-descriptions-item label="机构类型">
        <DictTag :type="'organization_type'" :value="detailData.type" />
      </el-descriptions-item>
      <el-descriptions-item label="机构等级">
        <DictTag :type="'organization_level'" :value="detailData.level" />
      </el-descriptions-item>
      <el-descriptions-item label="成立时间">
        {{ formatDate(detailData.establishDate) }}
      </el-descriptions-item>
      <el-descriptions-item label="机构logo" :span="2">
        <el-image v-if="detailData.logo" :src="detailData.logo" style="max-height: 100px;" :preview-src-list="[detailData.logo]" />
        <span v-else>暂无图片</span>
      </el-descriptions-item>
      <el-descriptions-item label="营业执照号码">{{ detailData.businessLicense }}</el-descriptions-item>
      <el-descriptions-item label="营业执照图片" :span="2">
        <el-image v-if="detailData.businessLicenseImg" :src="detailData.businessLicenseImg" style="max-height: 150px;" :preview-src-list="[detailData.businessLicenseImg]" />
        <span v-else>暂无图片</span>
      </el-descriptions-item>
      <el-descriptions-item label="法人代表">{{ detailData.legalPerson }}</el-descriptions-item>
      <el-descriptions-item label="法人联系方式">{{ detailData.legalPersonContact }}</el-descriptions-item>
      <el-descriptions-item label="床位数量">{{ detailData.bedCount }}</el-descriptions-item>
      <el-descriptions-item label="占地面积(平方米)">{{ detailData.areaSize }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
      <el-descriptions-item label="紧急联系电话">{{ detailData.emergencyPhone }}</el-descriptions-item>
      <el-descriptions-item label="电子邮箱">{{ detailData.email }}</el-descriptions-item>
      <el-descriptions-item label="官方网站">{{ detailData.website }}</el-descriptions-item>
      <el-descriptions-item label="详细地址" :span="2">{{ detailData.address }}</el-descriptions-item>
      <el-descriptions-item label="机构简介" :span="2">{{ detailData.introduction }}</el-descriptions-item>
      <el-descriptions-item label="机构特色" :span="2">{{ detailData.features }}</el-descriptions-item>
      <el-descriptions-item label="机构文化" :span="2">{{ detailData.culture }}</el-descriptions-item>
      <el-descriptions-item label="机构荣誉" :span="2">{{ detailData.honors }}</el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="detailVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { InfoApi, InfoVO } from '@/api/institution/info'
import InfoForm from './InfoForm.vue'
import { getStrDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
import './index.css'

/** 机构信息 列表 */
defineOptions({ name: 'Info' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InfoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvanced = ref(false) // 是否显示高级搜索
const detailVisible = ref(false) // 详情弹窗是否可见
const detailData = ref<InfoVO>({} as InfoVO) // 详情数据

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  shortName: undefined,
  logo: undefined,
  type: undefined,
  level: undefined,
  establishDate: [],
  businessLicense: undefined,
  businessLicenseImg: undefined,
  legalPerson: undefined,
  legalPersonContact: undefined,
  bedCount: undefined,
  areaSize: undefined,
  contactPhone: undefined,
  emergencyPhone: undefined,
  email: undefined,
  website: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  introduction: undefined,
  features: undefined,
  culture: undefined,
  honors: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InfoApi.getInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看详情操作 */
const handleViewDetail = async (row: InfoVO) => {
  detailData.value = row
  detailVisible.value = true
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InfoApi.deleteInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InfoApi.exportInfo(queryParams)
    download.excel(data, '机构信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
