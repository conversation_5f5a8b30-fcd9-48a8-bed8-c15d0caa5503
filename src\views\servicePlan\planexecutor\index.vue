<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="护理计划" prop="planId">
        <el-select
          v-model="queryParams.planId"
          placeholder="请选择护理计划"
          clearable
          filterable
          remote
          :remote-method="handlePlanSearch"
          :loading="planLoading"
          class="!w-240px"
        >
          <el-option
            v-for="plan in planList"
            :key="plan.id"
            :label="plan.planName"
            :value="plan.id"
          >
            <span>{{ plan.planName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="queryParams.executorId"
          placeholder="请选择执行人"
          clearable
          filterable
          remote
          :remote-method="handleUserSearch"
          :loading="userLoading"
          class="!w-240px"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          >
            <span>{{ user.nickname }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人"
          clearable
          filterable
          remote
          :remote-method="handleElderSearch"
          :loading="elderLoading"
          class="!w-240px"
        >
          <el-option
            v-for="elder in elderList"
            :key="elder.id"
            :label="elder.name"
            :value="elder.id"
          >
            <span>{{ elder.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联任务" prop="taskId">
        <el-select
          v-model="queryParams.taskId"
          placeholder="请选择任务"
          clearable
          filterable
          remote
          :remote-method="handleTaskSearch"
          :loading="taskLoading"
          class="!w-240px"
        >
          <el-option
            v-for="task in taskList"
            :key="task.id"
            :label="task.name"
            :value="task.id"
          >
            <span>{{ task.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务时间" prop="timeRange">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          :shortcuts="dateShortcuts"
          :disabledDate="disabledDate"
          @change="handleDateRangeChange"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          @click="openForm('create')"-->
<!--          v-hasPermi="['servicePlan:plan-executor:create']"-->
<!--        >-->
<!--          <Icon icon="ep:plus" class="mr-5px" /> 新增-->
<!--        </el-button>-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          @click="openConflictCheck('elder')"-->
<!--        >-->
<!--          <Icon icon="ep:warning" class="mr-5px" /> 检测老人冲突-->
<!--        </el-button>-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          @click="openConflictCheck('executor')"-->
<!--        >-->
<!--          <Icon icon="ep:warning" class="mr-5px" /> 检测执行人冲突-->
<!--        </el-button>-->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['servicePlan:plan-executor:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="warning"
          plain
          @click="handleExportExecution"
          :loading="exportExecutionLoading"
          v-hasPermi="['servicePlan:plan-executor:export']"
        >
          <Icon icon="ep:document" class="mr-5px" /> 导出执行表
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column 
        label="护理计划" 
        align="center" 
        prop="planName" 
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column label="执行人" align="center" prop="executorName" />
      <el-table-column label="关联老人" align="center" prop="elderName" />
      <el-table-column label="关联任务" align="center" prop="taskName" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:plan-executor:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:plan-executor:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="开始执行时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="结束执行时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter2"
        width="180px"
      />
      
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PlanExecutorForm ref="formRef" @success="getList" />

  <!-- 添加冲突检测弹窗 -->
  <ConflictCheckDialog
    ref="elderConflictRef"
    type="elder"
  />
  <ConflictCheckDialog
    ref="executorConflictRef"
    type="executor"
  />
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { PlanExecutorApi, PlanExecutorVO } from '@/api/servicePlan/planexecutor'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList } from '@/api/system/user'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'
import { TaskApi } from '@/api/servicePlan/serviceplantask'
import PlanExecutorForm from './PlanExecutorForm.vue'
import ConflictCheckDialog from './components/ConflictCheckDialog.vue'

/** 任务执行人关联 列表 */
defineOptions({ name: 'PlanExecutor' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 日期快捷选项
const dateShortcuts = [
  {
    text: '未来一周',
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '未来两周',
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 14)
      return [start, end]
    }
  },
  {
    text: '未来一个月',
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

const loading = ref(true) // 列表的加载中
const list = ref<PlanExecutorVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  taskId: undefined,
  executorId: undefined,
  elderId: undefined,
  planId: undefined,
  timeRange: undefined, // 用于绑定日期选择器
  startTime: undefined, // 实际传给后端的开始时间
  endTime: undefined,   // 实际传给后端的结束时间
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const exportExecutionLoading = ref(false) // 导出执行的加载中

// 添加加载状态
const planLoading = ref(false)
const userLoading = ref(false)
const elderLoading = ref(false)
const taskLoading = ref(false)

// 添加远程搜索方法
const handlePlanSearch = async (query: string) => {
  planLoading.value = true
  try {
    // 假设 ServicePlanApi.getServicePlanSimpleList() 支持根据计划名称模糊搜索
    // 并且返回的数据结构与之前一致 { id, planName }[]
    const data = await ServicePlanApi.getServicePlanSimpleList(query) // 假设接口支持 query 参数
    planList.value = data // 假设返回的已经是 el-select 需要的格式
  } catch (error) {
    console.error('获取护理计划列表失败:', error)
  } finally {
    planLoading.value = false
  }
}

const handleUserSearch = async (query: string) => {
  userLoading.value = true
  try {
    // 假设 getSimpleUserList() 支持根据昵称模糊搜索
    // 并且返回的数据结构与之前一致 { id, nickname }[]
    const data = await getSimpleUserList(query) // 假设接口支持 query 参数
    userList.value = data // 假设返回的已经是 el-select 需要的格式
  } catch (error) {
    console.error('获取执行人列表失败:', error)
  } finally {
    userLoading.value = false
  }
}

const handleElderSearch = async (query: string) => {
  elderLoading.value = true
  try {
    // 假设 ArchivesProfileApi.getArchivesProfileSimpleList() 支持根据姓名模糊搜索
    // 并且返回的数据结构与之前一致 { id, name }[]
    const data = await ArchivesProfileApi.getArchivesProfileSimpleList(query) // 假设接口支持 query 参数
    elderList.value = data // 假设返回的已经是 el-select 需要的格式
  } catch (error) {
    console.error('获取老人列表失败:', error)
  } finally {
    elderLoading.value = false
  }
}

const handleTaskSearch = async (query: string) => {
  taskLoading.value = true
  try {
    // 假设 TaskApi.getTaskSimpleList() 支持根据任务名称模糊搜索
    // 并且返回的数据结构与之前一致 { id, name }[]
    // 如果 TaskApi 中没有 getTaskSimpleList() 方法，需要根据实际后端接口进行调整
    const data = await TaskApi.getTaskSimpleList(query) // 假设接口支持 query 参数
    taskList.value = data // 假设返回的已经是 el-select 需要的格式
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    taskLoading.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PlanExecutorApi.getPlanExecutorPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 日期禁用函数 */
const disabledDate = (time: Date, currentValue) => {
  if (!currentValue || !currentValue.length) {
    return false
  }
  
  const [startTime, endTime] = currentValue
  
  // 如果已选择开始日期，限制结束日期不能超过开始日期一个月
  if (startTime && !endTime) {
    const oneMonthLater = new Date(startTime)
    oneMonthLater.setMonth(oneMonthLater.getMonth() + 1)
    return time > oneMonthLater
  }
  
  // 如果已选择结束日期，限制开始日期不能早于结束日期一个月
  if (!startTime && endTime) {
    const oneMonthEarlier = new Date(endTime)
    oneMonthEarlier.setMonth(oneMonthEarlier.getMonth() - 1)
    return time < oneMonthEarlier
  }
  
  return false
}

/** 处理日期范围变化 */
const handleDateRangeChange = (val) => {
  if (!val) {
    return
  }
  
  const [start, end] = val
  
  // 如果选择的日期范围超过一个月，给出提示并重置
  if (start && end) {
    const startDate = new Date(start)
    const endDate = new Date(end)
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays > 31) {
      message.warning('查询时间范围不能超过一个月')
      queryParams.timeRange = undefined
    }
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  
  // 检查时间范围
  if (queryParams.timeRange && queryParams.timeRange.length === 2) {
    const [start, end] = queryParams.timeRange
    const startDate = new Date(start)
    const endDate = new Date(end)
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays > 31) {
      message.warning('查询时间范围不能超过一个月')
      return
    }
    
    queryParams.startTime = start
    queryParams.endTime = end
  } else {
    queryParams.startTime = undefined
    queryParams.endTime = undefined
  }
  
  // 发起查询
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.timeRange = undefined // 确保时间范围也被重置
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PlanExecutorApi.deletePlanExecutor(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PlanExecutorApi.exportPlanExecutor(queryParams)
    download.excel(data, '任务执行人关联.xlsx')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 导出执行按钮操作 */
const handleExportExecution = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportExecutionLoading.value = true
    const data = await PlanExecutorApi.exportPlanExecutorExecution(queryParams)
    download.excel(data, '任务执行人分配信息.xlsx')
  } catch {
  } finally {
    exportExecutionLoading.value = false
  }
}

/** 冲突检测弹窗引用 */
const elderConflictRef = ref()
const executorConflictRef = ref()

/** 打开冲突检测弹窗 */
const openConflictCheck = (type: 'elder' | 'executor') => {
  if (type === 'elder') {
    elderConflictRef.value?.open()
  } else {
    executorConflictRef.value?.open()
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
})
</script>
