<template>
  <Dialog :title="'复制服务计划'" v-model="dialogVisible" width="1300px">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="copy-plan-form form-scaled"
      v-loading="loading"
    >
      <div class="flex">
        <!-- 左侧表单 -->
        <div class="form-container mr-4">
          <!-- 显示原计划信息 -->
          <div class="original-plan-info mb-4 p-4 bg-gray-50 rounded">
            <h4 class="text-base font-medium mb-2">原计划信息</h4>
            <p class="text-sm mb-2">计划名称：{{ planInfo.planName }}</p>
            <p class="text-sm mb-2">关联老人：{{ formatElder(planInfo.elderId) }}</p>
            <p class="text-sm mb-2">计划分类：{{ planInfo.categoryName }}</p>
            <p class="text-sm mb-2">计划时间：{{ planInfo.startDate }} 至 {{ planInfo.endDate }}</p>
          </div>
          <el-form-item label="计划名称" prop="planName">
            <el-input
              v-model="form.planName"
              placeholder="请输入计划名称"
              clearable
              class="w-full"
            />
          </el-form-item>
          <!-- 复制表单 -->
          <el-form-item label="计划分类" prop="categoryId">
            <el-tree-select
              v-model="form.categoryId"
              :data="categoryTree"
              :props="categoryProps"
              placeholder="请选择计划分类"
              clearable
              class="w-full"
            />
          </el-form-item>
        
          <el-form-item label="选择老人" prop="elderId">
            <el-select
              v-model="form.elderId"
              placeholder="请选择老人"
              clearable
              class="w-full"
            >
              <el-option
                v-for="item in elderList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>    
          <el-form-item label="计划日期" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled-date="disabledDate"
              class="w-full"
            />
          </el-form-item>
        
          <el-form-item label="是否复制任务" prop="copyTasks">
            <el-switch v-model="form.copyTasks" />
          </el-form-item>
        </div>
        
        <!-- 右侧描述预览 -->
        <div class="description-container flex-1 p-4 bg-gray-50 rounded">
          <h4 class="text-base font-medium mb-2">计划描述</h4>
          <div class="markdown-body" v-html="descriptionHtml">
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <el-button size="large" @click="dialogVisible = false">取 消</el-button>
      <el-button size="large" type="primary" @click="handleConfirm" :loading="confirmLoading">
        确 定
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'
import { CategoryApi } from '@/api/servicePlan/serviceplancategory'
import type { ServicePlanVO } from '@/api/servicePlan/serviceplan'
import { computed } from 'vue'
import dayjs from 'dayjs'

// 分类树配置
const categoryProps = {
  value: 'id',
  label: 'name',
  children: 'children',
}

defineOptions({ name: 'CopyPlanDialog' })

const props = defineProps<{
  elderList: {
    id: number
    name: string
  }[]
}>()

const emit = defineEmits(['success', 'confirm'])
const message = useMessage()

const dialogVisible = ref(false)
const loading = ref(false)
const confirmLoading = ref(false)
const planInfo = ref<Partial<ServicePlanVO & { 
  description?: string;
  categoryName?: string;
}>>({})
const categoryTree = ref<any[]>([])
const dateRange = ref<[string, string] | null>(null)
const isBatchMode = ref(false)
const batchCallback = ref<((planParams: any) => void) | null>(null)

const form = ref({
  elderId: undefined as number | undefined,
  planName: '',
  categoryId: undefined as number | undefined,
  copyTasks: true, // 默认复制任务
})

const rules = {
  elderId: [{ required: true, message: '请选择老人', trigger: 'change' }],
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择计划分类', trigger: 'change' }],
  dateRange: [
    { 
      required: true, 
      message: '请选择计划日期', 
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (!dateRange.value) {
          callback(new Error('请选择计划日期'))
          return
        }
        
        const startDate = dayjs(dateRange.value[0])
        const endDate = dayjs(dateRange.value[1])
        const monthsDiff = endDate.diff(startDate, 'month', true)

        if (monthsDiff > 6) {
          callback(new Error('计划时长不能超过6个月'))
          return
        }

        callback()
      }
    }
  ]
}

const formRef = ref()

// 初始化 markdown-it
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true,
})

// 计算属性：转换 markdown 为 html
const descriptionHtml = computed(() => {
  return md.render(planInfo.value.description || '')
})

/** 格式化老人显示 */
const formatElder = (elderId: number) => {
  const elder = props.elderList.find(item => item.id === elderId)
  return elder?.name || elderId
}

/** 获取分类树 */
const getCategoryTree = async () => {
  try {
    const data = await CategoryApi.generateSimpleTree()
    categoryTree.value = data
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }
}

/** 打开弹窗 */
const open = async (plan: ServicePlanVO, onConfirm?: (planParams: any) => void) => {
  dialogVisible.value = true
  loading.value = true
  isBatchMode.value = !!onConfirm
  batchCallback.value = onConfirm || null
  
  try {
    await getCategoryTree()
    // 获取完整的计划信息
    const data = await ServicePlanApi.getServicePlan(plan.id)
    planInfo.value = data
    // 设置默认计划名称和时间
    form.value = {
      elderId: data.elderId,
      planName: data.planName,
      categoryId: data.categoryId,
      copyTasks: true,
    }
    dateRange.value = [data.startDate, data.endDate]
  } catch (error) {
    console.error('获取计划信息失败:', error)
    message.error('获取计划信息失败')
  } finally {
    loading.value = false
  }
}

/** 确认复制 */
const handleConfirm = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    // 构造请求参数
    const planParams = {
      id: planInfo.value.id!,
      status: 1,
      elderId: form.value.elderId!,
      planName: form.value.planName,
      categoryId: form.value.categoryId!,
      startDate: new Date(dateRange.value![0]),
      endDate: new Date(dateRange.value![1]),
      copyTasks: form.value.copyTasks,
    }

    if (isBatchMode.value && batchCallback.value) {
      // 批量复制模式
      batchCallback.value(planParams)
      dialogVisible.value = false
    } else {
      // 单个复制模式
      await ServicePlanApi.copyServicePlan(planParams, form.value.copyTasks)
      message.success('复制成功')
      dialogVisible.value = false
      emit('success')
    }
  } catch (error) {
    console.error('复制计划失败:', error)
    message.error('复制计划失败')
  } finally {
    confirmLoading.value = false
  }
}

// 日期禁用函数
const disabledDate = (time: Date) => {
  // 禁用今天之前的日期
  if (time.getTime() < dayjs().startOf('day').valueOf()) {
    return true
  }
  
  // 如果已选择开始日期，限制结束日期不能超过开始日期6个月
  if (dateRange.value?.[0] && !dateRange.value[1]) {
    const startDate = dayjs(dateRange.value[0])
    const maxEndDate = startDate.add(6, 'month')
    return time.getTime() > maxEndDate.valueOf()
  }
  
  return false
}

defineExpose({ open })
</script>

<style scoped>
/* 表单整体放大20% - 使用更精确的方法 */
.form-scaled {
  /* 不使用transform，而是直接调整各个元素的尺寸 */

  :deep(.el-form-item) {
    margin-bottom: 26px; /* 22px * 1.2 */
  }

  :deep(.el-form-item__label) {
    font-size: 16.8px; /* 14px * 1.2 */
    line-height: 1.6;
  }

  :deep(.el-input) {
    font-size: 16.8px;

    .el-input__inner {
      height: 38px; /* 32px * 1.2 */
      line-height: 38px;
      font-size: 16.8px;
      padding: 0 18px; /* 15px * 1.2 */
    }
  }

  :deep(.el-select) {
    font-size: 16.8px;

    .el-input__inner {
      height: 38px;
      line-height: 38px;
      font-size: 16.8px;
    }
  }

  :deep(.el-date-editor) {
    .el-input__inner {
      height: 38px;
      line-height: 38px;
      font-size: 16.8px;
    }
  }

  :deep(.el-switch) {
    .el-switch__label {
      font-size: 16.8px;
    }
  }

  :deep(.el-button) {
    font-size: 16.8px;
    padding: 10px 18px; /* 8px 15px * 1.2 */

    &.el-button--large {
      padding: 14px 24px; /* 12px 20px * 1.2 */
      font-size: 16.8px;
    }
  }
}

.original-plan-info {
  border: 1px solid var(--el-border-color-lighter);
}

.description-container {
  border: 1px solid var(--el-border-color-lighter);
  min-height: 400px;
}

:deep(.markdown-body) {
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  min-height: 300px;
  overflow-y: auto;
  max-height: 500px;

  h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  p {
    margin-bottom: 16px;
    line-height: 1.6;
  }

  ul, ol {
    padding-left: 2em;
    margin-bottom: 16px;
  }

  code {
    padding: 0.2em 0.4em;
    background-color: rgba(27,31,35,0.05);
    border-radius: 3px;
  }
}
</style> 