<template>
  <Dialog title="任务详情" v-model="dialogVisible" width="800px">
    <el-form :model="formData" label-width="100px" class="detail-form">
      <el-form-item label="护理计划">
        <span class="detail-text">{{ formatPlan(formData.planId) }}</span>
      </el-form-item>
      <el-form-item label="关联老人">
        <span class="detail-text">{{ formatElder(formData.elderId) }}</span>
      </el-form-item>
      <el-form-item label="任务名称">
        <span class="detail-text">{{ formData.name || '-' }}</span>
      </el-form-item>
      <el-form-item label="任务类型">
        <span class="detail-text">{{ formData.taskType || '-' }}</span>
      </el-form-item>
      
      <el-form-item label="状态">
        <dict-tag
          v-if="formData.status !== null && formData.status !== undefined"
          :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS"
          :value="Number(formData.status)"
        />
        <span v-else class="detail-text">-</span>
      </el-form-item>
      
      <el-form-item label="是否周期性">
        <div class="recurring-info">
          <dict-tag
            v-if="formData.isRecurring !== null && formData.isRecurring !== undefined"
            :type="DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING"
            :value="Number(formData.isRecurring)"
          />
          <span v-else class="detail-text">-</span>

          <!-- 周期性日期展示 -->
          <span v-if="formData.isRecurring === 1 && getWeekDaysDisplay()" class="week-days-display">
            {{ getWeekDaysDisplay() }}
          </span>
        </div>
      </el-form-item>

      <!-- 周期性规则展示 -->
      <el-form-item v-if="formData.isRecurring === 1" label="周期性规则">
        <span class="detail-text">{{ formData.repeatPattern || '-' }}</span>
      </el-form-item>
      
      <!-- 时间信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间">
            <span class="detail-text">{{ formData.startTime || '-' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间">
            <span class="detail-text">{{ formData.endTime || '-' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="推荐执行人">
        <span class="detail-text">{{ formData.recommendedExecutorNames || '-' }}</span>
      </el-form-item>
      
      <el-form-item label="优先级">
        <dict-tag
          v-if="formData.priority !== null && formData.priority !== undefined"
          :type="DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY"
          :value="Number(formData.priority)"
        />
        <span v-else class="detail-text">-</span>
      </el-form-item>
      
      <el-form-item label="执行细则">
        <div class="detail-description" v-html="formData.description || '-'"></div>
      </el-form-item>
      
      <el-form-item label="创建时间">
        <span class="detail-text">{{ formatDateTime(formData.createTime) }}</span>
      </el-form-item>
      
      <el-form-item label="更新时间">
        <span class="detail-text">{{ formatDateTime(formData.updateTime) }}</span>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'

/** 任务详情 */
defineOptions({ name: 'TaskDetail' })

// 定义props
interface Props {
  elderMap: Map<number, string>
  planMap: Map<number, string>
}

const props = withDefaults(defineProps<Props>(), {
  elderMap: () => new Map(),
  planMap: () => new Map()
})

const dialogVisible = ref(false) // 弹窗的是否展示
const formData = ref<any>({}) // 表单数据

/** 打开弹窗 */
const open = async (data: any) => {
  dialogVisible.value = true
  formData.value = { ...data }
}

/** 格式化老人显示 */
const formatElder = (elderId: number | string) => {
  if (!elderId) return '-'
  const id = Number(elderId)
  return props.elderMap.get(id) || `老人ID: ${id}`
}

/** 格式化服务计划显示 */
const formatPlan = (planId: number) => {
  if (!planId) return '-'
  return props.planMap.get(planId) || `计划ID: ${planId}`
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return formatDate(dateTime, 'YYYY-MM-DD HH:mm:ss')
}

/** 获取周期性日期显示 */
const getWeekDaysDisplay = () => {
  if (!formData.value.repeatPattern) return ''

  // 从周期性规则中提取星期几信息
  const pattern = formData.value.repeatPattern

  // 查找【】中的内容
  const match = pattern.match(/每周【([^】]+)】/)
  if (match) {
    const dayText = match[1]
    return `(${dayText})`
  }

  // 如果没有找到标准格式，尝试其他常见格式
  const commonPatterns = [
    { pattern: /每天/, display: '(每天)' },
    { pattern: /工作日/, display: '(周一至周五)' },
    { pattern: /周末/, display: '(周六、周日)' },
    { pattern: /每周一/, display: '(周一)' },
    { pattern: /每周二/, display: '(周二)' },
    { pattern: /每周三/, display: '(周三)' },
    { pattern: /每周四/, display: '(周四)' },
    { pattern: /每周五/, display: '(周五)' },
    { pattern: /每周六/, display: '(周六)' },
    { pattern: /每周日/, display: '(周日)' }
  ]

  for (const item of commonPatterns) {
    if (item.pattern.test(pattern)) {
      return item.display
    }
  }

  return ''
}

// 提供给父组件调用的方法
defineExpose({ open })
</script>

<style scoped>
.detail-form {
  padding: 20px 0;
}

.detail-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.detail-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 字典标签样式 */
:deep(.el-tag) {
  font-weight: 500;
}

/* 周期性信息容器 */
.recurring-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 周期性日期显示 */
.week-days-display {
  color: #67c23a;
  font-size: 13px;
  font-weight: 500;
  background-color: #f0f9ff;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #b3e5fc;
}
</style>
