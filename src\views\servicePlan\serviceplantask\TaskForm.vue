<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="护理计划" prop="planId">
        <el-select
          v-model="formData.planId"
          placeholder="请选择护理计划"
          clearable
          filterable
          remote
          :remote-method="handlePlanSearch"
          :loading="false"
          class="w-full"
        >
          <el-option
            v-for="item in props.planList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="formData.elderId"
          placeholder="请选择关联老人"
          clearable
          filterable
          remote
          :remote-method="handleElderSearch"
          :loading="false"
          class="w-full"
        >
          <el-option
            v-for="item in props.elderList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="任务分类" prop="categoryId">
        <el-tree-select
          v-model="formData.categoryId"
          :data="categoryTree"
          :props="categoryProps"
          placeholder="请选择任务分类"
          clearable
          remote
          :remote-method="handleCategorySearch"
          :loading="categoryLoading"
          @update:model-value="handleCategoryChange"
          class="w-full"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in taskStatusOptions"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否周期性" prop="isRecurring">
        <el-radio-group v-model="formData.isRecurring" @change="handleRecurringChange">
          <el-radio
            v-for="dict in isRecurringOptions"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 周期性规则配置 - 仅在选择周期性时显示 -->
      <template v-if="formData.isRecurring === 1">
        <el-form-item label="执行日期" prop="weekDays">
          <el-checkbox-group v-model="selectedWeekDays" @change="handleWeekDaysChange">
            <el-checkbox
              v-for="day in weekDayOptions"
              :key="day.value"
              :label="day.value"
              class="week-day-checkbox"
            >
              {{ day.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="formData.startTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择开始时间"
                clearable
                @change="updateRepeatPattern"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="formData.endTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择结束时间"
                clearable
                @change="updateRepeatPattern"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="规则预览">
          <el-input
            v-model="formData.repeatPattern"
            readonly
            placeholder="请选择执行日期和时间"
            class="pattern-preview"
          />
        </el-form-item>
      </template>

      <!-- 非周期性任务的时间选择 -->
      <template v-else>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="formData.startTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择开始时间"
                clearable
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="formData.endTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择结束时间"
                clearable
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-form-item label="执行人" prop="recommendedExecutors">
        <el-select
          v-model="selectedExecutors"
          multiple
          placeholder="请选择执行人"
          clearable
          filterable
          remote
          :remote-method="handleUserSearch"
          :loading="false"
          class="w-full"
          @change="handleExecutorsChange"
        >
          <el-option
            v-for="item in props.userList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option
            v-for="dict in priorityOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行细则" prop="description">
        <Editor v-model="formData.description" height="250px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList } from '@/api/system/user'
import { useDictStoreWithOut } from '@/store/modules/dict'

/** 服务任务 表单 */
defineOptions({ name: 'TaskForm' })

// 定义props
interface Props {
  userList: { id: number; label: string; value: number }[]
  elderList: { id: number; label: string; value: number }[]
  planList: { id: number; label: string; value: number }[]
}

const props = withDefaults(defineProps<Props>(), {
  userList: () => [],
  elderList: () => [],
  planList: () => []
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dictStore = useDictStoreWithOut() // 字典存储

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 添加分类相关的数据
const categoryTree = ref<any[]>([])
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

// 修改表单数据，添加分类字段
const formData = ref({
  id: undefined,
  planId: undefined,
  elderId: undefined,
  categoryId: undefined,
  taskType: undefined, // 用于存储分类名称
  status: undefined,
  isRecurring: undefined as number | undefined,
  repeatPattern: undefined,
  startTime: undefined,
  endTime: undefined,
  recommendedExecutors: undefined,
  priority: undefined,
  description: undefined,
})

// 自定义验证规则：周期性任务必须选择执行日期
const validateWeekDays = (rule: any, value: any, callback: any) => {
  if (formData.value.isRecurring === 1 && selectedWeekDays.value.length === 0) {
    callback(new Error('周期性任务必须选择执行日期'))
  } else {
    callback()
  }
}

// 添加分类相关的表单验证
const formRules = reactive({
  planId: [{ required: true, message: '关联护理计划ID不能为空', trigger: 'blur' }],
  elderId: [{ required: true, message: '关联老人表不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择任务分类', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  isRecurring: [{ required: true, message: '是否周期任务不能为空', trigger: 'blur' }],
  weekDays: [{ validator: validateWeekDays, trigger: 'change' }],
  startTime: [{ required: true, message: '任务开始时间点不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '任务结束时间点不能为空', trigger: 'blur' }],
  priority: [{ required: true, message: '优先级不能为空', trigger: 'change' }],
  description: [{ required: true, message: '执行细则不能为空', trigger: 'blur' }],
})

const formRef = ref() // 表单 Ref

// 添加加载状态
const categoryLoading = ref(false)

// 选中的执行人
const selectedExecutors = ref<number[]>([])

// 选中的星期几
const selectedWeekDays = ref<number[]>([])

// 星期几选项
const weekDayOptions = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 字典数据响应式计算属性
const taskStatusOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)
})

const isRecurringOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)
})

const priorityOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)
})

/** 处理执行人变化 */
const handleExecutorsChange = (values: number[]) => {
  formData.value.recommendedExecutors = values.length > 0 ? JSON.stringify(values) : undefined
}

/** 处理周期性选择变化 */
const handleRecurringChange = (value: number) => {
  if (value === 0) {
    // 选择非周期性时，清空相关数据
    selectedWeekDays.value = []
    formData.value.repeatPattern = undefined
  } else {
    // 选择周期性时，更新规则
    updateRepeatPattern()
  }
  // 触发执行日期字段的验证
  nextTick(() => {
    formRef.value?.validateField('weekDays')
  })
}

/** 处理执行日期变化 */
const handleWeekDaysChange = () => {
  updateRepeatPattern()
  // 触发验证
  nextTick(() => {
    formRef.value?.validateField('weekDays')
  })
}

/** 更新周期性规则文本 */
const updateRepeatPattern = () => {
  if (formData.value.isRecurring !== 1) {
    return
  }

  // 构建星期几文本
  const dayNames = ['一', '二', '三', '四', '五', '六', '日']
  const selectedDayNames = selectedWeekDays.value
    .sort((a, b) => a - b)
    .map(day => dayNames[day - 1])

  let pattern = ''

  if (selectedDayNames.length > 0) {
    pattern = `每周【${selectedDayNames.join('，')}】`
  }

  if (formData.value.startTime) {
    pattern += pattern ? `，${formData.value.startTime}` : formData.value.startTime
  }

  if (formData.value.endTime) {
    pattern += pattern ? ` - ${formData.value.endTime}` : formData.value.endTime
  }

  formData.value.repeatPattern = pattern || undefined
}

/** 解析周期性规则文本，提取星期几信息 */
const parseRepeatPattern = (pattern: string) => {
  // 尝试从规则文本中提取星期几信息
  const dayMap = { '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 7 }
  const days: number[] = []

  // 查找【】中的内容
  const match = pattern.match(/每周【([^】]+)】/)
  if (match) {
    const dayText = match[1]
    Object.entries(dayMap).forEach(([name, value]) => {
      if (dayText.includes(name)) {
        days.push(value)
      }
    })
  }

  selectedWeekDays.value = days
}

/** 处理分类变化 */
const handleCategoryChange = (categoryId: number | null) => {
  if (!categoryId) {
    formData.value.categoryId = undefined
    formData.value.taskType = undefined
    return
  }
  
  // 查找分类名称
  const findCategory = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return node
      }
      if (node.children) {
        const found = findCategory(node.children)
        if (found) return found
      }
    }
    return null
  }

  const category = findCategory(categoryTree.value)
  formData.value.categoryId = categoryId
  formData.value.taskType = category?.name
}

/** 获取分类树数据 */
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }
}

/** 初始化字典数据 */
const initDictData = async () => {
  if (!dictStore.getIsSetDict) {
    await dictStore.setDictMap()
  }

  // 检查关键字典数据是否存在，如果不存在则重新加载
  if (taskStatusOptions.value.length === 0 ||
      isRecurringOptions.value.length === 0 ||
      priorityOptions.value.length === 0) {
    console.warn('TaskForm字典数据缺失，重新加载...')
    await dictStore.resetDict()
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 确保字典数据已加载
  await initDictData()

  // 获取分类树数据
  await getCategoryTree()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await TaskApi.getTask(id)
      formData.value = {
        ...data,
        isRecurring: Number(data.isRecurring)
      }
      // 设置选中的执行人
      if (data.recommendedExecutors) {
        try {
          selectedExecutors.value = JSON.parse(data.recommendedExecutors)
        } catch (e) {
          console.error('解析执行人数据失败:', e)
        }
      }

      // 解析周期性规则，提取星期几信息
      if (data.isRecurring === 1 && data.repeatPattern) {
        parseRepeatPattern(data.repeatPattern)
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  try {
    // 校验表单
    await formRef.value.validate()

    // 提交请求
    formLoading.value = true

    // 准备提交数据，只包含API需要的字段
    const submitData = {
      id: formData.value.id,
      planId: formData.value.planId,
      elderId: formData.value.elderId,
      taskType: formData.value.taskType, // 使用分类名称作为任务类型
      status: formData.value.status,
      isRecurring: formData.value.isRecurring,
      repeatPattern: formData.value.repeatPattern,
      startTime: formData.value.startTime,
      endTime: formData.value.endTime,
      recommendedExecutors: formData.value.recommendedExecutors,
      priority: formData.value.priority,
      description: formData.value.description
    }

    // 清理空值字段
    Object.keys(submitData).forEach(key => {
      if (submitData[key] === undefined || submitData[key] === null || submitData[key] === '') {
        delete submitData[key]
      }
    })

    console.log('提交数据:', submitData)
    console.log('表单类型:', formType.value)

    if (formType.value === 'create') {
      await TaskApi.createTask(submitData as unknown as TaskVO)
      message.success(t('common.createSuccess'))
    } else {
      await TaskApi.updateTask(submitData as unknown as TaskVO)
      message.success(t('common.updateSuccess'))
    }

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)

    // 详细的错误信息
    if (error?.response?.data?.message) {
      message.error(`服务器错误: ${error.response.data.message}`)
    } else if (error?.message) {
      message.error(`提交失败: ${error.message}`)
    } else {
      message.error('提交失败，请检查网络连接或联系管理员')
    }
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    planId: undefined,
    elderId: undefined,
    categoryId: undefined,
    taskType: undefined,
    status: undefined,
    isRecurring: undefined,
    repeatPattern: undefined,
    startTime: undefined,
    endTime: undefined,
    recommendedExecutors: undefined,
    priority: undefined,
    description: undefined,
  }
  selectedExecutors.value = []
  selectedWeekDays.value = []
  formRef.value?.resetFields()
  // 清除执行日期字段的验证状态
  nextTick(() => {
    formRef.value?.clearValidate('weekDays')
  })
}

// 远程搜索方法 - 现在使用props中的数据，不需要重新获取
const handlePlanSearch = async (query: string) => {
  // 护理计划数据已通过props传递，无需重新获取
  // 如果需要搜索功能，可以在父组件中实现
}

const handleElderSearch = async (query: string) => {
  // 老人数据已通过props传递，无需重新获取
  // 如果需要搜索功能，可以在父组件中实现
}

const handleCategorySearch = async (query: string) => {
  categoryLoading.value = true
  try {
    const data = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = data
  } catch (error) {
    console.error('获取任务分类树失败:', error)
  } finally {
    categoryLoading.value = false
  }
}

const handleUserSearch = async (query: string) => {
  // 用户数据已通过props传递，无需重新获取
  // 如果需要搜索功能，可以在父组件中实现
}
</script>

<style scoped>
/* 添加树形选择器样式 */
:deep(.el-tree-select) {
  width: 100%;
}

/* 星期几选择框样式 */
.week-day-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

:deep(.week-day-checkbox .el-checkbox__label) {
  font-weight: 500;
  padding-left: 8px;
}

/* 规则预览样式 */
.pattern-preview {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

:deep(.pattern-preview .el-input__inner) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
</style>
