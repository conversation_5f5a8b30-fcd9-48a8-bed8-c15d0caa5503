<template>
  <ContentWrap>
    <el-card header="字典数据调试">
      <div class="debug-section">
        <h3>SERVICE_PLAN_TASK_STATUS 字典数据:</h3>
        <pre>{{ JSON.stringify(taskStatusDict, null, 2) }}</pre>
        
        <h3>SERVICE_PLAN_TASK_IS_RECURRING 字典数据:</h3>
        <pre>{{ JSON.stringify(isRecurringDict, null, 2) }}</pre>
        
        <h3>SERVICE_PLAN_TASK_PRIORITY 字典数据:</h3>
        <pre>{{ JSON.stringify(priorityDict, null, 2) }}</pre>
        
        <h3>字典存储状态:</h3>
        <p>字典是否已设置: {{ dictStore.getIsSetDict }}</p>
        <p>字典Map大小: {{ Object.keys(dictStore.getDictMap).length }}</p>
        
        <h3>所有字典类型:</h3>
        <pre>{{ JSON.stringify(Object.keys(dictStore.getDictMap), null, 2) }}</pre>

        <h3>测试下拉框:</h3>
        <el-form>
          <el-form-item label="状态测试">
            <el-select v-model="testStatus" placeholder="请选择状态">
              <el-option
                v-for="dict in taskStatusDict"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否周期性测试">
            <el-select v-model="testRecurring" placeholder="请选择是否周期性">
              <el-option
                v-for="dict in isRecurringDict"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级测试">
            <el-select v-model="testPriority" placeholder="请选择优先级">
              <el-option
                v-for="dict in priorityDict"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <h3>DictTag测试:</h3>
        <p>选中状态值: {{ testStatus }} (类型: {{ typeof testStatus }})</p>
        <p>状态DictTag: <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS" :value="testStatus" /></p>

        <p>选中周期性值: {{ testRecurring }} (类型: {{ typeof testRecurring }})</p>
        <p>周期性DictTag: <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING" :value="testRecurring" /></p>

        <p>选中优先级值: {{ testPriority }} (类型: {{ typeof testPriority }})</p>
        <p>优先级DictTag: <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY" :value="testPriority" /></p>

        <h3>模拟表格数据测试:</h3>
        <el-table :data="mockTableData" border>
          <el-table-column label="状态值" prop="status" />
          <el-table-column label="状态类型" align="center">
            <template #default="{ row }">
              {{ typeof row.status }}
            </template>
          </el-table-column>
          <el-table-column label="状态显示" align="center">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS" :value="row.status" />
            </template>
          </el-table-column>
        </el-table>

        <el-button @click="refreshDict" type="primary">刷新字典数据</el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useDictStoreWithOut } from '@/store/modules/dict'

defineOptions({ name: 'DebugDict' })

const dictStore = useDictStoreWithOut()

// 测试用的响应式数据
const testStatus = ref()
const testRecurring = ref()
const testPriority = ref()

// 模拟表格数据
const mockTableData = ref([
  { status: 1 },
  { status: '1' },
  { status: 2 },
  { status: '2' },
  { status: null },
  { status: undefined },
  { status: 0 }
])

const taskStatusDict = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)
})

const isRecurringDict = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)
})

const priorityDict = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)
})

const refreshDict = async () => {
  await dictStore.resetDict()
}

onMounted(async () => {
  if (!dictStore.getIsSetDict) {
    await dictStore.setDictMap()
  }
})
</script>

<style scoped>
.debug-section {
  padding: 20px;
}

.debug-section h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #409eff;
}

.debug-section pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
