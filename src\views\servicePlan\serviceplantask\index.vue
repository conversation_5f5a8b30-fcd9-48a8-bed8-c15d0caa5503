<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="护理计划" prop="planId">
        <el-select
          v-model="queryParams.planId"
          placeholder="请选择护理计划"
          clearable
          filterable
          remote
          :remote-method="handlePlanSearch"
          :loading="planLoading"
          class="!w-240px"
        >
          <el-option
            v-for="item in planList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联老人" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择关联老人"
          clearable
          filterable
          remote
          :remote-method="handleElderSearch"
          :loading="elderLoading"
          class="!w-240px"
        >
          <el-option
            v-for="item in elderList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务分类" prop="categoryId">
        <el-tree-select
          v-model="queryParams.categoryId"
          :data="categoryTree"
          :props="categoryProps"
          placeholder="请选择任务分类"
          clearable
          remote
          :remote-method="handleCategorySearch"
          :loading="categoryLoading"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in taskStatusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行人" prop="executorId">
        <el-select
          v-model="queryParams.executorId"
          placeholder="请选择执行人"
          clearable
          filterable
          remote
          :remote-method="handleUserSearch"
          :loading="userLoading"
          class="!w-240px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否周期任务" prop="isRecurring">
        <el-select
          v-model="queryParams.isRecurring"
          placeholder="请选择是否周期任务"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in isRecurringOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select
          v-model="queryParams.priority"
          placeholder="请选择优先级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in priorityOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['servicePlan:task:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['servicePlan:task:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>



      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="任务名称" align="center" min-width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span
            v-if="row.name"
            class="plan-link"
            @click="handleDetail(row)"
            :title="'点击查看任务详情'"
          >
            {{ row.name }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="关联老人" align="center">
        <template #default="{ row }">
          {{ formatElder(row.elderId) }}
        </template>
      </el-table-column>
      <el-table-column label="护理计划" align="center" min-width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.planId">
            {{ formatPlan(row.planId) }}
          </span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="任务类型" align="center" prop="taskType" min-width="120px" show-overflow-tooltip />
      
      <el-table-column label="状态" align="center" prop="status" width="100px">
        <template #default="scope">
          <dict-tag
            v-if="scope.row.status !== null && scope.row.status !== undefined"
            :type="DICT_TYPE.SERVICE_PLAN_TASK_STATUS"
            :value="Number(scope.row.status)"
          />
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column label="是否周期性" align="center" prop="isRecurring">
        <template #default="scope">
          <dict-tag 
            :type="DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING" 
            :value="Number(scope.row.isRecurring)" 
          />
        </template>
      </el-table-column>

      <el-table-column label="优先级" align="center" prop="priority">
        <template #default="scope">
          <dict-tag
            v-if="scope.row.priority !== null && scope.row.priority !== undefined"
            :type="DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY"
            :value="Number(scope.row.priority)"
          />
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="160px">
        <template #default="scope">
          <el-button
            link
            type="success"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:task:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:task:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskForm
    ref="formRef"
    @success="getList"
    :user-list="userList"
    :elder-list="elderList"
    :plan-list="planList"
  />

  <!-- 详情弹窗 -->
  <TaskDetail
    ref="detailRef"
    :elder-map="elderMap"
    :plan-map="planMap"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TaskApi, TaskVO } from '@/api/servicePlan/serviceplantask'
import { getSimpleUserList } from '@/api/system/user'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { ServicePlanApi } from '@/api/servicePlan/serviceplan'
import TaskForm from './TaskForm.vue'
import TaskDetail from './TaskDetail.vue'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'
import { useDictStoreWithOut } from '@/store/modules/dict'

/** 服务任务 列表 */
defineOptions({ name: 'ServicePlanTask' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const dictStore = useDictStoreWithOut() // 字典存储

const loading = ref(true) // 列表的加载中
const list = ref<TaskVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  planId: undefined,
  elderId: undefined,
  categoryId: undefined,
  status: undefined,
  isRecurring: undefined,
  priority: undefined,
  executorId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中



// 存储执行人和老人的映射关系
const executorMap = ref<Map<number, string>>(new Map())
const elderMap = ref<Map<number, string>>(new Map())
// 存储服务计划的映射关系
const planMap = ref<Map<number, string>>(new Map())

// 定义列表数据
interface SelectOption {
  id: number
  label: string
  value: number
}

// 下拉框选项列表
const userList = ref<SelectOption[]>([])
const elderList = ref<SelectOption[]>([])
const planList = ref<SelectOption[]>([])

// 添加分类树相关的数据
const categoryTree = ref<any[]>([])
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

// 添加加载状态
const planLoading = ref(false)
const elderLoading = ref(false)
const categoryLoading = ref(false)
const userLoading = ref(false)

// 字典数据响应式计算属性
const taskStatusOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_STATUS)
})

const isRecurringOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_IS_RECURRING)
})

const priorityOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SERVICE_PLAN_TASK_PRIORITY)
})



// 添加远程搜索方法
const handlePlanSearch = async () => {
  planLoading.value = true
  try {
    const data = await ServicePlanApi.getServicePlanSimpleList()
    planList.value = data.map(item => ({ id: item.id, label: item.planName, value: item.id }))
  } catch (error) {
    console.error('获取护理计划列表失败:', error)
  } finally {
    planLoading.value = false
  }
}

const handleElderSearch = async () => {
  elderLoading.value = true
  try {
    // 直接调用 getElderList 以保持数据一致性
    await getElderList()
  } catch (error) {
    console.error('搜索老人列表失败:', error)
  } finally {
    elderLoading.value = false
  }
}

const handleCategorySearch = async () => {
  categoryLoading.value = true
  try {
    const data = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = data
  } catch (error) {
    console.error('获取任务分类树失败:', error)
  } finally {
    categoryLoading.value = false
  }
}

const handleUserSearch = async () => {
  userLoading.value = true
  try {
    const data = await getSimpleUserList()
    userList.value = data.map(item => ({ id: item.id, label: item.nickname, value: item.id }))
  } catch (error) {
    console.error('获取执行人列表失败:', error)
  } finally {
    userLoading.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 数据加载完成
    if (list.value.length > 0) {
      // 检查是否有老人ID但找不到老人信息的情况
      const elderIds = [...new Set(list.value.map(item => item.elderId).filter(Boolean))]
      const missingElders = elderIds.filter(id => !elderMap.value.has(Number(id)))

      // 如果老人数据缺失，尝试重新加载
      if (missingElders.length > 0 && elderMap.value.size === 0) {
        await getElderList()
      }
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 详情操作 */
const detailRef = ref()
const handleDetail = (row: any) => {
  detailRef.value.open(row)
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskApi.deleteTask(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TaskApi.exportTask(queryParams)
    download.excel(data, '服务任务.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}



/** 获取执行人列表 */
const getExecutorList = async () => {
  try {
    const data = await getSimpleUserList()
    userList.value = data.map(item => ({
      id: item.id,
      label: item.nickname,
      value: item.id
    }))
    data.forEach(item => {
      executorMap.value.set(Number(item.id), item.nickname)
    })
  } catch (error) {
    console.error('获取执行人列表失败:', error)
  }
}

/** 获取老人列表 */
const getElderList = async () => {
  try {
    const data = await ArchivesProfileApi.getArchivesProfileSimpleList()

    elderList.value = data.map(item => ({
      id: Number(item.id),
      label: item.name,
      value: Number(item.id)
    }))

    // 清空并重新填充 elderMap
    elderMap.value.clear()
    data.forEach(item => {
      elderMap.value.set(Number(item.id), item.name)
    })
  } catch (error) {
    console.error('获取老人列表失败:', error)
  }
}

/** 获取服务计划列表 */
const getPlanList = async () => {
  try {
    const data = await ServicePlanApi.getServicePlanSimpleList()
    planList.value = data.map(item => ({
      id: item.id,
      label: item.planName,
      value: item.id
    }))
    data.forEach(item => {
      planMap.value.set(item.id, item.planName)
    })
  } catch (error) {
    console.error('获取服务计划列表失败:', error)
  }
}

/** 获取分类树数据 */
const getCategoryTree = async () => {
  try {
    const treeData = await TaskCategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }
}



/** 格式化老人显示 */
const formatElder = (elderId: number | string) => {
  if (!elderId) return '-'

  const id = Number(elderId)
  if (isNaN(id)) return '-'

  // 直接从 elderMap 查找
  let name = elderMap.value.get(id)
  if (name) {
    return name
  }

  // 遍历 elderMap 进行强匹配（处理类型问题）
  for (const [key, value] of elderMap.value.entries()) {
    if (Number(key) === id) {
      elderMap.value.set(id, value)
      return value
    }
  }

  // 从 elderList 查找
  const elder = elderList.value.find(item => Number(item.value) === id)
  if (elder) {
    elderMap.value.set(id, elder.label)
    return elder.label
  }

  // 兜底查找（处理所有可能的类型问题）
  const foundInList = elderList.value.find(item =>
    item.value == elderId ||
    item.id == elderId ||
    String(item.value) === String(elderId) ||
    String(item.id) === String(elderId)
  )

  if (foundInList) {
    elderMap.value.set(id, foundInList.label)
    return foundInList.label
  }

  // 索引匹配（如果ID是小数字，可能是数组索引）
  if (id > 0 && id <= elderList.value.length) {
    const elderByIndex = elderList.value[id - 1]
    if (elderByIndex) {
      return elderByIndex.label
    }
  }

  return '-'
}



/** 格式化服务计划显示 */
const formatPlan = (planId: number) => {
  return planMap.value.get(planId) || planId
}



/** 初始化字典数据 */
const initDictData = async () => {
  if (!dictStore.getIsSetDict) {
    await dictStore.setDictMap()
  }

  // 检查关键字典数据是否存在，如果不存在则重新加载
  if (taskStatusOptions.value.length === 0 ||
      isRecurringOptions.value.length === 0 ||
      priorityOptions.value.length === 0) {
    console.warn('字典数据缺失，重新加载...')
    await dictStore.resetDict()
  }
}

/** 初始化 **/
onMounted(async () => {
  // 首先确保字典数据已加载
  await initDictData()

  // 然后加载所有下拉数据，确保elderMap等已填充
  await Promise.all([
    getExecutorList(),
    getElderList(),
    getPlanList(),
    getCategoryTree(),
  ])

  // 下拉数据加载完再查表格数据
  await getList()
})
</script>

<style scoped>
/* 添加树形选择器样式 */
:deep(.el-tree-select) {
  width: 240px !important;
}

/* 护理计划链接样式 */
.plan-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.plan-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 空值样式 */
.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}
</style>
