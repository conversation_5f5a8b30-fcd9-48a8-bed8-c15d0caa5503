<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入分类名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="danger" plain @click="toggleExpandAll">
          <Icon icon="ep:sort" class="mr-5px" /> 展开/折叠
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <!-- 提示栏 -->
    <el-alert
      title="该分类应用于服务任务管理，可根际具体情况客制化各类服务任务。"
      type="info"
      :closable="false"
      show-icon
      class="mb-4"
    />

    <el-table
      v-loading="loading"
      :data="list"
      row-key="id"
      :default-expand-all="isExpandAll"
      v-if="refreshTable"
    >
      <el-table-column prop="name" label="分类名称">
        <template #default="scope">
          <span style="margin-left: 10px;">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TaskCategoryForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { TaskCategoryApi } from '@/api/servicePlan/serviceplantaskcategory'
import TaskCategoryForm from './TaskCategoryForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

defineOptions({
  name: 'ServicePlanTaskCategory',
})

interface TaskCategoryVO {
  id?: number
  name: string
  description?: string
  status?: number
  children?: TaskCategoryVO[]
  parentId?: number | null
  ancestors?: string
  level?: number
  difyKey?: string | null
  createTime?: Date
}

const loading = ref(true)
const list = ref<TaskCategoryVO[]>([])
const queryFormRef = ref() // 搜索的表单
const formRef = ref() // 表单 Ref
const isExpandAll = ref(true) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态

// 查询参数
const queryParams = reactive({
  name: undefined
})



/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskCategoryApi.generateTaskCategoryTree()
    list.value = data
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await ElMessageBox.confirm('是否确认删除分类编号为"' + id + '"的数据项？')
    // 发起删除
    await TaskCategoryApi.deleteTaskCategory(id)
    ElMessage.success('删除成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
</style>